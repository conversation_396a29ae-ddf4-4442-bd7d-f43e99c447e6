import { PageLayout } from '@/components/layout/PageLayout'
import { PromptOptimizerClient } from './PromptOptimizerClient'
import type { Metadata } from 'next'

// Prompt Optimizer页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Prompt Optimizer - Perfect Prompts | HumanWhisper',
  description: 'Free AI prompt engineering tool. Transform vague ideas into clear, structured prompts. Get better results from any AI model.',
  keywords: [
    'AI prompt optimizer',
    'prompt engineering tool',
    'free prompt generator',
    'AI prompt builder',
    'prompt optimization'
  ],
  openGraph: {
    title: 'AI Prompt Optimizer - Perfect Prompts | HumanWhisper',
    description: 'Free AI prompt engineering tool. Transform vague ideas into clear, structured prompts. Get better results from any AI model.',
    url: '/prompt-optimizer',
    type: 'website',
  },
  twitter: {
    title: 'AI Prompt Optimizer - Perfect Prompts | HumanWhisper',
    description: 'Free AI prompt engineering tool. Transform vague ideas into clear, structured prompts. Get better results from any AI model.',
  },
  alternates: {
    canonical: '/prompt-optimizer',
  },
}

export default function PromptOptimizerPage() {
  return (
    <PageLayout>
      <PromptOptimizerClient />
    </PageLayout>
  )
}
