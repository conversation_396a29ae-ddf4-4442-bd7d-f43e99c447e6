import { NextRequest, NextResponse } from 'next/server'

// 智谱AI CogVideoX-Flash API配置
const ZHIPU_API_KEY = process.env.ZHIPU_API_KEY
const ZHIPU_BASE_URL = process.env.ZHIPU_BASE_URL || 'https://open.bigmodel.cn/api/paas/v4'

// 风格映射
const STYLE_MAP: Record<string, string> = {
  'natural': 'natural realistic motion, smooth transitions',
  'cinematic': 'cinematic dramatic effects, movie-like quality',
  'artistic': 'artistic stylized animation, creative visual effects',
  'dynamic': 'dynamic energetic motion, fast-paced movement',
  'gentle': 'gentle subtle movements, soft transitions',
  'dramatic': 'dramatic bold transitions, impactful visual effects'
}

/**
 * 视频生成API - POST请求处理
 */
export async function POST(request: NextRequest) {
  try {
    // 验证API配置
    if (!ZHIPU_API_KEY) {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 500 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const {
      image,
      prompt,
      style = 'natural'
    }: {
      image: string // base64编码的图片
      prompt: string
      style?: string
    } = body

    // 输入验证
    if (!image || typeof image !== 'string') {
      return NextResponse.json(
        { error: 'Image is required' },
        { status: 400 }
      )
    }

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Motion description is required' },
        { status: 400 }
      )
    }

    if (prompt.length > 500) {
      return NextResponse.json(
        { error: 'Motion description too long. Please keep it under 500 characters.' },
        { status: 400 }
      )
    }

    // 构建增强的提示词
    const styleDescription = STYLE_MAP[style] || STYLE_MAP.natural
    const enhancedPrompt = `${prompt}. ${styleDescription}. High quality video generation.`

    // 调用智谱AI CogVideoX-Flash API
    const response = await fetch(`${ZHIPU_BASE_URL}/videos/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ZHIPU_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'cogvideox-flash',
        prompt: enhancedPrompt,
        image_url: `data:image/jpeg;base64,${image}`
      }),
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`)
    }

    const data = await response.json()

    return NextResponse.json({
      id: data.id,
      task_status: data.task_status,
      request_id: data.request_id,
      metadata: {
        prompt: enhancedPrompt,
        style,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    let errorMessage = 'Failed to generate video. Please try again.'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'Service temporarily unavailable'
        statusCode = 500
      } else if (error.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.'
        statusCode = 429
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout. Please try again.'
        statusCode = 504
      } else if (error.message.includes('400')) {
        errorMessage = 'Invalid image or prompt. Please check your input.'
        statusCode = 400
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}

/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}
