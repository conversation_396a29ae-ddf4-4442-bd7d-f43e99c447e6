import Link from 'next/link'
import Image from 'next/image'

export function Footer() {
  return (
    <footer className="border-t bg-background py-12">
      <div className="container">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Image
                src="/logo-659x128.png"
                alt="HumanWhisper"
                width={120}
                height={23}
                className="h-6 w-auto"
              />
            </div>
            <p className="text-muted-foreground mb-4">
              AI that explains complex topics in simple, human-friendly language.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Product</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><Link href="/prompt-optimizer" className="hover:text-foreground transition-colors">Prompt Optimizer</Link></li>
              <li><Link href="/logo-generator" className="hover:text-foreground transition-colors">Logo Generator</Link></li>
              <li><Link href="/video-generator" className="hover:text-foreground transition-colors">Video Generator</Link></li>
              <li><Link href="/more-tools" className="hover:text-foreground transition-colors font-medium">More Tools</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li><Link href="/about" className="hover:text-foreground transition-colors">About Us</Link></li>
              <li><Link href="/contact" className="hover:text-foreground transition-colors">Contact Us</Link></li>
              <li><Link href="/privacy" className="hover:text-foreground transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-foreground transition-colors">Terms of Service</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; HumanWhisper. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
