'use client'

import React from 'react'
import { Eye, Database, Share } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { useSettingsStore } from '@/store/settings-store'

export function PrivacySettings() {
  const { 
    saveConversations,
    shareAnalytics,
    toggleSaveConversations,
    toggleShareAnalytics
  } = useSettingsStore()

  return (
    <div className="space-y-6">
      {/* Data Storage */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-blue-500" />
            <CardTitle>Data Storage</CardTitle>
          </div>
          <CardDescription>
            Control how your conversation data is stored
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="text-sm font-medium">Save Conversation History</div>
              <div className="text-xs text-muted-foreground">
                Store your conversation records locally for easy access next time
              </div>
            </div>
            <Switch
              checked={saveConversations}
              onCheckedChange={toggleSaveConversations}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Sharing */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Share className="h-5 w-5 text-green-500" />
            <CardTitle>Data Sharing</CardTitle>
          </div>
          <CardDescription>
            Help us improve the product experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="text-sm font-medium">Share Usage Statistics</div>
              <div className="text-xs text-muted-foreground">
                Anonymously share usage data to help improve product features
              </div>
            </div>
            <Switch
              checked={shareAnalytics}
              onCheckedChange={toggleShareAnalytics}
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Statement */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-purple-500" />
            <CardTitle>Privacy Commitment</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-muted-foreground">
            <p>• Your conversation content is only stored in your local browser</p>
            <p>• We do not collect or store your personal conversation content</p>
            <p>• Usage statistics are completely anonymous and contain no personal information</p>
            <p>• You can export or delete your data at any time</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
