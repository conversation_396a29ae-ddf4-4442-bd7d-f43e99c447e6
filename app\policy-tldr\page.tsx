import { PageLayout } from '@/components/layout/PageLayout'
import { PolicyTLDRClient } from './PolicyTLDRClient'
import type { Metadata } from 'next'

// Policy TL;DR页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Policy TL;DR - Simplify Legal Documents | HumanWhisper',
  description: 'Transform complex privacy policies and terms into simple summaries. Get key points, rights, and risks explained clearly. Free AI policy analyzer.',
  keywords: [
    'policy summary',
    'privacy policy',
    'terms of service',
    'legal document',
    'policy analyzer',
    'TL;DR generator',
    'legal summary'
  ],
  openGraph: {
    title: 'AI Policy TL;DR - Simplify Legal Documents | HumanWhisper',
    description: 'Transform complex privacy policies and terms into simple summaries. Get key points, rights, and risks explained clearly. Free AI policy analyzer.',
    url: '/policy-tldr',
    type: 'website',
  },
  twitter: {
    title: 'AI Policy TL;DR - Simplify Legal Documents | HumanWhisper',
    description: 'Transform complex privacy policies and terms into simple summaries. Get key points, rights, and risks explained clearly. Free AI policy analyzer.',
  },
  alternates: {
    canonical: '/policy-tldr',
  },
}

export default function PolicyTLDRPage() {
  return (
    <PageLayout>
      <PolicyTLDRClient />
    </PageLayout>
  )
}
