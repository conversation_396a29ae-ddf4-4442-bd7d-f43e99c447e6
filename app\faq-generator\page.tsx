import { PageLayout } from '@/components/layout/PageLayout'
import { FAQGeneratorClient } from './FAQGeneratorClient'
import type { Metadata } from 'next'

// FAQ Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI FAQ Generator - Create Professional FAQs | HumanWhisper',
  description: 'Generate comprehensive FAQ sections automatically. Input product info and user questions, get professional FAQ answers. Free AI FAQ generator.',
  keywords: [
    'FAQ generator',
    'FAQ creator',
    'customer support',
    'help documentation',
    'FAQ automation',
    'support content',
    'FAQ builder'
  ],
  openGraph: {
    title: 'AI FAQ Generator - Create Professional FAQs | HumanWhisper',
    description: 'Generate comprehensive FAQ sections automatically. Input product info and user questions, get professional FAQ answers. Free AI FAQ generator.',
    url: '/faq-generator',
    type: 'website',
  },
  twitter: {
    title: 'AI FAQ Generator - Create Professional FAQs | HumanWhisper',
    description: 'Generate comprehensive FAQ sections automatically. Input product info and user questions, get professional FAQ answers. Free AI FAQ generator.',
  },
  alternates: {
    canonical: '/faq-generator',
  },
}

export default function FAQGeneratorPage() {
  return (
    <PageLayout>
      <FAQGeneratorClient />
    </PageLayout>
  )
}
