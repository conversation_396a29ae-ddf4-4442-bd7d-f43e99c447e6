import { PageLayout } from '@/components/layout/PageLayout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Shield, Database, Lock, Eye, Trash2, Mail, FileText } from 'lucide-react'
import type { Metadata } from 'next'

// Privacy Policy页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Your Privacy and Data Protection - HumanWhisper',
  description: 'Read our privacy policy to understand how HumanWhisper collects, uses, and protects your data with transparency and care.',
  keywords: [
    'privacy policy',
    'data protection',
    'HumanWhisper privacy',
    'AI data use',
    'user privacy AI'
  ],
  openGraph: {
    title: 'Your Privacy and Data Protection - HumanWhisper',
    description: 'Read our privacy policy to understand how HumanWhisper collects, uses, and protects your data with transparency and care.',
    url: '/privacy',
    type: 'website',
  },
  twitter: {
    title: 'Your Privacy and Data Protection - HumanWhisper',
    description: 'Read our privacy policy to understand how HumanWhisper collects, uses, and protects your data with transparency and care.',
  },
  alternates: {
    canonical: '/privacy',
  },
}

export default function PrivacyPage() {
  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Your Privacy and Data Protection
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              At HumanWhisper, we believe <strong>privacy policy</strong> transparency is fundamental. 
              Learn how we protect your data with industry-leading <strong>data protection</strong> 
              measures and respect your <strong>user privacy</strong> at every step.
            </p>
          </div>
        </div>
      </section>

      {/* Privacy Overview */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Our Privacy Commitment
              </h2>
              <p className="text-lg text-muted-foreground">
                <strong>HumanWhisper privacy</strong> is built on a simple principle: your conversations 
                are yours alone. We've designed our system to protect your privacy by default, 
                ensuring your personal data remains secure and under your control.
              </p>
            </div>

            {/* Key Privacy Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <Card className="text-center">
                <CardContent className="pt-6">
                  <Database className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">Local Storage Only</h3>
                  <p className="text-sm text-muted-foreground">
                    Your chat history is stored only on your device, never on our servers
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="pt-6">
                  <Lock className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">End-to-End Security</h3>
                  <p className="text-sm text-muted-foreground">
                    All communications are encrypted and processed securely
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="pt-6">
                  <Trash2 className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">One-Click Deletion</h3>
                  <p className="text-sm text-muted-foreground">
                    Clear all your data instantly with a single click anytime
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Data Collection */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Data Collection and Storage</h2>
            
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Shield className="h-6 w-6 text-green-600" />
                  <CardTitle>What We DON'T Collect</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>We do <strong>NOT</strong> store your chat conversations on our servers</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>We do <strong>NOT</strong> access or read your personal conversations</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>We do <strong>NOT</strong> sell or share your personal data with third parties</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>We do <strong>NOT</strong> use your conversations for training our AI models</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Eye className="h-6 w-6 text-blue-600" />
                  <CardTitle>What We Do Collect</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  We only collect minimal data necessary for <strong>AI data use</strong> and service functionality:
                </p>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong>Account Information:</strong> Email address and basic profile data (via Clerk authentication)
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong>Usage Statistics:</strong> Credit consumption and feature usage (anonymized)
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="h-2 w-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <strong>Technical Data:</strong> Browser type, device information for optimization purposes
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Data Use and Protection */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">How We Use and Protect Your Data</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5 text-primary" />
                    Data Protection Measures
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <p>We implement industry-standard security measures to protect your data:</p>
                    <ul className="space-y-2 ml-4">
                      <li>• SSL/TLS encryption for all data transmission</li>
                      <li>• Secure authentication via Clerk</li>
                      <li>• Regular security audits and updates</li>
                      <li>• Limited access controls for our team</li>
                      <li>• Compliance with data protection regulations</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5 text-primary" />
                    Local Data Storage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <p>Your chat history is stored locally on your device:</p>
                    <ul className="space-y-2 ml-4">
                      <li>• Conversations saved in your browser's local storage</li>
                      <li>• No server-side chat history backup</li>
                      <li>• Data remains on your device only</li>
                      <li>• You have complete control over deletion</li>
                      <li>• No cross-device synchronization</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* User Rights */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Your Data Control Rights</h2>
            
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Complete Data Control</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-6">
                  As part of our commitment to <strong>user privacy AI</strong> principles, you have full control over your data:
                </p>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Trash2 className="h-5 w-5 text-red-500 mt-1" />
                      <div>
                        <h4 className="font-semibold">One-Click Data Deletion</h4>
                        <p className="text-sm text-muted-foreground">
                          Clear all your chat history instantly with a single click in the chat interface
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <Eye className="h-5 w-5 text-blue-500 mt-1" />
                      <div>
                        <h4 className="font-semibold">Data Transparency</h4>
                        <p className="text-sm text-muted-foreground">
                          View exactly what data we have about you through your dashboard
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Mail className="h-5 w-5 text-green-500 mt-1" />
                      <div>
                        <h4 className="font-semibold">Account Deletion</h4>
                        <p className="text-sm text-muted-foreground">
                          Request complete account deletion by contacting our support team
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <FileText className="h-5 w-5 text-purple-500 mt-1" />
                      <div>
                        <h4 className="font-semibold">Data Export</h4>
                        <p className="text-sm text-muted-foreground">
                          Export your account data and usage history upon request
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Legal and Contact */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Legal Compliance</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Our <strong>privacy policy</strong> complies with major data protection regulations:
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li>• General Data Protection Regulation (GDPR)</li>
                    <li>• California Consumer Privacy Act (CCPA)</li>
                    <li>• Children's Online Privacy Protection Act (COPPA)</li>
                    <li>• Other applicable local privacy laws</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Privacy Questions?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Have questions about our <strong>data protection</strong> practices?
                  </p>
                  <div>
                    <a href="mailto:<EMAIL>">
                      <Button className="w-full">
                        <Mail className="h-4 w-4 mr-2" />
                        <EMAIL>
                      </Button>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-8 p-6 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground text-center">
                <strong>Last Updated:</strong> July 21, 2025
                <br />
                We may update this privacy policy from time to time. We'll notify users of any significant changes.
              </p>
            </div>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
