import type {
  Message,
  ChatSettings,
  AIModel,
  Conversation
} from '@/types'
import { AI_MODELS } from './constants'

/**
 * 计算消息的token数量（估算）
 * @param content - 消息内容
 * @returns number
 */
export function estimateTokenCount(content: string): number {
  // 简单的token估算：中文字符按2个token计算，英文单词按1.3个token计算
  const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length
  const englishWords = content.split(/\s+/).filter(word => 
    /^[a-zA-Z]+$/.test(word)
  ).length
  const otherChars = content.length - chineseChars
  
  return Math.ceil(chineseChars * 2 + englishWords * 1.3 + otherChars * 0.5)
}

/**
 * 计算对话的总token数
 * @param messages - 消息列表
 * @returns number
 */
export function calculateConversationTokens(messages: Message[]): number {
  return messages.reduce((total, message) => {
    return total + estimateTokenCount(message.content)
  }, 0)
}

/**
 * 估算API调用成本
 * @param tokenCount - token数量
 * @param model - AI模型
 * @returns number
 */
export function estimateCost(tokenCount: number, model: AIModel): number {
  const costPer1k: Record<AIModel, number> = {
    [AI_MODELS.GPT_4_1_NANO]: 0.001,
    [AI_MODELS.CLAUDE_3_5_HAIKU]: 0.002,
    [AI_MODELS.GEMINI_2_0_FLASH]: 0.0015,
  }
  
  return (tokenCount / 1000) * (costPer1k[model] || 0.001)
}

/**
 * 检查消息是否过长
 * @param content - 消息内容
 * @param model - AI模型
 * @returns boolean
 */
export function isMessageTooLong(content: string, model: AIModel): boolean {
  const tokenCount = estimateTokenCount(content)
  const maxTokens: Record<AIModel, number> = {
    [AI_MODELS.GPT_4_1_NANO]: 3000, // 留一些余量给响应
    [AI_MODELS.CLAUDE_3_5_HAIKU]: 6000,
    [AI_MODELS.GEMINI_2_0_FLASH]: 6000,
  }
  
  return tokenCount > (maxTokens[model] || 3000)
}

/**
 * 截断过长的对话历史
 * @param messages - 消息列表
 * @param model - AI模型
 * @param maxTokens - 最大token数
 * @returns Message[]
 */
export function truncateConversation(
  messages: Message[], 
  model: AIModel, 
  maxTokens?: number
): Message[] {
  const limit = maxTokens || getModelContextLimit(model)
  let totalTokens = 0
  const truncatedMessages: Message[] = []
  
  // 从最新消息开始，向前保留消息直到达到token限制
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    if (!message) continue
    const messageTokens = estimateTokenCount(message.content)
    
    if (totalTokens + messageTokens > limit) {
      break
    }
    
    totalTokens += messageTokens
    truncatedMessages.unshift(message)
  }
  
  return truncatedMessages
}

/**
 * 获取模型的上下文限制
 * @param model - AI模型
 * @returns number
 */
function getModelContextLimit(model: AIModel): number {
  const limits: Record<AIModel, number> = {
    [AI_MODELS.GPT_4_1_NANO]: 6000,
    [AI_MODELS.CLAUDE_3_5_HAIKU]: 150000,
    [AI_MODELS.GEMINI_2_0_FLASH]: 800000,
  }
  
  return limits[model] || 6000
}

/**
 * 格式化消息用于API调用
 * @param messages - 消息列表
 * @returns Array<{role: MessageRole, content: string}>
 */
export function formatMessagesForAPI(messages: Message[]) {
  return messages.map(message => ({
    role: message.role,
    content: message.content,
  }))
}

/**
 * 验证消息格式
 * @param message - 消息对象
 * @returns boolean
 */
export function validateMessage(message: Partial<Message>): boolean {
  return !!(
    message.content &&
    message.role &&
    ['user', 'assistant', 'system'].includes(message.role)
  )
}

/**
 * 清理和标准化消息内容
 * @param content - 原始内容
 * @returns string
 */
export function sanitizeMessageContent(content: string): string {
  return content
    .trim()
    .replace(/\s+/g, ' ') // 合并多个空格
    .replace(/\n{3,}/g, '\n\n') // 限制连续换行
    .slice(0, 10000) // 限制最大长度
}

/**
 * 检测消息语言
 * @param content - 消息内容
 * @returns string
 */
export function detectLanguage(content: string): string {
  const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length
  const totalChars = content.length
  
  if (chineseChars / totalChars > 0.3) {
    return 'zh'
  }
  
  // 简单的其他语言检测
  if (/[а-яё]/i.test(content)) return 'ru'
  if (/[あ-んア-ン]/i.test(content)) return 'ja'
  if (/[가-힣]/i.test(content)) return 'ko'
  if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/i.test(content)) return 'fr'
  if (/[äöüß]/i.test(content)) return 'de'
  if (/[áéíóúüñ]/i.test(content)) return 'es'
  
  return 'en'
}

/**
 * 生成对话标题
 * @param messages - 消息列表
 * @param maxLength - 最大长度
 * @returns string
 */
export function generateConversationTitle(
  messages: Message[], 
  maxLength: number = 50
): string {
  const firstUserMessage = messages.find(m => m.role === 'user')
  
  if (!firstUserMessage) {
    return 'New Chat'
  }
  
  let title = firstUserMessage.content
    .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 移除特殊字符
    .trim()
  
  if (title.length > maxLength) {
    title = title.slice(0, maxLength - 3) + '...'
  }
  
  return title || 'New Chat'
}

/**
 * 计算对话统计信息
 * @param conversation - 对话对象
 * @returns object
 */
export function calculateConversationStats(conversation: Conversation) {
  const messages = conversation.messages
  const userMessages = messages.filter(m => m.role === 'user')
  const assistantMessages = messages.filter(m => m.role === 'assistant')
  
  const totalTokens = calculateConversationTokens(messages)
  const totalCost = messages.reduce((cost, message) => {
    if (message.model) {
      return cost + estimateCost(estimateTokenCount(message.content), message.model)
    }
    return cost
  }, 0)
  
  const averageResponseTime = assistantMessages.reduce((total, message) => {
    return total + (message.metadata?.processingTime || 0)
  }, 0) / assistantMessages.length || 0
  
  return {
    totalMessages: messages.length,
    userMessages: userMessages.length,
    assistantMessages: assistantMessages.length,
    totalTokens,
    totalCost,
    averageResponseTime,
    duration: new Date(conversation.updatedAt).getTime() - 
              new Date(conversation.createdAt).getTime(),
  }
}

/**
 * 检查是否需要切换模型
 * @param settings - 当前设置
 * @param conversationStats - 对话统计
 * @returns object
 */
export function shouldSwitchModel(
  settings: ChatSettings,
  conversationStats: ReturnType<typeof calculateConversationStats>
) {
  const { model, responseLength } = settings
  const { averageResponseTime, totalCost } = conversationStats
  
  const suggestions: string[] = []
  let recommendedModel: AIModel | null = null
  
  // 如果响应时间过长，建议切换到更快的模型
  if (averageResponseTime > 10000) { // 10秒
    if (model !== AI_MODELS.GPT_4_1_NANO) {
      suggestions.push('响应时间较长，建议切换到更快的模型')
      recommendedModel = AI_MODELS.GPT_4_1_NANO
    }
  }
  
  // 如果成本过高，建议切换到更便宜的模型
  if (totalCost > 0.1) { // 0.1美元
    if (model !== AI_MODELS.GPT_4_1_NANO) {
      suggestions.push('成本较高，建议切换到更经济的模型')
      recommendedModel = AI_MODELS.GPT_4_1_NANO
    }
  }
  
  // 如果需要详细回答，建议切换到更强的模型
  if (responseLength === 'THOROUGH' && model === AI_MODELS.GPT_4_1_NANO) {
    suggestions.push('需要详细回答，建议切换到更强的模型')
    recommendedModel = AI_MODELS.CLAUDE_3_5_HAIKU
  }
  
  return {
    shouldSwitch: suggestions.length > 0,
    suggestions,
    recommendedModel,
  }
}
