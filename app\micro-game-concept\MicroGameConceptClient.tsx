'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Gamepad2, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Target, Trophy, Puzzle, Lightbulb } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 游戏主题选项
const GAME_THEMES = [
  { id: 'environmental', name: 'Environmental', description: 'Climate change, pollution, conservation, sustainability' },
  { id: 'time_loop', name: 'Time Loop', description: 'Repeating cycles, temporal mechanics, cause and effect' },
  { id: 'social_anxiety', name: 'Social Anxiety', description: 'Introversion, social situations, communication challenges' },
  { id: 'minimalism', name: 'Minimalism', description: 'Simple mechanics, clean aesthetics, essential elements' },
  { id: 'cooperation', name: 'Cooperation', description: 'Teamwork, collaboration, mutual assistance' },
  { id: 'memory', name: 'Memory', description: 'Remembering, forgetting, nostalgia, cognitive challenges' },
  { id: 'growth', name: 'Growth', description: 'Evolution, progression, learning, development' },
  { id: 'isolation', name: 'Isolation', description: 'Loneliness, solitude, connection, distance' },
  { id: 'creativity', name: 'Creativity', description: 'Art, expression, imagination, innovation' },
  { id: 'survival', name: 'Survival', description: 'Resource management, adaptation, perseverance' },
  { id: 'mystery', name: 'Mystery', description: 'Investigation, secrets, discovery, puzzles' },
  { id: 'custom', name: 'Custom Theme', description: 'Define your own unique game theme' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Logical reasoning, game mechanics, systematic thinking'
  },
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, complex functions, concept innovation'
  }
]

interface GeneratedGameConcept {
  gameplayOverview: string
  victoryConditions: string
  dlcHooks: string[]
  wordCount: number
  coreLoop: string
  targetAudience: string
}

export function MicroGameConceptClient() {
  // 表单状态
  const [gameTheme, setGameTheme] = useState('')
  const [customTheme, setCustomTheme] = useState('')
  const [coreVerbs, setCoreVerbs] = useState('')
  const [artKeyword, setArtKeyword] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedConcept, setGeneratedConcept] = useState<GeneratedGameConcept | null>(null)
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成游戏概念
  const handleGenerate = async () => {
    if (!gameTheme || !coreVerbs.trim() || !artKeyword.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/micro-game-concept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gameTheme: gameTheme === 'custom' ? customTheme : gameTheme,
          coreVerbs: coreVerbs.trim(),
          artKeyword: artKeyword.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate game concept')
      }

      const data = await response.json()
      setGeneratedConcept(data.gameConcept)

      toast({
        title: "Game Concept Generated!",
        description: "Your micro-game concept card is ready for Game Jam.",
      })

    } catch (error) {
      setError('Failed to generate game concept. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制概念到剪贴板
  const copyToClipboard = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard.`,
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  // 复制完整概念
  const copyFullConcept = async () => {
    if (!generatedConcept) return
    
    const fullConcept = `GAME CONCEPT CARD

GAMEPLAY OVERVIEW:
${generatedConcept.gameplayOverview}

VICTORY CONDITIONS:
${generatedConcept.victoryConditions}

DLC EXPANSION HOOKS:
${generatedConcept.dlcHooks.map((hook, index) => `${index + 1}. ${hook}`).join('\n')}

CORE LOOP: ${generatedConcept.coreLoop}
TARGET AUDIENCE: ${generatedConcept.targetAudience}
WORD COUNT: ${generatedConcept.wordCount}`

    await copyToClipboard(fullConcept, 'Full Game Concept')
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950 dark:to-indigo-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                AI Micro-Game Concept
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Game Jam Ideas
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>complete micro-game concepts for Game Jams</strong>. 
              Create gameplay mechanics, victory conditions, and DLC hooks in 150 words.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                150 Words
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Game Jam Ready
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Complete Concept
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Gamepad2 className="h-5 w-5 text-primary" />
                      Game Concept Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Game Theme */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Game Theme *</label>
                      <Select value={gameTheme} onValueChange={setGameTheme}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your game theme" />
                        </SelectTrigger>
                        <SelectContent>
                          {GAME_THEMES.map((theme) => (
                            <SelectItem key={theme.id} value={theme.id}>
                              <div>
                                <div className="font-medium">{theme.name}</div>
                                <div className="text-xs text-muted-foreground">{theme.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Theme Input */}
                    {gameTheme === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Theme *</label>
                        <input
                          type="text"
                          placeholder="Describe your unique game theme"
                          value={customTheme}
                          onChange={(e) => setCustomTheme(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Core Verbs */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Core Action Verbs *</label>
                      <input
                        type="text"
                        placeholder="e.g., collect, build, dodge, solve"
                        value={coreVerbs}
                        onChange={(e) => setCoreVerbs(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Two main actions players will perform (separated by comma)
                      </p>
                    </div>

                    {/* Art Keyword */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Art Style Keyword *</label>
                      <input
                        type="text"
                        placeholder="e.g., pixel art, minimalist, hand-drawn, neon"
                        value={artKeyword}
                        onChange={(e) => setArtKeyword(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Visual style or aesthetic direction for the game
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Concept...
                        </>
                      ) : (
                        <>
                          <Gamepad2 className="h-4 w-4 mr-2" />
                          Generate Game Concept
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Game Concept Card
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedConcept ? (
                      <div className="space-y-6">
                        {/* Gameplay Overview */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Puzzle className="h-4 w-4" />
                              Gameplay Overview
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedConcept.gameplayOverview, 'Gameplay Overview')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg">
                            <p className="text-sm leading-relaxed">{generatedConcept.gameplayOverview}</p>
                          </div>
                        </div>

                        {/* Victory Conditions */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Trophy className="h-4 w-4" />
                              Victory Conditions
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedConcept.victoryConditions, 'Victory Conditions')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                            <p className="text-sm leading-relaxed">{generatedConcept.victoryConditions}</p>
                          </div>
                        </div>

                        {/* DLC Hooks */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Lightbulb className="h-4 w-4" />
                              DLC Expansion Hooks
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedConcept.dlcHooks.join('\n'), 'DLC Hooks')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-2">
                            {generatedConcept.dlcHooks.map((hook, index) => (
                              <div key={index} className="flex items-start gap-2">
                                <span className="text-xs font-medium text-blue-600 mt-0.5">{index + 1}.</span>
                                <p className="text-sm leading-relaxed">{hook}</p>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Game Details */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-muted/50 p-3 rounded-lg">
                            <div className="text-xs font-medium text-muted-foreground mb-1">Core Loop</div>
                            <div className="text-sm">{generatedConcept.coreLoop}</div>
                          </div>
                          <div className="bg-muted/50 p-3 rounded-lg">
                            <div className="text-xs font-medium text-muted-foreground mb-1">Target Audience</div>
                            <div className="text-sm">{generatedConcept.targetAudience}</div>
                          </div>
                        </div>

                        {/* Word Count & Copy All */}
                        <div className="flex items-center justify-between pt-4 border-t">
                          <Badge variant="outline" className="text-xs">
                            {generatedConcept.wordCount} words
                          </Badge>
                          <Button onClick={copyFullConcept} variant="default">
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Full Concept Card
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Gamepad2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your game concept card will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Use AI for Game Concept Design?
            </h2>
            <p className="text-xl text-muted-foreground">
              Break through creative blocks and generate innovative game ideas in minutes
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Creative Breakthrough</h3>
                <p className="text-sm text-muted-foreground">
                  Overcome designer's block with fresh perspectives and unexpected combinations
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Rapid Prototyping</h3>
                <p className="text-sm text-muted-foreground">
                  Generate complete concepts in minutes, perfect for Game Jam time constraints
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Scope-Appropriate</h3>
                <p className="text-sm text-muted-foreground">
                  Concepts designed for 48-72 hour development cycles with realistic scope
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Puzzle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Systematic Design</h3>
                <p className="text-sm text-muted-foreground">
                  Logical game mechanics with clear core loops and victory conditions
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Trophy className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Expansion Ready</h3>
                <p className="text-sm text-muted-foreground">
                  Built-in DLC hooks for post-jam development and monetization
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Complete Package</h3>
                <p className="text-sm text-muted-foreground">
                  Gameplay, victory conditions, and expansion ideas in one cohesive concept
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              From theme to complete game concept in 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Theme & Actions</h3>
              <p className="text-sm text-muted-foreground">
                Select a game theme and define two core action verbs that will drive gameplay
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-indigo-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Define Art Style</h3>
              <p className="text-sm text-muted-foreground">
                Specify the visual aesthetic that will influence both design and gameplay feel
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Get Complete Concept</h3>
              <p className="text-sm text-muted-foreground">
                Receive gameplay overview, victory conditions, and DLC expansion hooks
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Game Concept Examples
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different themes and mechanics create unique game experiences
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🌱</span>
                  Environmental Theme
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-green-600">Core Actions:</span>
                    <p className="text-sm">Plant, Nurture</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Art Style:</span>
                    <p className="text-sm">Hand-drawn watercolor</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Concept:</span>
                    <p className="text-sm">"Ecosystem Painter" - Players plant seeds and nurture growth while balancing environmental factors. Victory comes from creating sustainable ecosystems that thrive independently.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">⏰</span>
                  Time Loop Theme
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-blue-600">Core Actions:</span>
                    <p className="text-sm">Rewind, Predict</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-600">Art Style:</span>
                    <p className="text-sm">Pixel art</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-600">Concept:</span>
                    <p className="text-sm">"Temporal Detective" - Players rewind time to gather clues and predict future events. Each loop reveals new information to solve an ever-changing mystery.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">😰</span>
                  Social Anxiety Theme
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-purple-600">Core Actions:</span>
                    <p className="text-sm">Hide, Connect</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-purple-600">Art Style:</span>
                    <p className="text-sm">Minimalist</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-purple-600">Concept:</span>
                    <p className="text-sm">"Social Shadows" - Players navigate social situations by hiding in shadows and making strategic connections. Victory requires building confidence through small, meaningful interactions.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🎨</span>
                  Creativity Theme
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-orange-600">Core Actions:</span>
                    <p className="text-sm">Sketch, Inspire</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-orange-600">Art Style:</span>
                    <p className="text-sm">Neon cyberpunk</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-orange-600">Concept:</span>
                    <p className="text-sm">"Idea Architect" - Players sketch concepts in a digital world and inspire NPCs to bring them to life. Victory comes from creating viral ideas that transform the virtual society.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about AI game concept generation
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How detailed are the generated game concepts?",
                  answer: "Each concept includes a comprehensive gameplay overview, clear victory conditions, three DLC expansion hooks, core gameplay loop description, and target audience analysis - everything needed to start development immediately."
                },
                {
                  question: "Are these concepts suitable for Game Jams?",
                  answer: "Absolutely! All concepts are designed with 48-72 hour development cycles in mind. They balance innovation with realistic scope, ensuring you can create a playable prototype within Game Jam timeframes."
                },
                {
                  question: "Can I modify the generated concepts?",
                  answer: "Yes! The generated concepts are starting points for your creativity. Feel free to adapt mechanics, change themes, or combine elements from multiple concepts to create something uniquely yours."
                },
                {
                  question: "How do the core verbs influence the game design?",
                  answer: "The two core verbs become the foundation of your gameplay loop. Every mechanic, interaction, and system in the generated concept will relate back to these fundamental player actions, ensuring cohesive design."
                },
                {
                  question: "What makes these concepts innovative?",
                  answer: "The AI combines familiar game elements in unexpected ways, considers modern themes and social issues, and creates unique mechanics that haven't been explored extensively in existing games."
                },
                {
                  question: "How do DLC hooks help with post-jam development?",
                  answer: "DLC hooks provide clear directions for expanding your Game Jam prototype into a full game. They suggest monetization opportunities, additional content, and ways to extend player engagement beyond the initial concept."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
