// HumanWhisper Type Definitions

// Basic types
export type ID = string
export type Timestamp = number | Date

// AI model related types
export type AIModel = 'gpt-4.1-nano' | 'claude-3-5-haiku-latest' | 'gemini-2.0-flash'

export interface ModelInfo {
  id: AIModel
  name: string
  provider: string
  description: string
  maxTokens: number
  costPer1kTokens: number
}

// Message related types
export type MessageRole = 'user' | 'assistant' | 'system'

export interface Message {
  id: ID
  role: MessageRole
  content: string
  timestamp: Timestamp
  model: AIModel
  settings?: ChatSettings
  metadata?: MessageMetadata
  images?: string[]  // 新增：支持图像数据（base64格式）
}

// 聊天会话接口
export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  model: AIModel
  messageCount: number
  previewText?: string  // 预览文本（第一条用户消息的摘要）
}

// 聊天历史状态接口
export interface ChatHistoryState {
  sessions: ChatSession[]
  currentSessionId: string | null
  isLoading: boolean
}

// 时间分组类型
export type TimeGroup = 'today' | 'yesterday' | 'thisWeek' | 'lastWeek' | 'thisMonth' | 'older'

// 分组后的会话
export type GroupedSessions = {
  [key in TimeGroup]?: ChatSession[]
}

export interface MessageMetadata {
  tokenCount?: number
  processingTime?: number
  cost?: number
  error?: string
}

// 对话相关类型
export interface Conversation {
  id: ID
  title: string
  messages: Message[]
  createdAt: Timestamp
  updatedAt: Timestamp
  settings: ChatSettings
  metadata?: ConversationMetadata
}

export interface ConversationMetadata {
  totalTokens?: number
  totalCost?: number
  messageCount: number
  averageResponseTime?: number
}

// 聊天设置类型
export type ResponseLength = 'BRIEF' | 'STANDARD' | 'COMPREHENSIVE' | 'THOROUGH'
export type Language = 'zh' | 'en' | 'ja' | 'ko' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru'
export type Theme = 'light' | 'dark' | 'system'

export interface ChatSettings {
  model: AIModel
  language: Language
  responseLength: ResponseLength
  targetAge: number
  includeExamples: boolean
  useAnalogies: boolean
  showThinking: boolean
}

// 用户设置类型
export interface UserSettings extends ChatSettings {
  theme: Theme
  fontSize: 'small' | 'medium' | 'large'
  autoSave: boolean
  notifications: boolean
}

// API相关类型
export interface APIRequest {
  model: AIModel
  messages: Array<{
    role: MessageRole
    content: string
  }>
  settings: ChatSettings
  stream?: boolean
  complexityScore?: number // 新增：问题复杂度评分
}

export interface APIResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: MessageRole
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export interface StreamResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    delta: {
      role?: MessageRole
      content?: string
    }
    finish_reason?: string
  }>
}

// 错误类型
export interface APIError {
  error: {
    message: string
    type: string
    param?: string
    code?: string
  }
}

export type ErrorType = 
  | 'network_error'
  | 'api_error'
  | 'rate_limit_error'
  | 'invalid_input'
  | 'unknown_error'

export interface AppError {
  type: ErrorType
  message: string
  details?: string
  timestamp: Timestamp
}

// 状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface AsyncState<T> {
  data: T | null
  loading: boolean
  error: AppError | null
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  error?: string
}

// 存储类型
export interface StorageData {
  conversations: Conversation[]
  settings: UserSettings
  theme: Theme
  language: Language
}

// 分析和统计类型
export interface Analytics {
  totalConversations: number
  totalMessages: number
  totalTokensUsed: number
  totalCost: number
  averageResponseTime: number
  mostUsedModel: AIModel
  preferredLanguage: Language
  dailyUsage: Array<{
    date: string
    conversations: number
    messages: number
    tokens: number
  }>
}

// 导出和导入类型
export interface ExportData {
  version: string
  exportDate: Timestamp
  conversations: Conversation[]
  settings: UserSettings
}

export interface ImportResult {
  success: boolean
  conversationsImported: number
  settingsImported: boolean
  errors: string[]
}

// 搜索和过滤类型
export interface SearchFilters {
  query?: string
  model?: AIModel
  dateRange?: {
    start: Date
    end: Date
  }
  language?: Language
}

export interface SearchResult {
  conversations: Conversation[]
  messages: Message[]
  totalCount: number
}

// 键盘快捷键类型
export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  metaKey?: boolean
  description: string
  action: () => void
}

// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: ID
  type: NotificationType
  title: string
  message?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 响应式断点类型
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl'

// 工具函数类型
export type DebounceFunction<T extends (...args: any[]) => any> = (
  ...args: Parameters<T>
) => void

export type ThrottleFunction<T extends (...args: any[]) => any> = (
  ...args: Parameters<T>
) => void
