/**
 * 时间工具函数
 * 用于在AI对话中提供准确的时间上下文
 */

/**
 * 获取当前日期的格式化字符串
 * @returns 格式化的日期字符串，如 "Monday, July 21, 2025"
 */
export function getCurrentDateString(): string {
  const now = new Date()
  return now.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  })
}

/**
 * 获取当前年份
 * @returns 当前年份数字
 */
export function getCurrentYear(): number {
  return new Date().getFullYear()
}

/**
 * 获取当前月份名称
 * @returns 当前月份的英文名称
 */
export function getCurrentMonthName(): string {
  const now = new Date()
  return now.toLocaleDateString('en-US', { month: 'long' })
}

/**
 * 获取当前是星期几
 * @returns 星期几的英文名称
 */
export function getCurrentDayName(): string {
  const now = new Date()
  return now.toLocaleDateString('en-US', { weekday: 'long' })
}

/**
 * 获取相对时间描述
 * @param targetDate 目标日期
 * @returns 相对时间描述，如 "today", "yesterday", "2 days ago"
 */
export function getRelativeTimeDescription(targetDate: Date): string {
  const now = new Date()
  const diffTime = now.getTime() - targetDate.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return 'today'
  } else if (diffDays === 1) {
    return 'yesterday'
  } else if (diffDays === -1) {
    return 'tomorrow'
  } else if (diffDays > 1) {
    return `${diffDays} days ago`
  } else {
    return `in ${Math.abs(diffDays)} days`
  }
}

/**
 * 检查是否是时间相关的问题
 * @param question 用户问题
 * @returns 是否是时间相关问题
 */
export function isTimeRelatedQuestion(question: string): boolean {
  const timeKeywords = [
    'time', 'date', 'today', 'now', 'current', 'when',
    'year', 'month', 'day', 'week', 'yesterday', 'tomorrow',
    '时间', '日期', '今天', '现在', '当前', '什么时候',
    '年', '月', '日', '星期', '昨天', '明天'
  ]
  
  const lowerQuestion = question.toLowerCase()
  return timeKeywords.some(keyword => lowerQuestion.includes(keyword))
}

/**
 * 为AI提示词生成时间上下文
 * @returns 时间上下文字符串
 */
export function generateTimeContext(): string {
  const currentDate = getCurrentDateString()
  const currentYear = getCurrentYear()
  
  return `### Current Time Context:
Today is ${currentDate} (${currentYear}). When users ask about current time, dates, or "now", refer to this information. Always provide accurate, current information rather than outdated training data from your training period.

Important: Your training data may be from earlier years, but the current year is ${currentYear}. Always use this current date information when discussing recent events, current affairs, or anything time-sensitive.`
}

/**
 * 为用户消息添加时间戳
 * @param message 用户消息
 * @returns 带时间戳的消息
 */
export function addTimestampToUserMessage(message: string): string {
  const currentDate = getCurrentDateString()
  return `[Asked on ${currentDate}] ${message}`
}
