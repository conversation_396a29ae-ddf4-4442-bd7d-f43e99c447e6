import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 设计场景映射
const SCENARIO_MAP: Record<string, string> = {
  'brand_identity': 'brand identity design including logos, business cards, and brand guidelines',
  'website': 'website design and digital user interfaces',
  'mobile_app': 'mobile application interfaces for iOS and Android',
  'poster': 'poster design and print advertising materials',
  'packaging': 'product packaging and retail display design',
  'interior': 'interior design and architectural color schemes',
  'presentation': 'business presentations and slide decks',
  'social_media': 'social media graphics and digital marketing materials'
}

// 构建配色故事生成prompt
function buildColorStoryPrompt(
  scenario: string,
  colors: Array<{hex: string, name: string}>,
  emotions: string
): string {
  const scenarioDesc = SCENARIO_MAP[scenario] || scenario
  const colorList = colors.map(color => `${color.name}: ${color.hex}`).join(', ')

  const prompt = `You are a professional design consultant and color psychology expert. Your task is to create a compelling, professional explanation for a color palette that designers can present to their clients.

**Design Context:**
- Project Type: ${scenarioDesc}
- Color Palette: ${colorList}
- Target Emotions: ${emotions}

**Instructions:**
Create a comprehensive design explanation that includes:

1. **Color Psychology Overview**: Explain what each color psychologically represents and why it was chosen
2. **Emotional Impact**: How the palette achieves the target emotions (${emotions})
3. **Visual Harmony**: Why these colors work well together from a design theory perspective
4. **Usage Scenarios**: Specific recommendations for how to use each color in the design context
5. **Contrast & Accessibility**: Brief notes on readability and visual hierarchy
6. **Brand Personality**: What personality traits this palette communicates

**Format Requirements:**
- Write in professional, client-friendly language
- Use clear, confident explanations that justify design decisions
- Include specific color names and hex codes when referencing colors
- Structure with clear headings and bullet points for easy reading
- Keep explanations practical and actionable
- Aim for 300-500 words total
- Write in English only

**Tone:**
- Professional but approachable
- Confident and authoritative
- Educational without being condescending
- Focused on business value and user experience

Generate the color palette story now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      scenario,
      colors,
      emotions,
      model = 'THUDM/GLM-4.1V-9B-Thinking'
    } = body

    // 输入验证
    if (!scenario || !Array.isArray(colors) || colors.length === 0 || !emotions?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 验证颜色格式
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    const validColors = colors.filter(color => 
      color.hex && color.name && hexRegex.test(color.hex)
    )

    if (validColors.length === 0) {
      return NextResponse.json(
        { error: 'No valid colors provided' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildColorStoryPrompt(
      scenario,
      validColors,
      emotions.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/GLM-4.1V-9B-Thinking': {
        temperature: 0.95,
        max_tokens: 8192,
        top_p: 0.7,
        repetition_penalty: 1.1
      },
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/GLM-4.1V-9B-Thinking']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的故事
    const story = response.choices[0]?.message?.content?.trim()

    if (!story) {
      throw new Error('No story generated')
    }

    // 清理故事内容 - 移除Markdown格式符号
    const cleanStory = story
      .replace(/```markdown\n?/g, '')
      .replace(/```\n?/g, '')
      // 移除Markdown标题符号
      .replace(/^#{1,6}\s+/gm, '')
      // 移除粗体和斜体符号
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      // 移除列表符号，保留内容
      .replace(/^\s*[\*\-\+]\s+/gm, '• ')
      .replace(/^\s*\d+\.\s+/gm, '')
      // 移除代码块符号
      .replace(/`([^`]+)`/g, '$1')
      // 移除链接格式，保留文本
      .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
      // 清理多余空行
      .replace(/\n{3,}/g, '\n\n')
      .trim()

    return NextResponse.json({
      story: cleanStory,
      metadata: {
        scenario,
        colorCount: validColors.length,
        emotions,
        model,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate color story' },
      { status: 500 }
    )
  }
}
