'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Settings, RotateCcw, Download, Menu, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'

import { MessageList } from './MessageList'
import { ChatInput } from './ChatInput'
import { ChatSidebar } from './ChatSidebar'
import { ChatSettings } from './ChatSettings'


import { generateId, exportChatToFormat, downloadFile, type ExportFormat } from '@/lib/utils'
import { useChatStore } from '@/store/chat-store'
import { useChatHistoryStore } from '@/store/chat-history-store'
import type { Message, ChatSettings as ChatSettingsType, AIModel } from '@/types'

interface ChatInterfaceProps {
  className?: string
}

export function ChatInterface({ className }: ChatInterfaceProps) {
  // 使用持久化的 store
  const {
    settings,
    updateSettings,
    isLoading,
    setLoading,
    isStreaming,
    setStreaming,
    streamingContent,
    setStreamingContent
  } = useChatStore()

  // 聊天历史 store
  const {
    currentSessionId,
    createSession,
    getSession,
    addMessage,
    updateSessionMessages,
    setCurrentSession
  } = useChatHistoryStore()

  // 本地状态管理
  const [messages, setMessages] = useState<Message[]>([])
  const [showSettings, setShowSettings] = useState(false)
  const [currentInput, setCurrentInput] = useState('')
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [userCredits, setUserCredits] = useState<{ credits: number; usedCredits: number } | null>(null)
  const [showExportMenu, setShowExportMenu] = useState(false)

  // 点击外部关闭导出菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showExportMenu) {
        const target = event.target as Element
        if (!target.closest('.export-menu-container')) {
          setShowExportMenu(false)
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showExportMenu])


  // Refs
  const abortControllerRef = useRef<AbortController | null>(null)
  const currentRequestSessionId = useRef<string | null>(null)

  // 检测屏幕尺寸，移动端默认关闭侧边栏
  useEffect(() => {
    const checkScreenSize = () => {
      const isMobile = window.innerWidth < 768
      setSidebarOpen(!isMobile)
    }

    // 初始检查
    checkScreenSize()

    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 初始化时获取用户积分
  useEffect(() => {
    fetchUserCredits()
  }, [])

  // 初始化时加载当前会话的消息
  useEffect(() => {
    if (currentSessionId) {
      const session = getSession(currentSessionId)
      if (session && session.messages.length > 0) {
        setMessages(session.messages)
      }
    }
  }, [currentSessionId, getSession])



  // 发送消息
  const handleSendMessage = async (content: string, images?: string[]) => {
    if ((!content.trim() && !images?.length) || isLoading) return

    // 构建消息内容，支持图像
    let messageContent = content.trim()
    if (images && images.length > 0) {
      // 如果有图片，添加图片信息到消息中
      const imageText = images.length === 1 ? 'image' : 'images'
      messageContent = `${messageContent}\n\n[Contains ${images.length} ${imageText}]`
    }

    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: messageContent,
      timestamp: new Date(),
      ...(images && images.length > 0 && { images }), // 只在有图片时添加
      model: settings.model,
      settings
    }

    // 如果没有当前会话，创建新会话
    let sessionId = currentSessionId
    if (!sessionId) {
      sessionId = createSession(userMessage)
    } else {
      // 添加消息到现有会话
      addMessage(sessionId, userMessage)
    }

    // 保存当前请求的会话ID，供AI回复时使用
    currentRequestSessionId.current = sessionId

    setMessages(prev => [...prev, userMessage])
    setCurrentInput('')
    setLoading(true)
    setStreamingContent('')
    setStreaming(true)

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content.trim(),
          images: images, // 添加图像数据
          settings,
          conversationHistory: messages,
          stream: true
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        // 处理特定的错误状态码
        if (response.status === 402) {
          // 积分不足错误
          const errorMessage: Message = {
            id: generateId(),
            role: 'assistant',
            content: 'You have reached your trial limit. Upgrade to Premium plan to get 3600 credits monthly!\n\n[Upgrade Now](https://humanwhisper.com/pricing)',
            timestamp: new Date(),
            model: settings.model,
            metadata: {}
          }

          setMessages(prev => [...prev, errorMessage])
          setLoading(false)
          setStreaming(false)
          return
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 处理流式响应
      await handleStreamResponse(response, userMessage)

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // 请求已取消，静默处理
        return
      }

      // 发送消息失败，静默处理

      // 添加错误消息
      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: 'Sorry, an error occurred. Please try again later.',
        timestamp: new Date(),
        model: settings.model,
        metadata: {
          // 不输出具体错误信息
        }
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setLoading(false)
      setStreaming(false)
      setStreamingContent('')
      abortControllerRef.current = null
    }
  }

  // 处理流式响应
  const handleStreamResponse = async (response: Response, _userMessage: Message | null, continueMessageIndex?: number) => {
    const reader = response.body?.getReader()
    if (!reader) throw new Error('Unable to read response stream')

    const decoder = new TextDecoder()
    let fullContent = ''
    let assistantMessageId = generateId()

    // 如果是继续生成，获取现有消息内容
    const isContinuing = typeof continueMessageIndex === 'number'
    if (isContinuing && continueMessageIndex < messages.length) {
      const existingMessage = messages[continueMessageIndex]
      if (existingMessage) {
        fullContent = existingMessage.content
        assistantMessageId = existingMessage.id
      }
    }

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)

            if (data === '[DONE]') {
              // 流结束，创建或更新最终消息
              const finalMessage: Message = {
                id: assistantMessageId,
                role: 'assistant',
                content: fullContent,
                timestamp: new Date(),
                model: settings.model
              }

              if (isContinuing && typeof continueMessageIndex === 'number') {
                // 更新现有消息
                setMessages(prev => {
                  const newMessages = prev.map((msg, index) =>
                    index === continueMessageIndex ? finalMessage : msg
                  )
                  // 更新会话中的消息
                  if (currentRequestSessionId.current) {
                    updateSessionMessages(currentRequestSessionId.current, newMessages)
                  }
                  return newMessages
                })
              } else {
                // 添加新消息
                setMessages(prev => {
                  const newMessages = [...prev, finalMessage]
                  // 添加消息到会话
                  if (currentRequestSessionId.current) {
                    addMessage(currentRequestSessionId.current, finalMessage)
                  }
                  return newMessages
                })
              }
              setStreamingContent('')

              // 刷新用户积分（因为已经扣费）
              fetchUserCredits()
              return
            }

            try {
              const parsed = JSON.parse(data)

              if (parsed.type === 'content' && parsed.content) {
                fullContent += parsed.content
                setStreamingContent(fullContent)

              } else if (parsed.type === 'error') {
                throw new Error(parsed.error)
              }
            } catch (parseError) {
              if (parseError instanceof SyntaxError) {
                // 忽略JSON解析错误，可能是不完整的数据
                continue
              }
              throw parseError
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  // 停止生成
  const handleStopGeneration = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
  }

  // 获取用户积分
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/user/metadata')
      if (response.ok) {
        const data = await response.json()
        setUserCredits({
          credits: data.credits || 0,
          usedCredits: data.usedCredits || 0
        })
      }
    } catch (error) {
      // 获取用户积分失败，静默处理
    }
  }

  // 开始新对话
  const handleNewChat = () => {
    setMessages([])
    setCurrentInput('')
    setStreamingContent('')
    setCurrentSession(null)
  }

  // 选择会话
  const handleSessionSelect = (sessionId: string) => {
    const session = getSession(sessionId)
    if (session) {
      setMessages(session.messages)
      setCurrentSession(sessionId)
      // 在移动端选择会话后关闭侧边栏
      if (window.innerWidth < 768) {
        setSidebarOpen(false)
      }
    }
  }

  // 清空对话（保持兼容性）
  const handleClearChat = () => {
    handleNewChat()
  }



  // 重新生成回复
  const handleRegenerate = async (messageId: string) => {
    // 找到要重新生成的消息
    const messageIndex = messages.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // 获取该消息之前的对话历史（不包括要重新生成的消息）
    const conversationHistory = messages.slice(0, messageIndex)

    // 找到触发重新生成的用户消息
    const userMessage = conversationHistory[conversationHistory.length - 1]
    if (!userMessage || userMessage.role !== 'user') return

    // 移除要重新生成的消息及其之后的所有消息
    setMessages(prev => prev.slice(0, messageIndex))

    // 直接调用AI生成新回复
    setLoading(true)
    setStreamingContent('')
    setStreaming(true)

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          settings,
          conversationHistory: conversationHistory.slice(0, -1), // 不包括当前用户消息
          stream: true
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        // 处理特定的错误状态码
        if (response.status === 402) {
          // 积分不足错误
          const errorMessage: Message = {
            id: generateId(),
            role: 'assistant',
            content: 'You have reached your trial limit. Upgrade to Premium plan to get 3600 credits monthly!\n\n[Upgrade Now](https://humanwhisper.com/pricing)',
            timestamp: new Date(),
            model: settings.model,
            metadata: {}
          }

          setMessages(prev => [...prev, errorMessage])
          setLoading(false)
          setStreaming(false)
          return
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 处理流式响应
      await handleStreamResponse(response, userMessage)

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // 重新生成已取消，静默处理
        return
      }

      // 重新生成失败，静默处理

      // 添加错误消息
      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: 'Sorry, an error occurred while regenerating. Please try again.',
        timestamp: new Date(),
        model: settings.model,
        metadata: {
          // 不输出具体错误信息
        }
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setLoading(false)
      setStreaming(false)
      setStreamingContent('')
      abortControllerRef.current = null
    }
  }



  // 导出对话
  const handleExportChat = (format: ExportFormat = 'json') => {
    if (messages.length === 0) return

    const dateStr = new Date().toISOString().slice(0, 10)
    const content = exportChatToFormat(messages, format)

    const fileExtensions = {
      json: 'json',
      markdown: 'md',
      txt: 'txt'
    }

    const mimeTypes = {
      json: 'application/json',
      markdown: 'text/markdown',
      txt: 'text/plain'
    }

    const filename = `humanwhisper-chat-${dateStr}.${fileExtensions[format]}`
    downloadFile(content, filename, mimeTypes[format])

    // 关闭导出菜单
    setShowExportMenu(false)
  }

  // 更新设置
  const handleSettingsChange = (newSettings: Partial<ChatSettingsType>) => {
    updateSettings(newSettings)
  }

  // 模型切换
  const handleModelChange = (model: AIModel) => {
    updateSettings({ model })
  }



  return (
    <div className={`flex h-full bg-background ${className}`}>
      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      {sidebarOpen && (
        <div className="w-80 bg-card border-r h-full flex flex-col fixed md:relative z-50 md:z-0">
          <ChatSidebar
            isOpen={sidebarOpen}
            onToggle={() => setSidebarOpen(!sidebarOpen)}
            onNewChat={handleNewChat}
            onOpenSettings={() => setShowSettings(true)}
            onSessionSelect={handleSessionSelect}
          />
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex flex-col flex-1 min-w-0">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-card">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            title={sidebarOpen ? "Hide Sidebar" : "Show Sidebar"}
          >
            <Menu className="h-4 w-4" />
          </Button>
          <h1 className="text-xl font-semibold">HumanWhisper</h1>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowSettings(!showSettings)}
            title="Settings"
          >
            <Settings className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClearChat}
            disabled={isLoading || messages.length === 0}
            title="Clear Chat"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
          
          {/* 导出下拉菜单 */}
          <div className="relative export-menu-container">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowExportMenu(!showExportMenu)}
              disabled={messages.length === 0}
              title="Export Chat"
            >
              <Download className="h-4 w-4" />
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>

            {/* 导出格式菜单 */}
            {showExportMenu && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-background border border-border rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => handleExportChat('json')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center"
                  >
                    <span className="mr-2">📄</span>
                    JSON Format
                    <span className="ml-auto text-xs text-muted-foreground">.json</span>
                  </button>
                  <button
                    onClick={() => handleExportChat('markdown')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center"
                  >
                    <span className="mr-2">📝</span>
                    Markdown Format
                    <span className="ml-auto text-xs text-muted-foreground">.md</span>
                  </button>
                  <button
                    onClick={() => handleExportChat('txt')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center"
                  >
                    <span className="mr-2">📋</span>
                    Plain Text
                    <span className="ml-auto text-xs text-muted-foreground">.txt</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="border-b bg-muted/30">
          <ChatSettings
            settings={settings}
            onSettingsChange={handleSettingsChange}
            onClose={() => setShowSettings(false)}
          />
        </div>
      )}

      {/* 消息列表 */}
      <div className="flex-1 overflow-hidden">
        <MessageList
          messages={messages}
          streamingContent={streamingContent}
          isStreaming={isStreaming}
          currentModel={settings.model}
          onRegenerate={handleRegenerate}
        />
      </div>





      {/* 输入区域 */}
      <div className="border-t bg-card p-4">
        <ChatInput
          value={currentInput}
          onChange={setCurrentInput}
          onSend={handleSendMessage}
          onStop={handleStopGeneration}
          disabled={isLoading && !isStreaming}
          isLoading={isLoading}
          isStreaming={isStreaming}
          placeholder="Ask me anything! HumanWhisper will explain it in simple, easy-to-understand terms..."
          currentModel={settings.model}
          onModelChange={handleModelChange}
          userCredits={userCredits}
        />
      </div>
      </div>
    </div>
  )
}
