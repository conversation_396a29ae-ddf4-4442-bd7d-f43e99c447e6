import { PageLayout } from '@/components/layout/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Coins, CreditCard, Shield, Users } from 'lucide-react'
import Image from 'next/image'
import type { Metadata } from 'next'

// Terms of Service页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Terms and Usage Rules - HumanWhisper',
  description: 'Review the terms that guide the use of HumanWhisper, including user responsibilities, rights, and service conditions.',
  keywords: [
    'terms of service',
    'usage rules',
    'HumanWhisper policy',
    'AI platform terms',
    'user agreement'
  ],
  openGraph: {
    title: 'Terms and Usage Rules - HumanWhisper',
    description: 'Review the terms that guide the use of HumanWhisper, including user responsibilities, rights, and service conditions.',
    url: '/terms',
    type: 'website',
  },
  twitter: {
    title: 'Terms and Usage Rules - HumanWhisper',
    description: 'Review the terms that guide the use of HumanWhisper, including user responsibilities, rights, and service conditions.',
  },
  alternates: {
    canonical: '/terms',
  },
}

export default function TermsPage() {
  const modelPricing = [
    {
      model: 'GPT Series',
      baseCredits: 1,
      imageCredits: 1,
      color: 'bg-green-100 text-green-600',
      icon: '/gpt.svg'
    },
    {
      model: 'Claude Series',
      baseCredits: 4,
      imageCredits: 2,
      color: 'bg-purple-100 text-purple-600',
      icon: '/claude.svg'
    },
    {
      model: 'Gemini Series',
      baseCredits: 2,
      imageCredits: 1,
      color: 'bg-blue-100 text-blue-600',
      icon: '/gemini.svg'
    }
  ]

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Terms and Usage Rules
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Welcome to HumanWhisper! These <strong>terms of service</strong> outline the 
              <strong>usage rules</strong> and conditions that govern your use of our 
              <strong>AI platform</strong>. Please review this <strong>user agreement</strong> carefully.
            </p>
          </div>
        </div>
      </section>

      {/* Service Introduction */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Service Terms Overview</h2>
            
            <Card className="mb-8">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <FileText className="h-6 w-6 text-primary" />
                  <CardTitle>Agreement to Terms</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  By registering for and using HumanWhisper, you agree to be bound by these 
                  <strong>HumanWhisper policy</strong> terms and conditions. These <strong>AI platform terms</strong> 
                  govern your access to and use of our AI simplification services, including any content, 
                  functionality, and services offered through our platform.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Credit System */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center">Credit System & Pricing</h2>
            
            {/* Plan Overview */}
            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Users className="h-6 w-6 text-blue-600" />
                    <CardTitle>Free Plan</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary mb-2">100 Credits</div>
                  <p className="text-muted-foreground mb-4">
                    New users receive 100 credits upon registration to explore our AI simplification features.
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li>• Access to all AI models</li>
                    <li>• Local chat history storage</li>
                    <li>• Basic simplification features</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-primary/20 border-2">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-6 w-6 text-purple-600" />
                    <CardTitle>Premium Monthly Plan</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary mb-2">3,600 Credits</div>
                  <p className="text-muted-foreground mb-4">
                    Monthly subscription with 3,600 credits, valid for 30 days from payment date.
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li>• High-speed access to all models</li>
                    <li>• Advanced customization options</li>
                    <li>• Priority processing</li>
                    <li>• Early access to new features</li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Model Pricing Table */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="text-center">Credit Consumption by AI Model</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4 font-semibold">Model Series</th>
                        <th className="text-center p-4 font-semibold">Base Credits (per question)</th>
                        <th className="text-center p-4 font-semibold">Image Credits (per image)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {modelPricing.map((model, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                              <div className={`h-8 w-8 rounded-full flex items-center justify-center ${model.color}`}>
                                <Image
                                  src={model.icon}
                                  alt={`${model.model} icon`}
                                  width={16}
                                  height={16}
                                />
                              </div>
                              <span className="font-medium">{model.model}</span>
                            </div>
                          </td>
                          <td className="p-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <span className="font-semibold">{model.baseCredits}</span>
                              <Coins className="h-4 w-4 text-yellow-500" />
                            </div>
                          </td>
                          <td className="p-4 text-center">
                            <div className="flex items-center justify-center gap-1">
                              <span className="font-semibold">+{model.imageCredits}</span>
                              <Coins className="h-4 w-4 text-yellow-500" />
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Usage Examples */}
            <Card>
              <CardHeader>
                <CardTitle>Daily/Monthly Usage Estimates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-semibold mb-4">Free Plan (100 Credits)</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>GPT questions:</span>
                        <span className="font-medium">~100 questions</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Claude questions:</span>
                        <span className="font-medium">~25 questions</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Gemini questions:</span>
                        <span className="font-medium">~50 questions</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-4">Premium Plan (3,600 Credits/month)</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>GPT questions:</span>
                        <span className="font-medium">~3,600 questions</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Claude questions:</span>
                        <span className="font-medium">~900 questions</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Gemini questions:</span>
                        <span className="font-medium">~1,800 questions</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* User Responsibilities */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">User Responsibilities & Rights</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    Your Rights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Access to AI simplification services as described</li>
                    <li>• Privacy protection and data security</li>
                    <li>• Fair usage of allocated credits</li>
                    <li>• Customer support and assistance</li>
                    <li>• Service updates and improvements</li>
                    <li>• Cancellation rights for subscriptions</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Your Responsibilities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Use the service in accordance with these terms</li>
                    <li>• Provide accurate account information</li>
                    <li>• Respect intellectual property rights</li>
                    <li>• Not misuse or abuse the AI services</li>
                    <li>• Maintain account security</li>
                    <li>• Report any technical issues promptly</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Service Changes & Support */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Service Changes & Termination</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 text-sm text-muted-foreground">
                    <p>
                      We reserve the right to modify, suspend, or discontinue any part of our service 
                      with reasonable notice to users.
                    </p>
                    <p>
                      Users may terminate their account at any time. VIP subscriptions can be cancelled 
                      with continued access until the end of the billing period.
                    </p>
                    <p>
                      We may terminate accounts that violate these <strong>usage rules</strong> or 
                      engage in prohibited activities.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Limitation of Liability</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 text-sm text-muted-foreground">
                    <p>
                      HumanWhisper provides AI simplification services "as is" without warranties 
                      of any kind, express or implied.
                    </p>
                    <p>
                      We are not liable for any indirect, incidental, special, or consequential 
                      damages arising from your use of our services.
                    </p>
                    <p>
                      Our total liability shall not exceed the amount paid by you for the services 
                      in the preceding 12 months.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Last Updated */}
      <section className="py-12 bg-muted/30">
        <div className="container text-center">
          <div className="text-sm text-muted-foreground">
            <p className="mb-2">
              <strong>Last Updated:</strong> July 21, 2025
            </p>
            <p>
              These terms may be updated from time to time. Continued use of the service
              constitutes acceptance of any changes.
            </p>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
