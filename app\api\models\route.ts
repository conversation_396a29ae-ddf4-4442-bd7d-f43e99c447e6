import { NextRequest, NextResponse } from 'next/server'
import { getAIClient } from '@/lib/ai-client'
import { 
  getAllModels, 
  getModelById, 
  getModelsByProvider,
  getModelPerformance,
  getModelLimitations,
  recommendModel
} from '@/lib/ai-models'
import { ERROR_MESSAGES } from '@/lib/constants'
import type { ChatSettings, AIModel } from '@/types'

/**
 * 模型API - GET请求处理
 * 获取可用模型列表和详细信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'list'
    const modelId = searchParams.get('model') as AIModel
    const provider = searchParams.get('provider')
    
    switch (action) {
      case 'list':
        return handleListModels()
        
      case 'detail':
        if (!modelId) {
          return NextResponse.json(
            { error: 'Missing model ID parameter' },
            { status: 400 }
          )
        }
        return handleModelDetail(modelId)
        
      case 'by-provider':
        if (!provider) {
          return NextResponse.json(
            { error: 'Missing provider parameter' },
            { status: 400 }
          )
        }
        return handleModelsByProvider(provider)
        
      case 'recommend':
        const settingsParam = searchParams.get('settings')
        if (!settingsParam) {
          return NextResponse.json(
            { error: 'Missing settings parameter' },
            { status: 400 }
          )
        }
        
        try {
          const settings = JSON.parse(settingsParam) as ChatSettings
          return handleRecommendModel(settings)
        } catch {
          return NextResponse.json(
            { error: 'Invalid settings parameter format' },
            { status: 400 }
          )
        }
        
      default:
        return NextResponse.json(
          { error: 'Unsupported operation type' },
          { status: 400 }
        )
    }
    
  } catch (error) {
    // 模型API错误，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.UNKNOWN_ERROR },
      { status: 500 }
    )
  }
}

/**
 * 处理模型列表请求
 */
async function handleListModels() {
  try {
    // 获取本地支持的模型
    const localModels = getAllModels()
    
    // 尝试获取远程可用模型（可选）
    let remoteModels: string[] = []
    try {
      const aiClient = getAIClient()
      remoteModels = await aiClient.getAvailableModels()
    } catch (error) {
      // 无法获取远程模型列表，静默处理
    }
    
    // 合并并增强模型信息
    const enhancedModels = localModels.map(model => {
      const performance = getModelPerformance(model.id)
      const limitations = getModelLimitations(model.id)
      
      return {
        ...model,
        available: remoteModels.length === 0 || remoteModels.includes(model.id),
        performance,
        limitations: {
          maxTokens: limitations.maxTokens,
          contextWindow: limitations.contextWindow,
          rateLimit: limitations.rateLimit
        },
        status: 'active' // 可以根据实际情况动态设置
      }
    })
    
    return NextResponse.json({
      models: enhancedModels,
      total: enhancedModels.length,
      available: enhancedModels.filter(m => m.available).length,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    // 获取模型列表失败，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.API_ERROR },
      { status: 500 }
    )
  }
}

/**
 * 处理模型详情请求
 */
async function handleModelDetail(modelId: AIModel) {
  try {
    const modelInfo = getModelById(modelId)
    
    if (!modelInfo) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }
    
    // 获取详细信息
    const performance = getModelPerformance(modelId)
    const limitations = getModelLimitations(modelId)
    
    // 检查模型可用性
    let isAvailable = false
    let lastChecked = null
    
    try {
      const aiClient = getAIClient()
      isAvailable = await aiClient.checkConnection()
      lastChecked = new Date().toISOString()
    } catch (error) {
      // 模型连接检查失败，静默处理
    }
    
    const detailedInfo = {
      ...modelInfo,
      performance,
      limitations,
      availability: {
        isAvailable,
        lastChecked,
        status: isAvailable ? 'online' : 'offline'
      },
      usage: {
        recommendedFor: getModelUseCases(modelId),
        bestPractices: getModelBestPractices(modelId),
        limitations: limitations.restrictions
      }
    }
    
    return NextResponse.json(detailedInfo)
    
  } catch (error) {
    // 获取模型详情失败，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.API_ERROR },
      { status: 500 }
    )
  }
}

/**
 * 处理按提供商筛选模型请求
 */
async function handleModelsByProvider(provider: string) {
  try {
    const models = getModelsByProvider(provider)
    
    if (models.length === 0) {
      return NextResponse.json(
        { error: 'No models found for this provider' },
        { status: 404 }
      )
    }
    
    // 增强模型信息
    const enhancedModels = models.map(model => ({
      ...model,
      performance: getModelPerformance(model.id),
      limitations: getModelLimitations(model.id)
    }))
    
    return NextResponse.json({
      provider,
      models: enhancedModels,
      total: enhancedModels.length
    })
    
  } catch (error) {
    // 获取提供商模型失败，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.API_ERROR },
      { status: 500 }
    )
  }
}

/**
 * 处理模型推荐请求
 */
async function handleRecommendModel(settings: ChatSettings) {
  try {
    const recommendedModel = recommendModel(settings)
    const modelInfo = getModelById(recommendedModel)
    
    if (!modelInfo) {
      return NextResponse.json(
        { error: 'Failed to get recommended model information' },
        { status: 500 }
      )
    }
    
    // 生成推荐理由
    const reasons = generateRecommendationReasons(settings, recommendedModel)
    
    return NextResponse.json({
      recommended: {
        ...modelInfo,
        performance: getModelPerformance(recommendedModel),
        reasons
      },
      alternatives: getAlternativeModels(recommendedModel, settings),
      confidence: calculateRecommendationConfidence(settings, recommendedModel)
    })
    
  } catch (error) {
    // 模型推荐失败，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.API_ERROR },
      { status: 500 }
    )
  }
}



/**
 * 获取模型使用场景
 */
function getModelUseCases(modelId: AIModel): string[] {
  // 这里应该从 ai-models.ts 导入，为了避免循环依赖，暂时内联
  const useCases: Record<AIModel, string[]> = {
    'gpt-4.1-nano': ['Quick Q&A', 'Daily conversation', 'Simple explanations'],
    'claude-3-5-haiku-latest': ['Deep analysis', 'Complex reasoning', 'Creative writing'],
    'gemini-2.0-flash': ['Multimodal understanding', 'Technical explanations', 'Real-time chat']
  }
  
  return useCases[modelId] || []
}

/**
 * 获取模型最佳实践
 */
function getModelBestPractices(modelId: AIModel): string[] {
  const practices: Record<AIModel, string[]> = {
    'gpt-4.1-nano': ['Keep prompts concise', 'Suitable for high-frequency use', 'Best cost-effectiveness'],
    'claude-3-5-haiku-latest': ['Can use complex prompts', 'Suitable for deep thinking tasks', 'Mind response time'],
    'gemini-2.0-flash': ['Leverage its innovation capabilities', 'Suitable for diverse tasks', 'Keep prompts flexible']
  }
  
  return practices[modelId] || []
}

/**
 * 生成推荐理由
 */
function generateRecommendationReasons(settings: ChatSettings, modelId: AIModel): string[] {
  const reasons: string[] = []

  if (settings.responseLength === 'BRIEF' && modelId === 'gpt-4.1-nano') {
    reasons.push('Suitable for brief and quick answers')
  }

  if (settings.responseLength === 'THOROUGH' && modelId === 'claude-3-5-haiku-latest') {
    reasons.push('Excels at thorough and in-depth explanations')
  }

  if (settings.targetAge <= 12 && modelId === 'gpt-4.1-nano') {
    reasons.push('Suitable for child-friendly explanations')
  }

  if (settings.useAnalogies && modelId === 'claude-3-5-haiku-latest') {
    reasons.push('Excellent performance in analogical explanations')
  }

  return reasons.length > 0 ? reasons : ['Best match based on your settings']
}

/**
 * 获取替代模型
 */
function getAlternativeModels(recommendedModel: AIModel, _settings: ChatSettings) {
  const allModels = getAllModels()
  return allModels
    .filter(model => model.id !== recommendedModel)
    .slice(0, 2)
    .map(model => ({
      ...model,
      performance: getModelPerformance(model.id)
    }))
}

/**
 * 计算推荐置信度
 */
function calculateRecommendationConfidence(settings: ChatSettings, modelId: AIModel): number {
  // 简化的置信度计算
  let confidence = 0.7 // 基础置信度

  // 根据设置匹配度调整
  if (settings.responseLength === 'BRIEF' && modelId === 'gpt-4.1-nano') confidence += 0.2
  if (settings.responseLength === 'THOROUGH' && modelId === 'claude-3-5-haiku-latest') confidence += 0.2
  if (settings.targetAge <= 12 && modelId === 'gpt-4.1-nano') confidence += 0.1

  return Math.min(1.0, confidence)
}
