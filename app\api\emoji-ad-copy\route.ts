import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 平台映射
const PLATFORM_MAP: Record<string, { name: string, charLimit: number | null, style: string }> = {
  'twitter': { name: 'Twitter/X', charLimit: 280, style: 'short, punchy, trending hashtags' },
  'instagram': { name: 'Instagram', charLimit: 2200, style: 'visual-focused, lifestyle appeal, storytelling' },
  'tiktok': { name: 'TikTok', charLimit: 150, style: 'trendy, youth-oriented, viral potential' },
  'facebook': { name: 'Facebook', charLimit: 500, style: 'detailed, broader demographics, community-focused' },
  'linkedin': { name: 'LinkedIn', charLimit: 700, style: 'professional, business-focused, value-driven' },
  'push_notification': { name: 'Push Notification', charLimit: 60, style: 'urgent, action-oriented, minimal' },
  'email_subject': { name: 'Email Subject', charLimit: 50, style: 'compelling, curiosity-driven, clear benefit' },
  'youtube': { name: 'YouTube', charLimit: 1000, style: 'engaging, descriptive, community-building' }
}

// 目标人群映射
const AUDIENCE_MAP: Record<string, string> = {
  'gen_z': 'Gen Z (18-24): digital natives, trend-conscious, authentic, value diversity',
  'millennials': 'Millennials (25-40): tech-savvy, value-driven, career-focused, experience-oriented',
  'gen_x': 'Gen X (41-56): practical, family-oriented, brand loyal, quality-focused',
  'professionals': 'Business Professionals: career-focused, efficiency-minded, results-oriented',
  'parents': 'Parents: family-focused, safety-conscious, time-pressed, value-seeking',
  'students': 'Students: budget-conscious, learning-oriented, social, future-focused',
  'entrepreneurs': 'Entrepreneurs: innovation-focused, growth-minded, risk-taking, ambitious',
  'seniors': 'Seniors (55+): quality-focused, traditional values, trust-based, careful decision-makers'
}

// 构建广告文案生成prompt
function buildAdCopyPrompt(
  platform: string,
  productSelling: string,
  targetAudience: string
): string {
  const platformInfo = PLATFORM_MAP[platform] || { name: platform, charLimit: null, style: 'engaging and appropriate for the platform' }
  const audienceDesc = AUDIENCE_MAP[targetAudience] || targetAudience

  const prompt = `You are a social media marketing expert specializing in emoji-enhanced ad copy. Your task is to create engaging, platform-optimized ad copy that converts.

**Platform:** ${platformInfo.name}
${platformInfo.charLimit ? `**Character Limit:** ${platformInfo.charLimit} characters` : ''}
**Platform Style:** ${platformInfo.style}

**Product/Service:** ${productSelling}

**Target Audience:** ${audienceDesc}

**Instructions:**
Create exactly 3 different ad copy variations that:

1. **Emoji Integration**: Use emojis strategically to enhance emotion and visual appeal (2-5 emojis per copy)
2. **Platform Optimization**: Match the tone and style of ${platformInfo.name}
3. **Audience Targeting**: Appeal specifically to ${targetAudience}
4. **Call-to-Action**: Include compelling CTAs appropriate for the platform
5. **Character Limits**: ${platformInfo.charLimit ? `Stay within ${platformInfo.charLimit} characters` : 'Use appropriate length for the platform'}
6. **Engagement**: Focus on driving clicks, shares, or conversions

**Emoji Guidelines:**
- Use relevant, not random emojis
- Place emojis to enhance readability and emotion
- Avoid emoji overload (quality over quantity)
- Consider cultural appropriateness

**Output Format:**
Return a valid JSON array with exactly 3 objects:

[
  {
    "copy": "Your ad copy with emojis here",
    "explanation": "Brief explanation of strategy and emoji choices",
    "emojiCount": 3,
    "characterCount": 145
  }
]

**Important:**
- Each copy should be unique in approach (different angles, emotions, CTAs)
- Ensure JSON format is valid and properly escaped
- Count characters and emojis accurately
- Make copies platform-native and audience-appropriate

Generate the 3 emoji ad copies now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      platform,
      productSelling,
      targetAudience,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!platform || !productSelling?.trim() || !targetAudience) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildAdCopyPrompt(
      platform,
      productSelling.trim(),
      targetAudience
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'Qwen/Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let copies: Array<{copy: string, explanation: string, emojiCount: number, characterCount: number}>
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      copies = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!Array.isArray(copies)) {
        throw new Error('Response is not an array')
      }
      
      // 验证每个广告文案项目并重新计算字符数和emoji数
      copies = copies.filter(copy => 
        copy && 
        typeof copy.copy === 'string' && 
        typeof copy.explanation === 'string' &&
        copy.copy.trim()
      ).map(copy => ({
        copy: copy.copy.trim(),
        explanation: copy.explanation.trim(),
        emojiCount: (copy.copy.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length,
        characterCount: copy.copy.length
      }))
      
      if (copies.length === 0) {
        throw new Error('No valid ad copy items found')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取广告文案
      const lines = generatedContent.split('\n').filter(line => line.trim())
      copies = []
      
      for (const line of lines) {
        if (line.includes('📱') || line.includes('🚀') || line.includes('💡') || line.length > 20) {
          const cleanLine = line.replace(/^\d+\.\s*/, '').replace(/^[\-\*]\s*/, '').trim()
          if (cleanLine && cleanLine.length > 10) {
            copies.push({
              copy: cleanLine,
              explanation: 'Engaging ad copy with strategic emoji placement',
              emojiCount: (cleanLine.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length,
              characterCount: cleanLine.length
            })
          }
        }
      }
      
      if (copies.length === 0) {
        throw new Error('Failed to parse ad copy content')
      }
    }

    return NextResponse.json({
      copies: copies.slice(0, 3), // 确保不超过3个
      metadata: {
        platform,
        targetAudience,
        copyCount: copies.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate ad copies' },
      { status: 500 }
    )
  }
}
