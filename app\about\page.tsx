import { PageLayout } from '@/components/layout/PageLayout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Heart, Target, Lightbulb, Shield, Users, Mail } from 'lucide-react'
import Link from 'next/link'
import type { Metadata } from 'next'

// About Us页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Meet the Team Behind HumanWhisper - HumanWhisper',
  description: 'Discover our mission to make knowledge accessible using AI. Learn who we are, what we do, and why we believe in simplifying complexity.',
  keywords: [
    'about HumanWhisper',
    'our team',
    'AI mission',
    'AI for education',
    'knowledge accessibility'
  ],
  openGraph: {
    title: 'Meet the Team Behind HumanWhisper - HumanWhisper',
    description: 'Discover our mission to make knowledge accessible using AI. Learn who we are, what we do, and why we believe in simplifying complexity.',
    url: '/about',
    type: 'website',
  },
  twitter: {
    title: 'Meet the Team Behind HumanWhisper - HumanWhisper',
    description: 'Discover our mission to make knowledge accessible using AI. Learn who we are, what we do, and why we believe in simplifying complexity.',
  },
  alternates: {
    canonical: '/about',
  },
}

const values = [
  {
    id: 'user-centered',
    icon: Heart,
    title: 'User-Centered Design',
    description: 'Every feature we build starts with understanding what our users truly need - clear, accessible explanations without the complexity.'
  },
  {
    id: 'privacy-security',
    icon: Shield,
    title: 'Privacy & Security',
    description: 'Your conversations stay private. We store chat history locally on your device and never access your personal discussions.'
  },
  {
    id: 'continuous-innovation',
    icon: Lightbulb,
    title: 'Continuous Innovation',
    description: 'We constantly improve our AI models and simplification techniques to provide better, more accurate explanations.'
  },
  {
    id: 'accessibility-first',
    icon: Target,
    title: 'Accessibility First',
    description: 'Knowledge should be accessible to everyone, regardless of their educational background or technical expertise.'
  }
]

export default function AboutPage() {
  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Meet the Team Behind
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                HumanWhisper
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              We're a passionate team dedicated to making complex knowledge accessible to everyone. 
              Discover <strong>our mission</strong> to bridge the gap between complexity and understanding 
              through innovative <strong>AI for education</strong>.
            </p>
          </div>
        </div>
      </section>

      {/* Company Introduction */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
              About HumanWhisper
            </h2>
            
            <div className="prose prose-lg max-w-none text-center mb-12">
              <p className="text-xl text-muted-foreground leading-relaxed">
                HumanWhisper was born from a simple observation: the world is full of incredible knowledge, 
                but much of it is locked away behind complex jargon and technical language. <strong>Our team</strong> 
                believes that everyone deserves to understand the world around them, regardless of their background 
                or expertise level.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3 mb-4">
                    <Users className="h-8 w-8 text-primary" />
                    <CardTitle className="text-xl">Our Story</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    Founded by a team of AI researchers, educators, and communication experts, HumanWhisper 
                    emerged from countless conversations with people who felt overwhelmed by complex information. 
                    We realized that the problem wasn't intelligence—it was accessibility.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3 mb-4">
                    <Target className="h-8 w-8 text-primary" />
                    <CardTitle className="text-xl">What We Do</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    We've developed AI technology that doesn't just translate complex topics—it truly understands 
                    them and explains them the way a patient friend would. Our focus on <strong>knowledge accessibility</strong> 
                    means breaking down barriers, not just information.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">
              Our Mission
            </h2>
            
            <div className="bg-card p-8 rounded-lg border mb-8">
              <blockquote className="text-2xl md:text-3xl font-medium text-primary mb-6">
                "To make knowledge accessible to everyone by transforming complex information 
                into clear, understandable explanations."
              </blockquote>
              <p className="text-lg text-muted-foreground">
                We believe that understanding shouldn't be a privilege. Whether you're a student tackling 
                difficult concepts, a professional navigating industry jargon, or simply someone curious 
                about the world, <strong>HumanWhisper's AI mission</strong> is to be your bridge to clarity.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Technology & Innovation */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
              Technology & Innovation
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <div>
                <h3 className="text-xl font-semibold mb-4">Advanced AI Simplification</h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  Our proprietary AI doesn't just paraphrase—it truly comprehends complex concepts and 
                  rebuilds them using accessible language, analogies, and examples that resonate with 
                  human understanding.
                </p>
                
                <h3 className="text-xl font-semibold mb-4">Continuous Learning</h3>
                <p className="text-muted-foreground leading-relaxed">
                  We constantly refine our models based on user interactions and feedback, ensuring 
                  that our explanations become more accurate, helpful, and human-like over time.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-4">Multi-Domain Expertise</h3>
                <p className="text-muted-foreground leading-relaxed mb-6">
                  From scientific papers to legal documents, financial reports to technical manuals, 
                  our AI has been trained to understand and simplify content across virtually every field.
                </p>
                
                <h3 className="text-xl font-semibold mb-4">Human-Centered Design</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Every feature is designed with real people in mind. We prioritize clarity, empathy, 
                  and genuine understanding over technical complexity.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values & Commitments */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
              Our Values & Commitments
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {values.map((value) => (
                <Card key={value.id} className="text-center h-full">
                  <CardContent className="pt-6">
                    <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <value.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="font-semibold mb-3">{value.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Social Impact & Future */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">
              Social Impact & Future Vision
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              We envision a world where <strong>knowledge accessibility</strong> is universal. Where a student 
              in any corner of the world can understand quantum physics, where anyone can grasp complex 
              legal documents, and where curiosity is never limited by complexity.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">10M+</div>
                <p className="text-muted-foreground">Complex topics simplified</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <p className="text-muted-foreground">Industries covered</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <p className="text-muted-foreground">Available worldwide</p>
              </div>
            </div>
            
            <p className="text-lg text-muted-foreground">
              This is just the beginning. We're working on new features, expanding our AI capabilities, 
              and building partnerships with educational institutions to make learning more accessible than ever.
            </p>
          </div>
        </div>
      </section>

      {/* Contact & Interaction */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Join Our Mission
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Have feedback, suggestions, or want to learn more about <strong>HumanWhisper</strong>? 
            We'd love to hear from you. Every conversation helps us improve and grow.
          </p>
          <div className="flex justify-center">
            <Link href="/contact">
              <Button size="lg" variant="secondary">
                <Mail className="h-5 w-5 mr-2" />
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
