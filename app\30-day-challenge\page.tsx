import { PageLayout } from '@/components/layout/PageLayout'
import { ThirtyDayChallengeClient } from './ThirtyDayChallengeClient'
import type { Metadata } from 'next'

// 30-Day Challenge页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI 30-Day Challenge Generator - Community Engagement Ideas | HumanWhisper',
  description: 'Generate engaging 30-day challenges for communities. Create daily tasks, check-in prompts, and reward systems to boost member retention.',
  keywords: [
    '30 day challenge',
    'community engagement',
    'challenge ideas',
    'daily tasks',
    'member retention',
    'community building',
    'habit tracker'
  ],
  openGraph: {
    title: 'AI 30-Day Challenge Generator - Community Engagement Ideas | HumanWhisper',
    description: 'Generate engaging 30-day challenges for communities. Create daily tasks, check-in prompts, and reward systems to boost member retention.',
    url: '/30-day-challenge',
    type: 'website',
  },
  twitter: {
    title: 'AI 30-Day Challenge Generator - Community Engagement Ideas | HumanWhisper',
    description: 'Generate engaging 30-day challenges for communities. Create daily tasks, check-in prompts, and reward systems to boost member retention.',
  },
  alternates: {
    canonical: '/30-day-challenge',
  },
}

export default function ThirtyDayChallengePage() {
  return (
    <PageLayout>
      <ThirtyDayChallengeClient />
    </PageLayout>
  )
}
