'use client'

import React from 'react'
import Link from 'next/link'
import { Plus, MessageSquare, Settings, X, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ChatHistoryList } from './ChatHistoryList'

interface ChatSidebarProps {
  isOpen: boolean
  onToggle: () => void
  onNewChat: () => void
  onOpenSettings: () => void
  onSessionSelect: (sessionId: string) => void
}

export function ChatSidebar({ onToggle, onNewChat, onOpenSettings, onSessionSelect }: ChatSidebarProps) {

  return (
    <div className="w-full h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <Link href="/" className="flex items-center space-x-2">
            <MessageSquare className="h-6 w-6 text-primary" />
            <span className="font-semibold">HumanWhisper</span>
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="md:hidden"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* New Chat Button */}
        <div className="p-4">
          <Button
            onClick={onNewChat}
            className="w-full justify-start"
            variant="outline"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Chat
          </Button>
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-hidden">
          <ChatHistoryList onSessionSelect={onSessionSelect} />
        </div>

        {/* Bottom Actions */}
        <div className="border-t p-4 space-y-2">
          <Link href="/">
            <Button variant="ghost" className="w-full justify-start">
              <Home className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <Button
            variant="ghost"
            className="w-full justify-start"
            onClick={onOpenSettings}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
    </div>
  )
}
