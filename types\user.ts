// 用户相关类型定义

export interface UserMetadata {
  plan: 'free' | 'premium'
  credits: number
  usedCredits: number
  totalCreditsEarned: number
  subscriptionId?: string
  subscriptionStatus?: 'active' | 'canceled' | 'past_due' | 'trialing' | 'incomplete'
  subscriptionPeriodStart?: string
  subscriptionPeriodEnd?: string
  createdAt?: string
  lastActiveAt?: string
  creemCustomerId?: string
  creemSubscriptionId?: string
  creemOrderId?: string
}

export interface PlanLimits {
  maxCredits: number // -1 表示无限制
  features: string[]
  name: string
  price: number
  description: string
}

export interface UserPlan {
  id: string
  name: string
  limits: PlanLimits
  isActive: boolean
}

// 用户使用统计
export interface UserUsage {
  totalMessages: number
  messagesThisMonth: number
  creditsUsed: number
  creditsRemaining: number
  lastResetDate: string
  effectivePlan: string // 有效计划（考虑订阅状态后的实际计划）
  subscriptionActive: boolean // 订阅是否有效
}

// 订阅计划配置
export const SUBSCRIPTION_PLANS: Record<string, PlanLimits> = {
  free: {
    maxCredits: 100, // 注册送100积分
    features: ['basic_models'],
    name: 'Free Plan',
    price: 0,
    description: 'Get 100 credits to start using basic AI models'
  },
  premium: {
    maxCredits: 3600, // 正式环境3600积分
    features: ['all_models', 'priority_support', 'no_ads', 'exclusive_features'],
    name: 'Premium Plan',
    price: 29.99,
    description: '3600 credits monthly + All AI models + Premium features'
  }
}

// 模型调用积分定价
export const MODEL_PRICING: Record<string, number> = {
  'gpt-4.1-nano': 1,
  'claude-3-5-haiku-latest': 4,
  'gemini-2.0-flash': 2,
  // 未来扩展的模型定价
  'gpt-4o': 20,
  'claude-3-opus': 30,
  'gemini-pro': 10
}

// 注册奖励
export const REGISTRATION_REWARD = 100

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 用户操作结果
export interface UserOperationResult {
  success: boolean
  message: string
  data?: any
}
