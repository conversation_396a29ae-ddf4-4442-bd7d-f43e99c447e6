import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// 输入框变体样式定义
const inputVariants = cva(
  "flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "",
        error: "border-destructive focus-visible:ring-destructive",
        success: "border-green-500 focus-visible:ring-green-500",
      },
      size: {
        default: "h-10",
        sm: "h-9 px-2 text-xs",
        lg: "h-11 px-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  error?: string
  success?: string
  label?: string
  helperText?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type = "text", 
    variant, 
    size,
    error,
    success,
    label,
    helperText,
    id,
    ...props 
  }, ref) => {
    const inputId = id || React.useId()
    const hasError = Boolean(error)
    const hasSuccess = Boolean(success)
    
    // 根据状态确定变体
    const finalVariant = hasError ? "error" : hasSuccess ? "success" : variant

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        
        <input
          type={type}
          className={cn(inputVariants({ variant: finalVariant, size: size as "default" | "sm" | "lg" | null | undefined, className }))}
          ref={ref}
          id={inputId}
          {...props}
        />
        
        {/* 错误、成功或帮助文本 */}
        {(error || success || helperText) && (
          <div className="text-sm">
            {error && (
              <p className="text-destructive flex items-center gap-1">
                <span className="text-xs">⚠️</span>
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-green-600 flex items-center gap-1">
                <span className="text-xs">✅</span>
                {success}
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-muted-foreground">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input, inputVariants }
