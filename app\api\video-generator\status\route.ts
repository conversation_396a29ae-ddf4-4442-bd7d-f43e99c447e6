import { NextRequest, NextResponse } from 'next/server'

// 智谱AI CogVideoX-Flash API配置
const ZHIPU_API_KEY = process.env.ZHIPU_API_KEY
const ZHIPU_BASE_URL = process.env.ZHIPU_BASE_URL || 'https://open.bigmodel.cn/api/paas/v4'

/**
 * 视频生成状态查询API - GET请求处理
 */
export async function GET(request: NextRequest) {
  try {
    // 验证API配置
    if (!ZHIPU_API_KEY) {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 500 }
      )
    }

    // 从URL参数获取taskId
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('taskId')

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      )
    }

    // 调用智谱AI状态查询API
    const response = await fetch(`${ZHIPU_BASE_URL}/async-result/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ZHIPU_API_KEY}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Status API request failed: ${response.status}`)
    }

    const data = await response.json()

    // 返回状态信息
    return NextResponse.json({
      id: data.id,
      task_status: data.task_status,
      video_result: data.video_result || [],
      request_id: data.request_id,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    let errorMessage = 'Failed to check video status. Please try again.'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('404')) {
        errorMessage = 'Video task not found. Please start a new generation.'
        statusCode = 404
      } else if (error.message.includes('rate limit')) {
        errorMessage = 'Too many status checks. Please wait a moment.'
        statusCode = 429
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Status check timeout. Please try again.'
        statusCode = 504
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}

/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}
