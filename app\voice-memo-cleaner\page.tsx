import { PageLayout } from '@/components/layout/PageLayout'
import { VoiceMemoCleanerClient } from './VoiceMemoCleanerClient'
import type { Metadata } from 'next'

// Voice Memo Cleaner页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Voice Memo Cleaner - Clean Audio Transcripts | HumanWhisper',
  description: 'Transform messy voice recordings into clean transcripts. Remove filler words, repetitions, and errors. Free AI voice memo cleaner tool.',
  keywords: [
    'voice memo cleaner',
    'audio transcript',
    'speech to text',
    'voice recording',
    'audio cleaner',
    'transcript editor',
    'voice notes'
  ],
  openGraph: {
    title: 'AI Voice Memo Cleaner - Clean Audio Transcripts | HumanWhisper',
    description: 'Transform messy voice recordings into clean transcripts. Remove filler words, repetitions, and errors. Free AI voice memo cleaner tool.',
    url: '/voice-memo-cleaner',
    type: 'website',
  },
  twitter: {
    title: 'AI Voice Memo Cleaner - Clean Audio Transcripts | HumanWhisper',
    description: 'Transform messy voice recordings into clean transcripts. Remove filler words, repetitions, and errors. Free AI voice memo cleaner tool.',
  },
  alternates: {
    canonical: '/voice-memo-cleaner',
  },
}

export default function VoiceMemoCleanerPage() {
  return (
    <PageLayout>
      <VoiceMemoCleanerClient />
    </PageLayout>
  )
}
