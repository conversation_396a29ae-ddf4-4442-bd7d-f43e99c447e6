import { upgradeUserPlan, updateUserMetadata, getUserMetadata } from '@/lib/user-management'
import { 
  checkEventIdempotency, 
  markEventResult, 
  getEventStats 
} from '@/lib/webhook-idempotency'
import type { 
  CreemWebhookEvent, 
  CreemSubscription, 
  WebhookProcessResult, 
  WebhookContext 
} from '@/types/webhook'

/**
 * 从订阅元数据中获取用户ID
 */
function getUserIdFromSubscription(subscription: CreemSubscription): string | null {
  return subscription.metadata?.userId || null
}

/**
 * 处理订阅创建事件（首次付费成功）
 */
export async function handleSubscriptionCreated(
  event: CreemWebhookEvent,
  _context: WebhookContext
): Promise<WebhookProcessResult> {
  try {
    const subscription = event.data.object as CreemSubscription
    const userId = getUserIdFromSubscription(subscription)

    if (!userId) {
      return {
        success: false,
        message: 'No userId found in subscription metadata',
        processed: false
      }
    }

    // 升级用户计划
    await upgradeUserPlan(userId, 'premium', subscription.id)

    // 更新订阅相关信息
    await updateUserMetadata(userId, {
      creemCustomerId: subscription.customer,
      creemSubscriptionId: subscription.id,
      subscriptionStatus: subscription.status,
      subscriptionPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      subscriptionPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
    })

    return {
      success: true,
      message: `User ${userId} upgraded to premium successfully`,
      processed: true
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to process subscription.created: ${error instanceof Error ? error.message : String(error)}`,
      processed: false
    }
  }
}

/**
 * 处理订阅续费事件
 */
export async function handleSubscriptionRenewed(
  event: CreemWebhookEvent,
  _context: WebhookContext
): Promise<WebhookProcessResult> {
  try {
    const subscription = event.data.object as CreemSubscription
    const userId = getUserIdFromSubscription(subscription)

    if (!userId) {
      return {
        success: false,
        message: 'No userId found in subscription metadata',
        processed: false
      }
    }

    // 获取当前用户数据
    const currentMetadata = await getUserMetadata(userId)
    
    // 续费时重置积分，不保留剩余积分（防止积分累积）
    const newCredits = 3600 // 正式环境：3600积分

    // 更新订阅信息和积分
    await updateUserMetadata(userId, {
      subscriptionStatus: subscription.status,
      subscriptionPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      subscriptionPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      credits: newCredits,
      usedCredits: 0, // 重置已使用积分
      totalCreditsEarned: (currentMetadata.totalCreditsEarned || 0) + newCredits
    })

    return {
      success: true,
      message: `User ${userId} subscription renewed successfully`,
      processed: true
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to process subscription.renewed: ${error instanceof Error ? error.message : String(error)}`,
      processed: false
    }
  }
}

/**
 * 处理订阅取消事件
 */
export async function handleSubscriptionCanceled(
  event: CreemWebhookEvent,
  _context: WebhookContext
): Promise<WebhookProcessResult> {
  try {
    const subscription = event.data.object as CreemSubscription
    const userId = getUserIdFromSubscription(subscription)

    if (!userId) {
      return {
        success: false,
        message: 'No userId found in subscription metadata',
        processed: false
      }
    }

    // 更新订阅状态为取消，但保留到期时间（用户可使用到期末）
    await updateUserMetadata(userId, {
      subscriptionStatus: 'canceled',
      // 保留 subscriptionPeriodEnd，用户可使用到期末
    })

    // 注意：不立即降级用户，让用户使用到订阅期结束
    // 实际的降级应该通过定时任务在到期时处理

    return {
      success: true,
      message: `User ${userId} subscription canceled, access until ${subscription.current_period_end}`,
      processed: true
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to process subscription.canceled: ${error instanceof Error ? error.message : String(error)}`,
      processed: false
    }
  }
}

/**
 * 处理支付失败事件
 */
export async function handleSubscriptionPaymentFailed(
  event: CreemWebhookEvent,
  _context: WebhookContext
): Promise<WebhookProcessResult> {
  try {
    const subscription = event.data.object as CreemSubscription
    const userId = getUserIdFromSubscription(subscription)

    if (!userId) {
      return {
        success: false,
        message: 'No userId found in subscription metadata',
        processed: false
      }
    }

    // 支付失败，直接取消订阅权限
    await updateUserMetadata(userId, {
      subscriptionStatus: 'canceled'
    })

    return {
      success: true,
      message: `User ${userId} payment failed, subscription status updated to past_due`,
      processed: true
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to process subscription.payment_failed: ${error instanceof Error ? error.message : String(error)}`,
      processed: false
    }
  }
}

/**
 * 主要的webhook事件处理器
 */
export async function processWebhookEvent(event: CreemWebhookEvent): Promise<WebhookProcessResult> {
  const subscription = event.data.object as CreemSubscription
  const subscriptionId = subscription?.id
  const userId = getUserIdFromSubscription(subscription)

  // 如果没有用户ID，无法处理事件
  if (!userId) {
    return {
      success: false,
      message: 'No userId found in subscription metadata',
      processed: false
    }
  }

  // 高级幂等性检查
  const idempotencyCheck = await checkEventIdempotency(
    event.id,
    event.type,
    subscriptionId,
    userId
  )

  // 如果事件已处理，返回之前的结果
  if (idempotencyCheck.isProcessed) {
    const previousEvent = idempotencyCheck.previousEvent!
    return {
      success: previousEvent.result === 'success',
      message: `Event already processed at ${previousEvent.processedAt}`,
      processed: false
    }
  }

  // 如果检测到重复的订阅事件，防止重复处理
  if (idempotencyCheck.isDuplicate) {
    const duplicateEvent = idempotencyCheck.duplicateEvent!
    console.warn(`Duplicate subscription event detected:`, {
      currentEvent: event.id,
      eventType: event.type,
      subscriptionId,
      previousEvent: duplicateEvent.eventId,
      previousProcessedAt: duplicateEvent.processedAt
    })

    await markEventResult(event.id, event.type, 'success', {
      subscriptionId,
      userId,
      reason: 'duplicate_prevented',
      duplicateOf: duplicateEvent.eventId
    })

    return {
      success: true,
      message: `Duplicate event prevented, similar event ${duplicateEvent.eventId} processed at ${duplicateEvent.processedAt}`,
      processed: false
    }
  }

  const context: WebhookContext = {
    eventId: event.id,
    eventType: event.type as any,
    timestamp: event.created,
    userId
  }

  let result: WebhookProcessResult

  try {
    // 根据事件类型分发处理
    switch (event.type) {
      case 'subscription.created':
        result = await handleSubscriptionCreated(event, context)
        break
      
      case 'subscription.renewed':
        result = await handleSubscriptionRenewed(event, context)
        break
      
      case 'subscription.canceled':
        result = await handleSubscriptionCanceled(event, context)
        break
      
      case 'subscription.payment_failed':
        result = await handleSubscriptionPaymentFailed(event, context)
        break
      
      default:
        result = {
          success: true,
          message: `Unhandled event type: ${event.type}`,
          processed: false
        }
    }

    // 记录处理结果
    await markEventResult(
      event.id, 
      event.type, 
      result.success ? 'success' : 'failed',
      {
        subscriptionId,
        userId,
        processed: result.processed,
        message: result.message
      }
    )

    // 记录统计信息
    if (result.processed) {
      const stats = getEventStats()
      console.log(`Webhook processing stats:`, stats)
    }

    return result

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    
    // 记录失败结果
    await markEventResult(event.id, event.type, 'failed', {
      subscriptionId,
      userId,
      error: errorMessage
    })

    return {
      success: false,
      message: `Webhook processing failed: ${errorMessage}`,
      processed: false
    }
  }
}
