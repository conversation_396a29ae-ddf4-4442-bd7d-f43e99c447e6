import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const { url } = body

    // 输入验证
    if (!url?.trim()) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }

    // 验证URL格式
    let validUrl: URL
    try {
      validUrl = new URL(url.trim())
      if (!['http:', 'https:'].includes(validUrl.protocol)) {
        throw new Error('Invalid protocol')
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      )
    }

    // 设置请求头模拟真实浏览器
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    }

    // 尝试获取网页内容
    const response = await fetch(validUrl.toString(), {
      method: 'GET',
      headers,
      // 设置超时时间
      signal: AbortSignal.timeout(15000), // 15秒超时
    })

    // 检查响应状态
    if (!response.ok) {
      if (response.status === 403) {
        return NextResponse.json(
          { error: 'Access forbidden - website has anti-scraping protection' },
          { status: 403 }
        )
      }
      if (response.status === 404) {
        return NextResponse.json(
          { error: 'Page not found' },
          { status: 404 }
        )
      }
      if (response.status === 429) {
        return NextResponse.json(
          { error: 'Rate limited - too many requests' },
          { status: 429 }
        )
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 获取内容类型
    const contentType = response.headers.get('content-type') || ''
    if (!contentType.includes('text/html')) {
      return NextResponse.json(
        { error: 'URL does not point to an HTML page' },
        { status: 400 }
      )
    }

    // 读取HTML内容
    const html = await response.text()

    // 简单的HTML文本提取（移除标签）
    const extractTextFromHtml = (html: string): string => {
      // 移除script和style标签及其内容
      let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      
      // 移除HTML标签
      text = text.replace(/<[^>]*>/g, ' ')
      
      // 解码HTML实体
      text = text.replace(/&nbsp;/g, ' ')
      text = text.replace(/&amp;/g, '&')
      text = text.replace(/&lt;/g, '<')
      text = text.replace(/&gt;/g, '>')
      text = text.replace(/&quot;/g, '"')
      text = text.replace(/&#39;/g, "'")
      
      // 清理空白字符
      text = text.replace(/\s+/g, ' ')
      text = text.trim()
      
      return text
    }

    const extractedText = extractTextFromHtml(html)

    // 检查提取的内容长度
    if (extractedText.length < 100) {
      return NextResponse.json(
        { error: 'Insufficient content extracted from the page' },
        { status: 400 }
      )
    }

    // 限制内容长度（避免过长的文档）
    const maxLength = 50000 // 50K字符限制
    const finalContent = extractedText.length > maxLength 
      ? extractedText.substring(0, maxLength) + '...[Content truncated]'
      : extractedText

    return NextResponse.json({
      content: finalContent,
      metadata: {
        url: validUrl.toString(),
        contentLength: finalContent.length,
        timestamp: new Date().toISOString(),
        title: extractTitleFromHtml(html)
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error.name === 'AbortError' || error.name === 'TimeoutError') {
      return NextResponse.json(
        { error: 'Request timeout - website took too long to respond' },
        { status: 408 }
      )
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return NextResponse.json(
        { error: 'Cannot connect to the website' },
        { status: 502 }
      )
    }

    if (error.message?.includes('fetch')) {
      return NextResponse.json(
        { error: 'Failed to fetch content from URL' },
        { status: 502 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to fetch webpage content' },
      { status: 500 }
    )
  }
}

// 辅助函数：从HTML中提取标题
function extractTitleFromHtml(html: string): string {
  const titleMatch = html.match(/<title[^>]*>([\s\S]*?)<\/title>/i)
  if (titleMatch && titleMatch[1]) {
    return titleMatch[1].replace(/<[^>]*>/g, '').trim()
  }
  return 'Untitled'
}
