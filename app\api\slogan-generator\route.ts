import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 行业映射
const INDUSTRY_MAP: Record<string, string> = {
  'saas': 'SaaS and technology products',
  'consumer': 'consumer products and retail',
  'web3': 'Web3, blockchain, and cryptocurrency',
  'nonprofit': 'non-profit and charitable organizations',
  'healthcare': 'healthcare and wellness services',
  'education': 'education and learning platforms',
  'finance': 'financial services and fintech',
  'ecommerce': 'e-commerce and online retail',
  'food': 'food and beverage industry'
}

// 调性映射
const TONE_MAP: Record<string, string> = {
  'minimal': 'minimal, clean, and elegant',
  'humorous': 'humorous, fun, and playful',
  'premium': 'premium, luxury, and sophisticated',
  'rebellious': 'rebellious, bold, and disruptive',
  'friendly': 'friendly, warm, and approachable',
  'professional': 'professional, serious, and trustworthy',
  'innovative': 'innovative, cutting-edge, and forward-thinking',
  'inspiring': 'inspiring, motivational, and empowering'
}

// 构建口号生成prompt
function buildSloganPrompt(
  industry: string,
  tone: string,
  productDescription: string,
  targetAudience: string,
  keywords: string
): string {
  const industryDesc = INDUSTRY_MAP[industry] || industry
  const toneDesc = TONE_MAP[tone] || tone

  const prompt = `You are a creative brand strategist and copywriter specializing in memorable slogans and taglines. Your task is to create compelling brand slogans that capture attention and communicate value.

**Brand Context:**
- Industry: ${industryDesc}
- Brand Tone: ${toneDesc}
- Product: ${productDescription}
- Target Audience: ${targetAudience}
- Key Brand Values: ${keywords}

**Instructions:**
Generate exactly 10 unique English slogans that:
1. Are memorable and catchy (3-7 words ideal)
2. Reflect the specified brand tone and industry
3. Appeal to the target audience
4. Incorporate or reflect the key brand values
5. Are suitable for marketing materials and branding
6. Avoid clichés and overused phrases
7. Are original and creative

For each slogan, also provide:
- A brief explanation of why it works
- A suggested domain name (without .com, just the name part)

**Output Format:**
Return a valid JSON array with exactly 10 objects:

[
  {
    "slogan": "Your Creative Slogan Here",
    "explanation": "Brief explanation of why this slogan works for the brand",
    "domainSuggestion": "suggesteddomainname"
  }
]

**Important Guidelines:**
- Keep slogans concise and impactful
- Ensure domain suggestions are brandable and memorable
- Make each slogan unique and distinct
- Consider trademark-ability and uniqueness
- Focus on emotional connection and brand differentiation
- Ensure JSON format is valid and properly escaped

Generate the 10 brand slogans now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      industry,
      tone,
      productDescription,
      targetAudience,
      keywords,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!industry || !tone || !productDescription?.trim() || !targetAudience?.trim() || !keywords?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildSloganPrompt(
      industry,
      tone,
      productDescription.trim(),
      targetAudience.trim(),
      keywords.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let slogans: Array<{slogan: string, explanation: string, domainSuggestion: string}>
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      slogans = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!Array.isArray(slogans)) {
        throw new Error('Response is not an array')
      }
      
      // 验证每个口号项目
      slogans = slogans.filter(slogan => 
        slogan && 
        typeof slogan.slogan === 'string' && 
        typeof slogan.explanation === 'string' &&
        typeof slogan.domainSuggestion === 'string' &&
        slogan.slogan.trim() && 
        slogan.explanation.trim() &&
        slogan.domainSuggestion.trim()
      )
      
      if (slogans.length === 0) {
        throw new Error('No valid slogan items found')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取口号
      const lines = generatedContent.split('\n').filter(line => line.trim())
      slogans = []
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]?.trim()
        if (line && (line.includes('"') || line.includes(':'))) {
          // 尝试提取口号信息
          const sloganMatch = line.match(/"([^"]+)"/g)
          if (sloganMatch && sloganMatch.length >= 1) {
            slogans.push({
              slogan: sloganMatch[0].replace(/"/g, ''),
              explanation: 'Creative slogan for your brand',
              domainSuggestion: sloganMatch[0].replace(/"/g, '').toLowerCase().replace(/[^a-z0-9]/g, '')
            })
          }
        }
      }
      
      if (slogans.length === 0) {
        throw new Error('Failed to parse slogan content')
      }
    }

    // 确保域名建议格式正确
    slogans = slogans.map(slogan => ({
      ...slogan,
      domainSuggestion: slogan.domainSuggestion
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20) // 限制长度
    }))

    return NextResponse.json({
      slogans: slogans.slice(0, 10), // 确保不超过10个
      metadata: {
        industry,
        tone,
        sloganCount: slogans.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate slogans' },
      { status: 500 }
    )
  }
}
