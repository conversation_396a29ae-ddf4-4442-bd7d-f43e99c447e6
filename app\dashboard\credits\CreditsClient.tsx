'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'
import Link from 'next/link'
import {
  Coins,
  Crown,
  ArrowLeft,
  MessageSquare,
  TrendingUp,
  Calendar,
  CreditCard,
  Bot,
  Sparkles
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MODEL_PRICING } from '@/types/user'

interface UserStats {
  plan: 'free' | 'vip'
  credits: number
  usedCredits: number
  subscriptionPeriodEnd?: string
}

export function CreditsClient() {
  const { user } = useUser()
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch('/api/user/stats')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        }
      } catch (error) {
        console.error('Failed to fetch user stats:', error)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      fetchUserStats()
    }
  }, [user])

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-20 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Unable to load credit information</p>
          <Link href="/dashboard">
            <Button className="mt-4">Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  const remainingCredits = stats.credits - stats.usedCredits
  const isVip = stats.plan === 'vip'
  const usagePercentage = stats.credits > 0 ? (stats.usedCredits / stats.credits) * 100 : 0

  return (
    <div className="min-h-screen bg-background">
      <main className="max-w-4xl mx-auto p-6 space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center gap-4">
          <Link href="/dashboard">
            <Button variant="ghost">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Credits Details</h1>
        </div>
        {/* 积分概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Coins className="h-5 w-5 text-yellow-500" />
                Current Balance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary mb-2">
                {remainingCredits}
              </div>
              <div className="w-full bg-muted rounded-full h-2 mb-2">
                <div
                  className="bg-primary rounded-full h-2 transition-all"
                  style={{ width: `${Math.max(0, 100 - usagePercentage)}%` }}
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Remaining {((100 - usagePercentage)).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <TrendingUp className="h-5 w-5 text-green-500" />
                Used Credits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {stats.usedCredits}
              </div>
              <p className="text-sm text-muted-foreground">
                Credits consumed this month
              </p>
            </CardContent>
          </Card>


        </div>

        {/* 会员信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isVip ? <Crown className="h-5 w-5 text-yellow-500" /> : <Calendar className="h-5 w-5" />}
              Membership Info
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge
                    variant={isVip ? "default" : "secondary"}
                    className={isVip ? "bg-gradient-to-r from-purple-500 to-blue-500" : ""}
                  >
                    {isVip ? "VIP Member" : "Free User"}
                  </Badge>
                  {isVip && (
                    <span className="text-sm text-muted-foreground">
                      3600 credits/month
                    </span>
                  )}
                </div>
                {isVip && stats.subscriptionPeriodEnd && (
                  <p className="text-sm text-muted-foreground">
                    Expires: {new Date(stats.subscriptionPeriodEnd).toLocaleDateString()}
                  </p>
                )}
                {!isVip && (
                  <p className="text-sm text-muted-foreground">
                    Get 100 credits on signup, upgrade to Premium for more
                  </p>
                )}
              </div>
              {!isVip && (
                <Link href="/dashboard">
                  <Button>
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade to Premium
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 模型定价表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-blue-500" />
              Model Credit Costs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(MODEL_PRICING).map(([model, credits]) => {
                const isAvailable = isVip || ['gpt-4.1-nano', 'claude-3-5-haiku-latest', 'gemini-2.0-flash'].includes(model)
                
                return (
                  <div key={model} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        model.includes('gpt') ? 'bg-green-100 text-green-600' :
                        model.includes('claude') ? 'bg-purple-100 text-purple-600' :
                        'bg-blue-100 text-blue-600'
                      }`}>
                        <Bot className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium">{model}</div>
                        <div className="text-sm text-muted-foreground">
                          {isAvailable ? 'Available' : 'VIP Only'}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{credits}</span>
                      <Coins className="h-4 w-4 text-yellow-500" />
                      {!isAvailable && (
                        <Badge variant="secondary" className="ml-2">
                          <Crown className="h-3 w-3 mr-1" />
                          VIP
                        </Badge>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* 积分获取方式 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-yellow-500" />
              How to Get Credits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <Sparkles className="h-8 w-8 text-green-500" />
                <div>
                  <div className="font-medium">Signup Bonus</div>
                  <div className="text-sm text-muted-foreground">Get 100 credits on registration</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <CreditCard className="h-8 w-8 text-purple-500" />
                <div>
                  <div className="font-medium">VIP Membership</div>
                  <div className="text-sm text-muted-foreground">Get 3600 credits monthly</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 快速操作 */}
        <div className="flex gap-4">
          <Link href="/chat">
            <Button className="flex-1">
              <MessageSquare className="h-4 w-4 mr-2" />
              Start Chat
            </Button>
          </Link>
          <Link href="/dashboard">
            <Button variant="outline" className="flex-1">
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </main>
    </div>
  )
}
