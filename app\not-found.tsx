import Link from 'next/link'
import { PageLayout } from '@/components/layout/PageLayout'
import type { Metadata } from 'next'

// 404页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Page Not Found - HumanWhisper',
  description: 'Sorry, the page you are looking for does not exist. Return to homepage to continue exploring HumanWhisper AI features.',
  robots: {
    index: false,
    follow: false,
  },
}

export default function NotFound() {
  return (
    <PageLayout>
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* 404 大标题 */}
          <div className="mb-8">
            <h1 className="text-8xl md:text-9xl font-bold text-slate-200 mb-4">
              404
            </h1>
            <div className="relative">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Page Not Found
              </h2>
              <div className="absolute -top-2 -right-4 text-2xl">
                🤔
              </div>
            </div>
          </div>

          {/* 描述文字 */}
          <div className="mb-8">
            <p className="text-lg text-gray-600 mb-4">
              Sorry, the page you are looking for does not exist or has been moved.
            </p>
            <p className="text-gray-500">
              Don't worry, let us help you find the right direction!
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link
              href="/"
              className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🏠 Back to Home
            </Link>
            <Link
              href="/chat"
              className="inline-flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              💬 Start Chat
            </Link>
          </div>

          {/* 推荐功能 */}
          <div className="grid md:grid-cols-3 gap-4 mb-8">
            <div className="p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="text-center">
                <div className="text-2xl mb-3">💬</div>
                <h3 className="font-semibold mb-2">AI Chat Assistant</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Making complex topics simple and understandable
                </p>
                <Link href="/chat" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  Try Now →
                </Link>
              </div>
            </div>

            <div className="p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="text-center">
                <div className="text-2xl mb-3">✨</div>
                <h3 className="font-semibold mb-2">AI Creative Tools</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Logo generation, video creation, prompt optimization
                </p>
                <Link href="/logo-generator" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  Explore Tools →
                </Link>
              </div>
            </div>

            <div className="p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="text-center">
                <div className="text-2xl mb-3">🔍</div>
                <h3 className="font-semibold mb-2">Features</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Learn about all HumanWhisper features
                </p>
                <Link href="/features" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  View Features →
                </Link>
              </div>
            </div>
          </div>

          {/* 返回链接 */}
          <div className="text-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              ← Go Back
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
