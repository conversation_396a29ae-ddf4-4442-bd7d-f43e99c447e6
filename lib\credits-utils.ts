/**
 * 客户端积分计算工具
 * 不依赖服务端组件，可在客户端安全使用
 */

import { MODEL_PRICING } from '@/types/user'

/**
 * 计算图像处理所需的额外积分
 */
export function getImageCredits(modelName: string, imageCount: number): number {
  if (imageCount === 0) return 0

  // 不同模型的图像处理积分
  const imageCreditsPerModel: Record<string, number> = {
    'gpt-4.1-nano': 1,           // 每张图片1积分
    'claude-3-5-haiku-latest': 2, // 每张图片2积分
    'gemini-2.0-flash': 1,       // 每张图片1积分
  }

  const creditsPerImage = imageCreditsPerModel[modelName] || 1
  return imageCount * creditsPerImage
}



/**
 * 获取模型基础积分（不含图像）
 */
export function getBaseModelCredits(modelName: string): number {
  return MODEL_PRICING[modelName] || 1
}

/**
 * 获取模型调用所需积分（包含图像处理）
 */
export function getModelCredits(modelName: string, imageCount: number = 0): number {
  const baseCredits = getBaseModelCredits(modelName)
  const imageCredits = getImageCredits(modelName, imageCount)
  return baseCredits + imageCredits
}

/**
 * 计算总积分消耗（用于前端显示）
 */
export function calculateTotalCredits(modelName: string, imageCount: number = 0): {
  baseCredits: number
  imageCredits: number
  totalCredits: number
} {
  const baseCredits = getBaseModelCredits(modelName)
  const imageCredits = getImageCredits(modelName, imageCount)
  const totalCredits = baseCredits + imageCredits

  return {
    baseCredits,
    imageCredits,
    totalCredits
  }
}
