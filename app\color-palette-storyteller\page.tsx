import { PageLayout } from '@/components/layout/PageLayout'
import { ColorPaletteStorytellerClient } from './ColorPaletteStorytellerClient'
import type { Metadata } from 'next'

// Color Palette Storyteller页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Color Palette Storyteller - Design Color Stories | HumanWhisper',
  description: 'Generate professional color palette explanations for clients. Input colors and emotions, get design rationale with color psychology and usage tips.',
  keywords: [
    'color palette',
    'color psychology',
    'design explanation',
    'color theory',
    'brand colors',
    'design rationale',
    'color storytelling'
  ],
  openGraph: {
    title: 'AI Color Palette Storyteller - Design Color Stories | HumanWhisper',
    description: 'Generate professional color palette explanations for clients. Input colors and emotions, get design rationale with color psychology and usage tips.',
    url: '/color-palette-storyteller',
    type: 'website',
  },
  twitter: {
    title: 'AI Color Palette Storyteller - Design Color Stories | HumanWhisper',
    description: 'Generate professional color palette explanations for clients. Input colors and emotions, get design rationale with color psychology and usage tips.',
  },
  alternates: {
    canonical: '/color-palette-storyteller',
  },
}

export default function ColorPaletteStorytellerPage() {
  return (
    <PageLayout>
      <ColorPaletteStorytellerClient />
    </PageLayout>
  )
}
