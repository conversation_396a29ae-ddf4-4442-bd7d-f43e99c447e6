import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { getEventStats, getUserEventHistory } from '@/lib/webhook-idempotency'

/**
 * 获取webhook处理统计信息
 * 仅限管理员访问
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 这里可以添加管理员权限检查
    // 暂时允许所有认证用户访问统计信息

    const { searchParams } = new URL(request.url)
    const userIdParam = searchParams.get('userId')

    // 获取整体统计
    const stats = getEventStats()

    let userHistory = null
    if (userIdParam) {
      userHistory = await getUserEventHistory(userIdParam, 20)
    }

    return NextResponse.json({
      success: true,
      stats,
      userHistory,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Webhook stats error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get webhook stats',
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}