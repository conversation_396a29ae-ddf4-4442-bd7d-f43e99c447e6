'use client'


import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, MessageCircle } from 'lucide-react'
import Link from 'next/link'

export default function GlobalError({
  reset,
}: {
  reset: () => void
}) {

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center px-4 bg-background">
          <div className="max-w-md mx-auto text-center">
            <Card>
              <CardHeader>
                <div className="flex justify-center mb-4">
                  <AlertTriangle className="h-16 w-16 text-destructive" />
                </div>
                <CardTitle className="text-2xl">Something went wrong</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  Sorry, the application encountered an unexpected error. We have logged this issue and are working to fix it.
                </p>

                <div className="flex flex-col gap-3">
                  <Button onClick={reset} className="gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </Button>

                  <div className="flex gap-2">
                    <Button asChild variant="outline" className="flex-1 gap-2">
                      <Link href="/">
                        <Home className="h-4 w-4" />
                        Home
                      </Link>
                    </Button>

                    <Button asChild variant="outline" className="flex-1 gap-2">
                      <Link href="/chat">
                        <MessageCircle className="h-4 w-4" />
                        Chat
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </body>
    </html>
  )
}
