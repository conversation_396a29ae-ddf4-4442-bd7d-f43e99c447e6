import { NextRequest, NextResponse } from 'next/server'

/**
 * 视频下载代理API - POST请求处理
 * 解决CORS跨域问题，通过后端代理下载智谱AI生成的视频文件
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const { videoUrl }: { videoUrl: string } = body

    // 输入验证
    if (!videoUrl || typeof videoUrl !== 'string') {
      return NextResponse.json(
        { error: 'Video URL is required' },
        { status: 400 }
      )
    }

    // 验证URL是否来自智谱AI的域名（安全检查）
    if (!videoUrl.includes('aigc-files.bigmodel.cn')) {
      return NextResponse.json(
        { error: 'Invalid video URL' },
        { status: 400 }
      )
    }

    // 通过后端获取视频文件
    const response = await fetch(videoUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'HumanWhisper/1.0',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch video: ${response.status}`)
    }

    // 获取文件内容
    const videoBuffer = await response.arrayBuffer()
    
    // 返回视频文件，设置适当的响应头
    return new NextResponse(videoBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Disposition': 'attachment; filename="generated-video.mp4"',
        'Content-Length': videoBuffer.byteLength.toString(),
        'Cache-Control': 'no-cache',
      },
    })

  } catch (error) {
    let errorMessage = 'Failed to download video. Please try again.'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('404')) {
        errorMessage = 'Video file not found. It may have expired.'
        statusCode = 404
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Download timeout. Please try again.'
        statusCode = 504
      } else if (error.message.includes('network')) {
        errorMessage = 'Network error. Please check your connection.'
        statusCode = 503
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}

/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}
