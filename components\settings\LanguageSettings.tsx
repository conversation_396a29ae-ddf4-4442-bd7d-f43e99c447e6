'use client'

import React from 'react'
import { Globe, MessageSquare, Target, Sparkles } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { useSettingsStore } from '@/store/settings-store'
import { useChatStore } from '@/store/chat-store'
import { 
  SUPPORTED_LANGUAGES, 
  RESPONSE_LENGTH_CONFIG, 
  TARGET_AGES 
} from '@/lib/constants'
import type { Language, ResponseLength } from '@/types'

export function LanguageSettings() {
  const { userSettings, updateUserSettings } = useSettingsStore()
  const { updateSettings } = useChatStore()

  const handleLanguageChange = (language: Language) => {
    updateUserSettings({ language })
    updateSettings({ language })
  }

  const handleResponseLengthChange = (responseLength: ResponseLength) => {
    updateUserSettings({ responseLength })
    updateSettings({ responseLength })
  }

  const handleTargetAgeChange = (targetAge: number) => {
    updateUserSettings({ targetAge })
    updateSettings({ targetAge })
  }

  const handleToggleExamples = (includeExamples: boolean) => {
    updateUserSettings({ includeExamples })
    updateSettings({ includeExamples })
  }

  const handleToggleAnalogies = (useAnalogies: boolean) => {
    updateUserSettings({ useAnalogies })
    updateSettings({ useAnalogies })
  }

  return (
    <div className="space-y-6">
      {/* Language Preference */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Globe className="h-5 w-5 text-blue-500" />
            <CardTitle>Language Preference</CardTitle>
          </div>
          <CardDescription>
            Choose the language and cultural background for AI responses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Response Language
              </label>
              <Select 
                value={userSettings.language} 
                onValueChange={handleLanguageChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <div className="flex items-center space-x-2">
                        <span>{lang.nativeName}</span>
                        <span className="text-muted-foreground">({lang.name})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Language Features Description */}
            <div className="p-3 rounded-lg bg-muted/50">
              <h4 className="text-sm font-medium mb-2">Current Language Features</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                {userSettings.language === 'zh' && (
                  <>
                    <p>• Uses Chinese cultural background examples and analogies</p>
                    <p>• Expression style suitable for Chinese users</p>
                    <p>• Includes Chinese thinking and traditional cultural elements</p>
                  </>
                )}
                {userSettings.language === 'en' && (
                  <>
                    <p>• Uses Western cultural references</p>
                    <p>• Conversational American English tone</p>
                    <p>• Includes familiar daily life examples</p>
                  </>
                )}
                {userSettings.language === 'ja' && (
                  <>
                    <p>• Uses examples and metaphors rooted in Japanese culture</p>
                    <p>• Polite and humble Japanese communication style</p>
                    <p>• Includes seasonal feelings and Japanese aesthetic sense</p>
                  </>
                )}
                {!['zh', 'en', 'ja'].includes(userSettings.language) && (
                  <p>• Adapts to the cultural background and expression habits of that language</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Response Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-green-500" />
            <CardTitle>Response Settings</CardTitle>
          </div>
          <CardDescription>
            Control the detail level and style of AI responses
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Response Length */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Response Length
            </label>
            <Select 
              value={userSettings.responseLength} 
              onValueChange={handleResponseLengthChange}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(RESPONSE_LENGTH_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center space-x-2">
                      <span>{config?.icon || '📝'}</span>
                      <div>
                        <div className="font-medium">{config?.label || key}</div>
                        <div className="text-xs text-muted-foreground">
                          {config?.description || 'Response configuration'}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Target Understanding Level */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Understanding Level
            </label>
            <Select 
              value={userSettings.targetAge.toString()} 
              onValueChange={(value) => handleTargetAgeChange(parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TARGET_AGES.map((age) => (
                  <SelectItem key={age.value} value={age.value.toString()}>
                    <div>
                      <div className="font-medium">{age.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {age.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Features */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            <CardTitle>Enhanced Features</CardTitle>
          </div>
          <CardDescription>
            Make AI responses more vivid and interesting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Include Examples */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Include Specific Examples</span>
                <Badge variant="secondary" className="text-xs">Recommended</Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                Use specific examples to illustrate abstract concepts for easier understanding
              </div>
            </div>
            <Switch
              checked={userSettings.includeExamples}
              onCheckedChange={handleToggleExamples}
            />
          </div>

          {/* Use Analogies */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Use Life Analogies</span>
                <Badge variant="secondary" className="text-xs">Recommended</Badge>
              </div>
              <div className="text-xs text-muted-foreground">
                Use everyday objects as metaphors to make complex concepts easier to understand
              </div>
            </div>
            <Switch
              checked={userSettings.useAnalogies}
              onCheckedChange={handleToggleAnalogies}
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Configuration Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-orange-500" />
            <CardTitle>Current Configuration</CardTitle>
          </div>
          <CardDescription>
            Summary of your language and response preference settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            <div className="space-y-2">
              <div className="text-sm font-medium">Language Settings</div>
              <div className="flex flex-wrap gap-1">
                <Badge variant="outline">
                  {SUPPORTED_LANGUAGES.find(l => l.code === userSettings.language)?.nativeName}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Response Style</div>
              <div className="flex flex-wrap gap-1">
                <Badge variant="outline">
                  {RESPONSE_LENGTH_CONFIG[userSettings.responseLength]?.label || userSettings.responseLength}
                </Badge>
                <Badge variant="outline">
                  {TARGET_AGES.find(a => a.value === userSettings.targetAge)?.label}
                </Badge>
              </div>
            </div>

            <div className="space-y-2 md:col-span-2">
              <div className="text-sm font-medium">Enhanced Features</div>
              <div className="flex flex-wrap gap-1">
                {userSettings.includeExamples && (
                  <Badge variant="secondary">Include Examples</Badge>
                )}
                {userSettings.useAnalogies && (
                  <Badge variant="secondary">Use Analogies</Badge>
                )}
                {!userSettings.includeExamples && !userSettings.useAnalogies && (
                  <Badge variant="outline">Basic Mode</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Configuration Suggestions */}
          <div className="mt-4 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
            <div className="text-sm">
              <div className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                💡 Configuration Suggestions
              </div>
              <div className="text-blue-600 dark:text-blue-400 text-xs">
                {userSettings.targetAge <= 12
                  ? 'Recommend enabling "Include Examples" and "Use Analogies" to make explanations more vivid and interesting'
                  : userSettings.targetAge >= 18
                  ? 'You can choose more detailed response lengths for deeper explanations'
                  : 'Current configuration is great for learning new knowledge, balancing depth and comprehensibility'
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
