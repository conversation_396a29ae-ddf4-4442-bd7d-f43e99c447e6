'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  Wand2, 
  Palette, 
  Video, 
  Mail, 
  Mic, 
  HelpCircle, 
  FileText, 
  Megaphone, 
  Heart, 
  Gamepad2, 
  Calendar,
  Rocket,
  Smile,
  MessageCircle,
  ArrowRight,
  Sparkles,
  Zap,
  Target
} from 'lucide-react'
import Link from 'next/link'

// 工具分类定义
const TOOL_CATEGORIES = [
  {
    id: 'content-creation',
    name: 'Content Creation',
    description: 'AI-powered tools for writing, editing, and content optimization',
    icon: FileText,
    color: 'blue'
  },
  {
    id: 'design-visual',
    name: 'Design & Visual',
    description: 'Creative tools for logos, colors, and visual content',
    icon: Palette,
    color: 'purple'
  },
  {
    id: 'marketing-social',
    name: 'Marketing & Social',
    description: 'Tools for social media, advertising, and brand promotion',
    icon: Megaphone,
    color: 'green'
  },
  {
    id: 'communication',
    name: 'Communication',
    description: 'Email, messaging, and conversation enhancement tools',
    icon: MessageCircle,
    color: 'orange'
  },
  {
    id: 'entertainment-games',
    name: 'Entertainment & Games',
    description: 'Fun tools for games, challenges, and creative projects',
    icon: Gamepad2,
    color: 'red'
  },
  {
    id: 'productivity',
    name: 'Productivity',
    description: 'Tools to boost efficiency and streamline workflows',
    icon: Target,
    color: 'indigo'
  }
]

// 所有工具定义
const ALL_TOOLS = [
  // Content Creation
  {
    id: 'prompt-optimizer',
    name: 'Prompt Optimizer',
    description: 'Optimize AI prompts for better results and efficiency',
    href: '/prompt-optimizer',
    category: 'content-creation',
    icon: Wand2,
    featured: true
  },
  {
    id: 'voice-memo-cleaner',
    name: 'Voice Memo Cleaner',
    description: 'Clean up voice recordings by removing filler words and improving clarity',
    href: '/voice-memo-cleaner',
    category: 'content-creation',
    icon: Mic
  },
  {
    id: 'faq-generator',
    name: 'FAQ Auto-Generator',
    description: 'Automatically generate comprehensive FAQ sections for your content',
    href: '/faq-generator',
    category: 'content-creation',
    icon: HelpCircle
  },
  {
    id: 'policy-tldr',
    name: 'AI Policy TL;DR',
    description: 'Summarize complex policies and legal documents into digestible insights',
    href: '/policy-tldr',
    category: 'content-creation',
    icon: FileText
  },
  {
    id: 'podcast-title-brainstorm',
    name: 'Podcast Episode Title Brainstorm',
    description: 'Generate catchy podcast episode titles with SEO optimization',
    href: '/podcast-title-brainstorm',
    category: 'content-creation',
    icon: Mic
  },

  // Design & Visual
  {
    id: 'logo-generator',
    name: 'Logo Generator',
    description: 'Create professional logos with AI-powered design assistance',
    href: '/logo-generator',
    category: 'design-visual',
    icon: Palette,
    featured: true
  },
  {
    id: 'video-generator',
    name: 'Video Generator',
    description: 'Generate video content and scripts with AI assistance',
    href: '/video-generator',
    category: 'design-visual',
    icon: Video,
    featured: true
  },
  {
    id: 'color-palette-storyteller',
    name: 'Color Palette Storyteller',
    description: 'Create compelling stories and meanings behind color palettes',
    href: '/color-palette-storyteller',
    category: 'design-visual',
    icon: Palette
  },

  // Marketing & Social
  {
    id: 'slogan-generator',
    name: 'Brand Slogan Storm',
    description: 'Generate memorable brand slogans and taglines',
    href: '/slogan-generator',
    category: 'marketing-social',
    icon: Megaphone
  },
  {
    id: 'emoji-ad-copy',
    name: 'Emoji Ad Copy',
    description: 'Create engaging social media ad copy with strategic emoji placement',
    href: '/emoji-ad-copy',
    category: 'marketing-social',
    icon: Smile
  },
  {
    id: 'flash-landing-page',
    name: 'Flash Landing Page',
    description: 'Generate complete coming soon landing pages in minutes',
    href: '/flash-landing-page',
    category: 'marketing-social',
    icon: Rocket
  },
  {
    id: '404-limerick',
    name: '404 Page Limerick',
    description: 'Create creative 404 error pages with humorous limericks',
    href: '/404-limerick',
    category: 'marketing-social',
    icon: Smile
  },

  // Communication
  {
    id: 'email-reply-generator',
    name: 'Email Reply Generator',
    description: 'Generate professional email responses quickly and efficiently',
    href: '/email-reply-generator',
    category: 'communication',
    icon: Mail
  },
  {
    id: 'anti-cliche-love-letter',
    name: 'Anti-Cliche Love Letter',
    description: 'Write personalized love letters that avoid common cliches',
    href: '/anti-cliche-love-letter',
    category: 'communication',
    icon: Heart
  },

  // Entertainment & Games
  {
    id: 'micro-game-concept',
    name: 'Micro-Game Concept Card',
    description: 'Generate complete micro-game concepts for Game Jams',
    href: '/micro-game-concept',
    category: 'entertainment-games',
    icon: Gamepad2
  },

  // Productivity
  {
    id: '30-day-challenge',
    name: '30-Day Challenge Idea',
    description: 'Create engaging 30-day challenges for community building',
    href: '/30-day-challenge',
    category: 'productivity',
    icon: Calendar
  }
]

export function MoreToolsClient() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // 过滤工具
  const filteredTools = ALL_TOOLS.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = !selectedCategory || tool.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // 获取分类颜色
  const getCategoryColor = (categoryId: string) => {
    const category = TOOL_CATEGORIES.find(cat => cat.id === categoryId)
    return category?.color || 'gray'
  }

  // 获取特色工具
  const featuredTools = ALL_TOOLS.filter(tool => tool.featured)

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                More AI Tools
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Complete Collection
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Explore our <strong>complete collection of AI-powered tools</strong> for content creation, 
              design, marketing, and productivity. Find the perfect tool for your needs.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                {ALL_TOOLS.length}+ Tools
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Target className="h-4 w-4 mr-1" />
                All Categories
              </Badge>
            </div>

            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Tools */}
      {!searchQuery && !selectedCategory && (
        <section className="py-16 bg-background">
          <div className="container">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Featured Tools</h2>
              <p className="text-xl text-muted-foreground">
                Our most popular and powerful AI tools
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {featuredTools.map((tool) => {
                const IconComponent = tool.icon
                return (
                  <Card key={tool.id} className="group hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`h-10 w-10 bg-${getCategoryColor(tool.category)}-100 dark:bg-${getCategoryColor(tool.category)}-900 rounded-lg flex items-center justify-center`}>
                          <IconComponent className={`h-5 w-5 text-${getCategoryColor(tool.category)}-600`} />
                        </div>
                        <Badge variant="secondary" className="text-xs">Featured</Badge>
                      </div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">{tool.description}</p>
                      <Link href={tool.href}>
                        <Button className="w-full group-hover:bg-primary/90">
                          Try Now
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>
      )}

      {/* Categories */}
      <section className="py-16 bg-muted/30">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Browse by Category</h2>
            <p className="text-xl text-muted-foreground">
              Find tools organized by their primary use case
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {TOOL_CATEGORIES.map((category) => {
              const IconComponent = category.icon
              const toolCount = ALL_TOOLS.filter(tool => tool.category === category.id).length
              
              return (
                <Card 
                  key={category.id} 
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    selectedCategory === category.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedCategory(selectedCategory === category.id ? null : category.id)}
                >
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`h-12 w-12 bg-${category.color}-100 dark:bg-${category.color}-900 rounded-lg flex items-center justify-center`}>
                        <IconComponent className={`h-6 w-6 text-${category.color}-600`} />
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {toolCount} tools
                      </Badge>
                    </div>
                    <CardTitle className="text-xl">{category.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{category.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Tools Grid */}
      <section className="py-16 bg-background">
        <div className="container">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold mb-2">
                {selectedCategory 
                  ? TOOL_CATEGORIES.find(cat => cat.id === selectedCategory)?.name 
                  : searchQuery 
                    ? `Search Results for "${searchQuery}"` 
                    : 'All Tools'
                }
              </h2>
              <p className="text-muted-foreground">
                {filteredTools.length} tool{filteredTools.length !== 1 ? 's' : ''} found
              </p>
            </div>
            
            {selectedCategory && (
              <Button 
                variant="outline" 
                onClick={() => setSelectedCategory(null)}
              >
                Show All Categories
              </Button>
            )}
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.map((tool) => {
              const IconComponent = tool.icon
              return (
                <Card key={tool.id} className="group hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`h-10 w-10 bg-${getCategoryColor(tool.category)}-100 dark:bg-${getCategoryColor(tool.category)}-900 rounded-lg flex items-center justify-center`}>
                        <IconComponent className={`h-5 w-5 text-${getCategoryColor(tool.category)}-600`} />
                      </div>
                      {tool.featured && (
                        <Badge variant="secondary" className="text-xs">Featured</Badge>
                      )}
                    </div>
                    <CardTitle className="text-lg">{tool.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">{tool.description}</p>
                    <Link href={tool.href}>
                      <Button variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                        Try Tool
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {filteredTools.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No tools found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search or browse different categories
              </p>
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory(null)
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </section>
    </>
  )
}
