/**
 * Webhook事件幂等性管理
 * 使用Clerk的用户元数据存储处理过的事件，确保支付安全
 */

import { createClerkClient } from '@clerk/nextjs/server'

const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY!
})

interface ProcessedEvent {
  eventId: string
  eventType: string
  subscriptionId?: string | undefined
  userId?: string | undefined
  processedAt: string
  result: 'success' | 'failed'
  metadata?: Record<string, any>
}

// 使用Clerk存储事件处理记录
class EventStore {
  private readonly MAX_EVENTS_PER_USER = 50

  /**
   * 从用户元数据获取处理过的事件列表
   */
  private async getUserProcessedEvents(userId: string): Promise<ProcessedEvent[]> {
    try {
      const user = await clerkClient.users.getUser(userId)
      const metadata = user.privateMetadata as any
      return metadata.processedWebhookEvents || []
    } catch (error) {
      console.error('Failed to get user processed events:', error)
      return []
    }
  }

  /**
   * 保存处理过的事件到用户元数据
   */
  private async saveUserProcessedEvents(userId: string, events: ProcessedEvent[]): Promise<void> {
    try {
      const user = await clerkClient.users.getUser(userId)
      const currentMetadata = user.privateMetadata as any
      
      await clerkClient.users.updateUserMetadata(userId, {
        privateMetadata: {
          ...currentMetadata,
          processedWebhookEvents: events
        }
      })
    } catch (error) {
      console.error('Failed to save user processed events:', error)
      throw error
    }
  }

  /**
   * 检查事件是否已处理
   */
  async isEventProcessed(eventId: string, userId?: string): Promise<boolean> {
    if (!userId) return false
    
    const events = await this.getUserProcessedEvents(userId)
    return events.some(event => event.eventId === eventId)
  }

  /**
   * 获取已处理的事件信息
   */
  async getProcessedEvent(eventId: string, userId?: string): Promise<ProcessedEvent | null> {
    if (!userId) return null
    
    const events = await this.getUserProcessedEvents(userId)
    return events.find(event => event.eventId === eventId) || null
  }

  /**
   * 标记事件为已处理
   */
  async markEventProcessed(
    eventId: string, 
    eventType: string, 
    result: 'success' | 'failed',
    metadata?: {
      subscriptionId?: string
      userId?: string
      [key: string]: any
    }
  ): Promise<void> {
    if (!metadata?.userId) return

    const event: ProcessedEvent = {
      eventId,
      eventType,
      subscriptionId: metadata.subscriptionId,
      userId: metadata.userId,
      processedAt: new Date().toISOString(),
      result,
      metadata
    }

    const events = await this.getUserProcessedEvents(metadata.userId)
    events.push(event)

    // 保留最新的事件，清理旧事件
    const sortedEvents = events
      .sort((a, b) => new Date(b.processedAt).getTime() - new Date(a.processedAt).getTime())
      .slice(0, this.MAX_EVENTS_PER_USER)

    await this.saveUserProcessedEvents(metadata.userId, sortedEvents)
  }

  /**
   * 检查订阅相关的重复事件
   */
  async checkSubscriptionEventDuplicate(
    subscriptionId: string, 
    eventType: string,
    userId?: string,
    timeWindowMinutes: number = 5
  ): Promise<ProcessedEvent | null> {
    if (!userId) return null
    
    const cutoffTime = new Date(Date.now() - timeWindowMinutes * 60 * 1000)
    const events = await this.getUserProcessedEvents(userId)
    
    return events.find(event => 
      event.subscriptionId === subscriptionId &&
      event.eventType === eventType &&
      new Date(event.processedAt) > cutoffTime &&
      event.result === 'success'
    ) || null
  }

  /**
   * 获取用户的处理历史
   */
  async getUserEventHistory(userId: string, limit: number = 10): Promise<ProcessedEvent[]> {
    const events = await this.getUserProcessedEvents(userId)
    return events
      .sort((a, b) => new Date(b.processedAt).getTime() - new Date(a.processedAt).getTime())
      .slice(0, limit)
  }

  /**
   * 获取统计信息（简化版，因为数据分散在各用户中）
   */
  getStats(): {
    message: string
  } {
    return {
      message: 'Event statistics are stored per-user in Clerk metadata'
    }
  }
}

// 全局事件存储实例
export const eventStore = new EventStore()

/**
 * 高级幂等性检查
 * 不仅检查事件ID，还检查订阅相关的重复事件
 */
export async function checkEventIdempotency(
  eventId: string,
  eventType: string,
  subscriptionId?: string,
  userId?: string
): Promise<{
  isProcessed: boolean
  isDuplicate: boolean
  previousEvent?: ProcessedEvent | undefined
  duplicateEvent?: ProcessedEvent | undefined
}> {
  // 检查事件ID是否已处理
  const isProcessed = await eventStore.isEventProcessed(eventId, userId)
  const previousEvent = isProcessed ? await eventStore.getProcessedEvent(eventId, userId) : undefined

  // 检查订阅相关的重复事件
  let isDuplicate = false
  let duplicateEvent: ProcessedEvent | undefined

  if (subscriptionId && !isProcessed && userId) {
    const duplicateResult = await eventStore.checkSubscriptionEventDuplicate(subscriptionId, eventType, userId)
    duplicateEvent = duplicateResult || undefined
    isDuplicate = !!duplicateEvent
  }

  return {
    isProcessed,
    isDuplicate,
    previousEvent: previousEvent || undefined,
    duplicateEvent
  }
}

/**
 * 标记事件处理结果
 */
export async function markEventResult(
  eventId: string,
  eventType: string,
  result: 'success' | 'failed',
  metadata?: {
    subscriptionId?: string
    userId?: string
    [key: string]: any
  }
): Promise<void> {
  await eventStore.markEventProcessed(eventId, eventType, result, metadata)
}

/**
 * 获取事件处理统计
 */
export function getEventStats() {
  return eventStore.getStats()
}

/**
 * 获取用户事件历史
 */
export async function getUserEventHistory(userId: string, limit?: number): Promise<ProcessedEvent[]> {
  return eventStore.getUserEventHistory(userId, limit)
}