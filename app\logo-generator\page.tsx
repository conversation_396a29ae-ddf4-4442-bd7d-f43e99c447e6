import { PageLayout } from '@/components/layout/PageLayout'
import { LogoGeneratorClient } from './LogoGeneratorClient'
import type { Metadata } from 'next'

// Logo Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Logo Generator - Create Professional Logos | HumanWhisper',
  description: 'Free AI logo generator that creates professional brand logos instantly. No design skills required - just describe your brand.',
  keywords: [
    'AI logo generator',
    'logo maker tool',
    'brand logo creator',
    'professional logo design',
    'logo generator free'
  ],
  openGraph: {
    title: 'AI Logo Generator - Create Professional Logos | HumanWhisper',
    description: 'Free AI logo generator that creates professional brand logos instantly. No design skills required - just describe your brand.',
    url: '/logo-generator',
    type: 'website',
  },
  twitter: {
    title: 'AI Logo Generator - Create Professional Logos | HumanWhisper',
    description: 'Free AI logo generator that creates professional brand logos instantly. No design skills required - just describe your brand.',
  },
  alternates: {
    canonical: '/logo-generator',
  },
}

export default function LogoGeneratorPage() {
  return (
    <PageLayout>
      <LogoGeneratorClient />
    </PageLayout>
  )
}
