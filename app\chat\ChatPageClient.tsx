'use client'

import React, { useState, useEffect } from 'react'
import { ChatInterface } from '@/components/chat/ChatInterface'
import { SettingsPanel } from '@/components/settings/SettingsPanel'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { useChatStore } from '@/store/chat-store'

export function ChatPageClient() {
  const [showSettings, setShowSettings] = useState(false)
  const { createConversation, currentConversation } = useChatStore()

  // 初始化对话
  useEffect(() => {
    if (!currentConversation) {
      createConversation()
    }
  }, [currentConversation, createConversation])

  // 监听全局快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + , 打开设置
      if ((e.ctrlKey || e.metaKey) && e.key === ',') {
        e.preventDefault()
        setShowSettings(true)
      }
      
      // Esc 关闭设置
      if (e.key === 'Escape' && showSettings) {
        setShowSettings(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [showSettings])

  return (
    <ThemeProvider>
      <div className="h-screen flex flex-col bg-background">
        {/* 主聊天界面 */}
        <ChatInterface className="flex-1" />
        
        {/* 设置面板 */}
        <SettingsPanel 
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </ThemeProvider>
  )
}
