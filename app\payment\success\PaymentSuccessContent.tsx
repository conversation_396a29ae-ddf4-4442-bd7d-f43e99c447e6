'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { <PERSON><PERSON>ir<PERSON>, <PERSON>rk<PERSON>, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface PaymentResult {
  success: boolean
  message: string
  credits?: number
  plan?: string
  subscriptionEnd?: string
}

export function PaymentSuccessContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [result, setResult] = useState<PaymentResult | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const processPayment = async () => {
      const checkoutId = searchParams.get('checkout_id')
      const orderId = searchParams.get('order_id')
      const subscriptionId = searchParams.get('subscription_id')
      const customerId = searchParams.get('customer_id')

      // 验证必要的支付参数
      if (!checkoutId || !orderId || !subscriptionId) {
        setResult({
          success: false,
          message: 'Payment information incomplete. Please contact support if you completed a payment.'
        })
        setLoading(false)
        return
      }

      // 支付成功 - 直接更新用户状态
      try {
        const response = await fetch('/api/user/upgrade-premium', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            subscriptionId,
            orderId,
            customerId
          })
        })

        if (response.ok) {
          const data = await response.json()
          setResult({
            success: true,
            message: 'Payment completed successfully! Your account has been upgraded.',
            credits: data.credits || 3600,
            plan: 'premium',
            subscriptionEnd: data.subscriptionEnd
          })

          // 3秒后跳转到dashboard
          setTimeout(() => {
            router.push('/dashboard')
          }, 3000)
        } else {
          setResult({
            success: false,
            message: 'Payment successful but account upgrade failed. Please contact support.'
          })
        }
      } catch (error) {
        setResult({
          success: false,
          message: 'Payment successful but account upgrade failed. Please contact support.'
        })
      }

      setLoading(false)
    }

    processPayment()
  }, [searchParams])

  if (loading) {
    return (
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">Processing payment result...</p>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="text-center">
        <p className="text-muted-foreground">Unable to get payment result</p>
      </div>
    )
  }

  return (
    <div className="max-w-md w-full bg-card rounded-lg shadow-lg p-8 text-center">
      {result.success ? (
        <>
          <div className="mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-foreground mb-2">
              Payment Successful!
            </h1>
            <p className="text-muted-foreground">
              Congratulations on becoming a Premium user
            </p>
          </div>

          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center mb-2">
              <Sparkles className="h-5 w-5 text-purple-500 mr-2" />
              <span className="font-semibold">Credits Received</span>
            </div>
            <div className="text-3xl font-bold text-purple-600">
              {result.credits || 3600}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Automatically distributed monthly, enjoy all AI models
            </p>
          </div>

          <div className="space-y-3 mb-6 text-left">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-sm">Unlock all AI models</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-sm">Priority customer support</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-sm">Ad-free experience</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              <span className="text-sm">Early access to exclusive features</span>
            </div>
          </div>

          {result.subscriptionEnd && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
              <div className="text-sm text-blue-800">
                <strong>Subscription Valid Until:</strong>
                <br />
                {new Date(result.subscriptionEnd).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          )}

          <Button
            onClick={() => router.push('/chat')}
            className="w-full"
          >
            Start Using
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </>
      ) : (
        <>
          <div className="mb-6">
            <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-500 text-2xl">✕</span>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              Payment Failed
            </h1>
            <p className="text-muted-foreground">
              {result.message}
            </p>
          </div>

          <Button 
            onClick={() => router.push('/chat')}
            variant="outline"
            className="w-full"
          >
            Back to Chat
          </Button>
        </>
      )}
    </div>
  )
}
