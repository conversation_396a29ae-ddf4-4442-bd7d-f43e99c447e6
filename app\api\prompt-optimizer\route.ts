import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 系统提示词模板
const SYSTEM_PROMPTS = {
  creative: `You are an expert prompt engineer specializing in creative writing prompts. Transform the user's vague idea into a detailed, structured prompt that will generate high-quality creative content. Include specific elements like:
- Clear creative goal and format
- Character/setting details when relevant  
- Tone and style specifications
- Length requirements
- Specific creative constraints or requirements`,

  analytical: `You are an expert prompt engineer specializing in analytical and research tasks. Transform the user's request into a structured prompt that will generate thorough, well-organized analysis. Include:
- Clear analytical objective
- Specific methodology or framework to use
- Required output format and structure
- Key areas to focus on
- Actionable insights or recommendations needed`,

  coding: `You are an expert prompt engineer specializing in programming tasks. Transform the user's coding request into a detailed technical prompt that will generate clean, functional code. Include:
- Specific programming language and version
- Clear functional requirements
- Code structure and organization needs
- Error handling requirements
- Documentation and commenting standards
- Testing considerations when relevant`,

  business: `You are an expert prompt engineer specializing in business and marketing tasks. Transform the user's business request into a professional prompt that will generate strategic, actionable business content. Include:
- Clear business objective and context
- Target audience specification
- Required deliverables and format
- Key business metrics or KPIs to consider
- Professional tone and style requirements`,

  educational: `You are an expert prompt engineer specializing in educational content. Transform the user's learning request into a structured educational prompt that will generate clear, comprehensive teaching material. Include:
- Learning objectives and outcomes
- Target audience skill level
- Content structure and organization
- Examples and practical applications needed
- Assessment or practice elements when relevant`,

  general: `You are an expert prompt engineer. Transform the user's general request into a clear, structured prompt that will generate high-quality, relevant responses. Include:
- Clear objective and context
- Specific requirements and constraints
- Desired output format and structure
- Tone and style specifications
- Any relevant background information needed`
}

/**
 * 提示词优化API - POST请求处理
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      userInput,
      model = 'Qwen/Qwen2-7B-Instruct',
      promptType = 'general'
    }: {
      userInput: string
      model?: string
      promptType?: keyof typeof SYSTEM_PROMPTS
    } = body

    // 输入验证
    if (!userInput || typeof userInput !== 'string') {
      return NextResponse.json(
        { error: 'User input is required' },
        { status: 400 }
      )
    }

    if (userInput.length > 2000) {
      return NextResponse.json(
        { error: 'Input too long. Please keep it under 2000 characters.' },
        { status: 400 }
      )
    }

    // 获取对应的系统提示词
    const systemPrompt = SYSTEM_PROMPTS[promptType] || SYSTEM_PROMPTS.general

    // 构建优化请求
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt
      },
      {
        role: 'user' as const,
        content: `Please optimize this prompt or idea into a clear, structured, and effective prompt:

"${userInput}"

Return only the optimized prompt without any explanations or additional text.`
      }
    ]

    // 创建AI客户端（使用随机 API key）
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      temperature: 0.7,
      max_tokens: 1024,
      top_p: 0.7,
      frequency_penalty: 0.0,
    })

    // 提取优化后的提示词
    const optimizedPrompt = response.choices[0]?.message?.content?.trim()

    if (!optimizedPrompt) {
      throw new Error('No response generated')
    }

    return NextResponse.json({
      optimizedPrompt,
      metadata: {
        model,
        promptType,
        inputLength: userInput.length,
        outputLength: optimizedPrompt.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    // 根据错误类型返回适当的错误信息
    let errorMessage = 'Failed to optimize prompt. Please try again.'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'API configuration error'
        statusCode = 500
      } else if (error.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.'
        statusCode = 429
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout. Please try again.'
        statusCode = 504
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}

/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}
