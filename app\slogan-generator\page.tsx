import { PageLayout } from '@/components/layout/PageLayout'
import { SloganGeneratorClient } from './SloganGeneratorClient'
import type { Metadata } from 'next'

// Brand Slogan Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Brand Slogan Generator - Create Catchy Slogans | HumanWhisper',
  description: 'Generate powerful brand slogans and taglines instantly. Choose industry, tone, and get 10 creative English slogans with domain availability check.',
  keywords: [
    'slogan generator',
    'brand slogan',
    'tagline creator',
    'marketing copy',
    'brand messaging',
    'slogan maker',
    'brand identity'
  ],
  openGraph: {
    title: 'AI Brand Slogan Generator - Create Catchy Slogans | HumanWhisper',
    description: 'Generate powerful brand slogans and taglines instantly. Choose industry, tone, and get 10 creative English slogans with domain availability check.',
    url: '/slogan-generator',
    type: 'website',
  },
  twitter: {
    title: 'AI Brand Slogan Generator - Create Catchy Slogans | HumanWhisper',
    description: 'Generate powerful brand slogans and taglines instantly. Choose industry, tone, and get 10 creative English slogans with domain availability check.',
  },
  alternates: {
    canonical: '/slogan-generator',
  },
}

export default function SloganGeneratorPage() {
  return (
    <PageLayout>
      <SloganGeneratorClient />
    </PageLayout>
  )
}
