'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, HelpCircle, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, FileText, Users, MessageSquare, BookOpen, Target, Search, TrendingUp } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 业务场景选项
const BUSINESS_SCENARIOS = [
  { id: 'saas', name: 'SaaS Platform', description: 'Software as a Service products' },
  { id: 'ecommerce', name: 'E-commerce Store', description: 'Online retail and shopping' },
  { id: 'hardware', name: 'Hardware Product', description: 'Physical products and devices' },
  { id: 'event', name: 'Event Registration', description: 'Events, conferences, workshops' },
  { id: 'education', name: 'Educational Service', description: 'Courses, training, learning' },
  { id: 'healthcare', name: 'Healthcare Service', description: 'Medical, wellness, health' },
  { id: 'finance', name: 'Financial Service', description: 'Banking, investment, fintech' },
  { id: 'consulting', name: 'Consulting Service', description: 'Professional services, consulting' },
  { id: 'custom', name: 'Custom Business', description: 'Define your own business type' }
]

// FAQ数量选项
const FAQ_COUNTS = [
  { id: '8', name: '8 FAQs', description: 'Essential questions only' },
  { id: '10', name: '10 FAQs', description: 'Standard FAQ section' },
  { id: '12', name: '12 FAQs', description: 'Comprehensive coverage' },
  { id: '15', name: '15 FAQs', description: 'Detailed FAQ section' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'Qwen/Qwen2.5-7B-Instruct',
    name: 'Qwen2.5',
    description: 'Structured output, JSON format support'
  },
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM',
    description: 'Complex reasoning, customer service scenarios'
  }
]

export function FAQGeneratorClient() {
  // 表单状态
  const [scenario, setScenario] = useState('')
  const [customScenario, setCustomScenario] = useState('')
  const [productInfo, setProductInfo] = useState('')
  const [userQuestions, setUserQuestions] = useState('')
  const [faqCount, setFaqCount] = useState('10')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'Qwen/Qwen2.5-7B-Instruct')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedFAQs, setGeneratedFAQs] = useState<Array<{question: string, answer: string}>>([])
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成FAQ
  const handleGenerate = async () => {
    if (!scenario || !productInfo.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in business scenario and product information.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/faq-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scenario: scenario === 'custom' ? customScenario : scenario,
          productInfo: productInfo.trim(),
          userQuestions: userQuestions.trim(),
          faqCount: parseInt(faqCount),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate FAQs')
      }

      const data = await response.json()
      setGeneratedFAQs(data.faqs)

      toast({
        title: "FAQs Generated!",
        description: `Generated ${data.faqs.length} professional FAQ items.`,
      })

    } catch (error) {
      setError('Failed to generate FAQs. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制FAQ到剪贴板
  const copyFAQsToClipboard = async () => {
    const faqText = generatedFAQs.map((faq, index) => 
      `${index + 1}. ${faq.question}\n${faq.answer}\n`
    ).join('\n')
    
    try {
      await navigator.clipboard.writeText(faqText)
      toast({
        title: "Copied!",
        description: "FAQs copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  // 复制JSON格式
  const copyFAQsAsJSON = async () => {
    const jsonText = JSON.stringify(generatedFAQs, null, 2)
    
    try {
      await navigator.clipboard.writeText(jsonText)
      toast({
        title: "JSON Copied!",
        description: "FAQs copied as JSON format.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                AI FAQ Generator
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Create Professional FAQs
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>comprehensive FAQ sections</strong> automatically. Input your product info 
              and user questions, get professional FAQ answers instantly.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Generation
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Professional Quality
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Multiple Formats
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-primary" />
                      FAQ Configuration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Business Scenario */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Business Scenario *</label>
                      <Select value={scenario} onValueChange={setScenario}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your business type" />
                        </SelectTrigger>
                        <SelectContent>
                          {BUSINESS_SCENARIOS.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Scenario Input */}
                    {scenario === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Business Type *</label>
                        <input
                          type="text"
                          placeholder="Describe your business type"
                          value={customScenario}
                          onChange={(e) => setCustomScenario(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Product Information */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Product Information *</label>
                      <Textarea
                        placeholder="Describe your product or service in detail. Include key features, benefits, pricing, and target audience..."
                        value={productInfo}
                        onChange={(e) => setProductInfo(e.target.value)}
                        className="min-h-[120px] resize-none"
                      />
                    </div>

                    {/* User Questions */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Recent User Questions</label>
                      <Textarea
                        placeholder="Paste recent customer questions, chat logs, or support tickets. Each question on a new line or separated by commas..."
                        value={userQuestions}
                        onChange={(e) => setUserQuestions(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Optional: Add real user questions to make FAQs more relevant
                      </p>
                    </div>

                    {/* FAQ Count */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Number of FAQs</label>
                      <Select value={faqCount} onValueChange={setFaqCount}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {FAQ_COUNTS.map((count) => (
                            <SelectItem key={count.id} value={count.id}>
                              <div>
                                <div className="font-medium">{count.name}</div>
                                <div className="text-xs text-muted-foreground">{count.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating FAQs...
                        </>
                      ) : (
                        <>
                          <HelpCircle className="h-4 w-4 mr-2" />
                          Generate FAQ Section
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-primary" />
                        Generated FAQs
                      </span>
                      {generatedFAQs.length > 0 && (
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyFAQsToClipboard}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy Text
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={copyFAQsAsJSON}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            Copy JSON
                          </Button>
                        </div>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedFAQs.length > 0 ? (
                      <div className="space-y-4">
                        <div className="text-sm text-muted-foreground mb-4">
                          Generated {generatedFAQs.length} FAQ items
                        </div>
                        <FAQ items={generatedFAQs} />
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your generated FAQ section will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Choose Our AI FAQ Generator?
            </h2>
            <p className="text-xl text-muted-foreground">
              Create comprehensive FAQ sections that reduce support tickets and improve customer satisfaction
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Generate comprehensive FAQ sections in seconds, not hours
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Professional Quality</h3>
                <p className="text-sm text-muted-foreground">
                  AI-crafted answers that maintain professional standards and clarity
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Customer-Focused</h3>
                <p className="text-sm text-muted-foreground">
                  Based on real user questions and common customer concerns
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Formats</h3>
                <p className="text-sm text-muted-foreground">
                  Export as text, JSON, or integrate directly into your website
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Industry-Specific</h3>
                <p className="text-sm text-muted-foreground">
                  Tailored for different business types and industry requirements
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Ready to Use</h3>
                <p className="text-sm text-muted-foreground">
                  Generated FAQs are ready for immediate use on your website or documentation
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create professional FAQ sections in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Business Type</h3>
              <p className="text-sm text-muted-foreground">
                Select your business scenario from SaaS, e-commerce, hardware, events, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Add Product Info</h3>
              <p className="text-sm text-muted-foreground">
                Describe your product or service, including features, pricing, and target audience
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Include User Questions</h3>
              <p className="text-sm text-muted-foreground">
                Optionally add real customer questions from support tickets or chat logs
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Generate & Export</h3>
              <p className="text-sm text-muted-foreground">
                Get professional FAQ sections and export in your preferred format
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Every Business
            </h2>
            <p className="text-xl text-muted-foreground">
              Generate FAQs tailored to your specific industry and business needs
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💻</span>
                </div>
                <h3 className="font-semibold mb-2">SaaS Platforms</h3>
                <p className="text-sm text-muted-foreground">
                  Features, pricing, integrations, and technical support questions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🛒</span>
                </div>
                <h3 className="font-semibold mb-2">E-commerce</h3>
                <p className="text-sm text-muted-foreground">
                  Shipping, returns, product details, and payment information
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="font-semibold mb-2">Events</h3>
                <p className="text-sm text-muted-foreground">
                  Registration, schedules, venues, and attendee information
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔧</span>
                </div>
                <h3 className="font-semibold mb-2">Hardware</h3>
                <p className="text-sm text-muted-foreground">
                  Setup, troubleshooting, warranties, and technical specifications
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Writing Guide Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Professional FAQ Writing Guide
              </h2>
              <p className="text-xl text-muted-foreground">
                Learn how to create effective FAQs that reduce support tickets and improve customer satisfaction
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              {/* FAQ Structure Formula */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    FAQ Structure Formula
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-blue-600">1</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Clear Question</h4>
                        <p className="text-xs text-muted-foreground">Use customer language, not technical jargon</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-green-600">2</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Direct Answer</h4>
                        <p className="text-xs text-muted-foreground">Start with the most important information</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-purple-600">3</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Supporting Details</h4>
                        <p className="text-xs text-muted-foreground">Add context, examples, or next steps</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-orange-600">4</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Call to Action</h4>
                        <p className="text-xs text-muted-foreground">Link to resources or contact information</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Question Discovery Methods */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5 text-primary" />
                    How to Find User Questions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-sm text-blue-600 mb-1">📧 Support Channels</h4>
                      <p className="text-xs text-muted-foreground">Email tickets, live chat logs, phone call summaries</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-green-600 mb-1">💬 Social Media</h4>
                      <p className="text-xs text-muted-foreground">Comments, DMs, mentions, and community discussions</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-purple-600 mb-1">📊 Analytics Data</h4>
                      <p className="text-xs text-muted-foreground">Search queries, page exits, and user behavior patterns</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-orange-600 mb-1">🗣️ Sales Team</h4>
                      <p className="text-xs text-muted-foreground">Common objections and questions during sales calls</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-red-600 mb-1">📝 User Surveys</h4>
                      <p className="text-xs text-muted-foreground">Direct feedback and feature requests from customers</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Industry-Specific FAQ Templates */}
            <div className="mb-12">
              <h3 className="text-2xl font-bold text-center mb-8">Industry-Specific FAQ Templates</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <span className="text-2xl">💻</span>
                      SaaS Platform FAQs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                      <p><strong>Pricing:</strong> "How much does [product] cost?"</p>
                      <p><strong>Features:</strong> "What's included in the [plan] plan?"</p>
                      <p><strong>Integration:</strong> "Does [product] integrate with [tool]?"</p>
                      <p><strong>Security:</strong> "How do you protect my data?"</p>
                      <p><strong>Support:</strong> "What support options are available?"</p>
                      <p><strong>Trial:</strong> "Do you offer a free trial?"</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <span className="text-2xl">🛒</span>
                      E-commerce FAQs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                      <p><strong>Shipping:</strong> "How long does shipping take?"</p>
                      <p><strong>Returns:</strong> "What's your return policy?"</p>
                      <p><strong>Payment:</strong> "What payment methods do you accept?"</p>
                      <p><strong>Sizing:</strong> "How do I choose the right size?"</p>
                      <p><strong>Tracking:</strong> "How can I track my order?"</p>
                      <p><strong>International:</strong> "Do you ship internationally?"</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <span className="text-2xl">🎯</span>
                      Event Registration FAQs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                      <p><strong>Registration:</strong> "How do I register for the event?"</p>
                      <p><strong>Tickets:</strong> "Can I transfer my ticket to someone else?"</p>
                      <p><strong>Schedule:</strong> "What's the event schedule?"</p>
                      <p><strong>Location:</strong> "Where is the event located?"</p>
                      <p><strong>Cancellation:</strong> "What if I can't attend?"</p>
                      <p><strong>Networking:</strong> "Will there be networking opportunities?"</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <span className="text-2xl">🔧</span>
                      Hardware Product FAQs
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                      <p><strong>Setup:</strong> "How do I set up the device?"</p>
                      <p><strong>Compatibility:</strong> "Is it compatible with [system]?"</p>
                      <p><strong>Warranty:</strong> "What's covered under warranty?"</p>
                      <p><strong>Troubleshooting:</strong> "The device isn't working, what should I do?"</p>
                      <p><strong>Updates:</strong> "How do I update the firmware?"</p>
                      <p><strong>Specifications:</strong> "What are the technical specifications?"</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* FAQ Best Practices */}
            <div className="mt-12">
              <h3 className="text-2xl font-bold text-center mb-8">FAQ Writing Best Practices</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Be Specific</h4>
                  <p className="text-sm text-muted-foreground">Use exact numbers, timeframes, and clear instructions</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Use Customer Language</h4>
                  <p className="text-sm text-muted-foreground">Write questions as customers would ask them, not as you would</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Keep Updated</h4>
                  <p className="text-sm text-muted-foreground">Regularly review and update FAQs based on new questions</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-6 w-6 text-orange-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Test Answers</h4>
                  <p className="text-sm text-muted-foreground">Ensure answers actually solve the customer's problem</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our AI FAQ generator
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How does the AI FAQ generator work?",
                  answer: "Our AI analyzes your product information and business type to generate relevant questions customers typically ask. It then creates comprehensive, professional answers tailored to your specific business."
                },
                {
                  question: "Can I customize the generated FAQs?",
                  answer: "Yes! You can copy the generated FAQs and edit them as needed. The AI provides a solid foundation that you can customize to match your brand voice and specific requirements."
                },
                {
                  question: "What formats can I export the FAQs in?",
                  answer: "You can copy FAQs as plain text for easy editing or as JSON format for direct integration into websites, apps, or content management systems."
                },
                {
                  question: "How many FAQs can I generate at once?",
                  answer: "You can generate between 8-15 FAQ items in a single session. This range provides comprehensive coverage while maintaining quality and relevance."
                },
                {
                  question: "Do I need to provide user questions?",
                  answer: "User questions are optional but recommended. Adding real customer questions from support tickets or chat logs helps create more relevant and targeted FAQs."
                },
                {
                  question: "Is the FAQ generator suitable for all business types?",
                  answer: "Yes! We support various business scenarios including SaaS, e-commerce, hardware, events, education, healthcare, finance, and consulting. You can also define custom business types."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
