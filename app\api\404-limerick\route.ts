import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 品牌调性映射
const BRAND_TONE_MAP: Record<string, string> = {
  'geeky': 'geeky and tech-savvy with programming references and nerdy humor',
  'cute': 'cute, playful, and adorable with friendly and approachable language',
  'dark': 'dark, edgy, and mysterious with sophisticated and subtle humor',
  'minimal': 'minimal, clean, and understated with simple and elegant expression',
  'quirky': 'quirky, eccentric, and unconventional with unique personality',
  'professional': 'professional and business-appropriate with subtle, tasteful humor',
  'playful': 'playful, fun, and energetic with lighthearted and cheerful tone',
  'witty': 'witty, clever, and intellectually humorous with smart wordplay'
}

// 构建404打油诗生成prompt
function buildLimerickPrompt(
  brandTone: string,
  brandName: string,
  productDescription: string
): string {
  const toneDesc = BRAND_TONE_MAP[brandTone] || brandTone

  const prompt = `You are a creative copywriter specializing in brand humor and poetry. Your task is to create a memorable 404 error page experience using a limerick (5-line poem with AABBA rhyme scheme) and a humorous call-to-action.

**Brand Information:**
- Brand Name: ${brandName}
- Product/Service: ${productDescription}
- Brand Tone: ${toneDesc}

**Instructions:**
Create a complete 404 error page content that includes:

1. **Limerick (5 lines)**: Follow traditional limerick structure:
   - Lines 1, 2, 5: Longer lines that rhyme with each other (A rhyme)
   - Lines 3, 4: Shorter lines that rhyme with each other (B rhyme)
   - Rhythm: da-da-DUM-da-da-DUM-da-da-DUM (anapestic meter)
   - Should reference the 404 error, the brand, and be humorous

2. **Call-to-Action**: One humorous sentence that encourages users to navigate back or explore the site

3. **HTML Code**: Complete HTML structure ready to implement

**Limerick Requirements:**
- Must mention the brand name (${brandName})
- Should reference "404" or "page not found" concept
- Must be appropriate for the brand tone: ${toneDesc}
- Should be memorable and shareable
- Keep it family-friendly and professional

**Output Format:**
Return a valid JSON object:

{
  "limerick": "Line 1\\nLine 2\\nLine 3\\nLine 4\\nLine 5",
  "cta": "Your humorous call-to-action sentence here",
  "htmlCode": "Complete HTML code with proper structure and styling",
  "explanation": "Brief explanation of the creative strategy and tone choices"
}

**HTML Structure Guidelines:**
- Include proper DOCTYPE and meta tags
- Add basic CSS styling for typography and layout
- Make it responsive and accessible
- Include the limerick in a prominent position
- Add the CTA as a button or link
- Use appropriate colors and fonts for the brand tone

**Important:**
- Ensure JSON format is valid and properly escaped
- Make the limerick scan properly (good rhythm)
- Keep the tone consistent throughout
- Make it genuinely funny, not forced

Generate the 404 limerick page content now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      brandTone,
      brandName,
      productDescription,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!brandTone || !brandName?.trim() || !productDescription?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildLimerickPrompt(
      brandTone,
      brandName.trim(),
      productDescription.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'Qwen/Qwen2.5-7B-Instruct': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let limerick: {limerick: string, cta: string, htmlCode: string, explanation: string}
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      limerick = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!limerick.limerick || !limerick.cta || !limerick.htmlCode || !limerick.explanation) {
        throw new Error('Invalid response structure')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取内容
      const lines = generatedContent.split('\n').filter(line => line.trim())
      
      // 尝试找到打油诗内容
      let limerickText = ''
      let ctaText = ''
      let explanationText = 'Creative 404 limerick for brand engagement'
      
      // 简单的内容提取逻辑
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]?.trim()
        if (line && line.length > 10 && !line.includes('{') && !line.includes('}')) {
          if (!limerickText && (line.includes('404') || line.includes(brandName))) {
            // 尝试提取5行打油诗
            const potentialLimerick = lines.slice(i, i + 5).join('\n')
            if (potentialLimerick.length > 50) {
              limerickText = potentialLimerick
            }
          } else if (!ctaText && line.length < 100 && (line.includes('back') || line.includes('home') || line.includes('try'))) {
            ctaText = line
          }
        }
      }
      
      if (!limerickText) {
        throw new Error('Failed to parse limerick content')
      }
      
      // 生成基本HTML结构
      const basicHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | ${brandName}</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .limerick { font-size: 18px; line-height: 1.6; margin: 30px 0; font-style: italic; color: #333; }
        .cta { margin-top: 30px; }
        .btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>404 - Oops!</h1>
        <div class="limerick">${limerickText.replace(/\n/g, '<br>')}</div>
        <div class="cta">
            <p>${ctaText || 'Let\'s get you back on track!'}</p>
            <a href="/" class="btn">Go Home</a>
        </div>
    </div>
</body>
</html>`
      
      limerick = {
        limerick: limerickText,
        cta: ctaText || 'Let\'s get you back on track!',
        htmlCode: basicHtml,
        explanation: explanationText
      }
    }

    return NextResponse.json({
      limerick,
      metadata: {
        brandTone,
        brandName,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate 404 limerick' },
      { status: 500 }
    )
  }
}
