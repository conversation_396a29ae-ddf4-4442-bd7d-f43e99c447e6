'use client'

import React, { useState } from 'react'
import {
  Settings,
  Palette,
  Globe,
  Shield,
  Keyboard,
  Download,
  RotateCcw,
  X
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { useSettingsStore } from '@/store/settings-store'
import { useChatStore } from '@/store/chat-store'
import { ThemeSettings } from './ThemeSettings'
import { LanguageSettings } from './LanguageSettings'
import { PrivacySettings } from './PrivacySettings'
import { ShortcutSettings } from './ShortcutSettings'
import { ImportExportSettings } from './ImportExportSettings'

interface SettingsPanelProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

export function SettingsPanel({ isOpen, onClose, className }: SettingsPanelProps) {
  const [activeTab, setActiveTab] = useState('appearance')
  const { resetToDefaults } = useSettingsStore()
  const { exportConversations } = useChatStore()

  if (!isOpen) return null

  const handleResetSettings = () => {
    if (confirm('Are you sure you want to reset all settings? This action cannot be undone.')) {
      resetToDefaults()
    }
  }

  const handleExportAll = () => {
    const settings = useSettingsStore.getState().exportSettings()
    const conversations = exportConversations()
    
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: JSON.parse(settings),
      conversations: JSON.parse(conversations)
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `humanwhisper-backup-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className={`fixed inset-0 z-50 bg-background/80 backdrop-blur-sm ${className}`}>
      <div className="fixed inset-y-0 right-0 w-full max-w-2xl bg-background border-l shadow-lg">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <h2 className="text-xl font-semibold">Settings</h2>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
              <div className="border-b px-6">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="appearance" className="flex items-center space-x-2">
                    <Palette className="h-4 w-4" />
                    <span className="hidden sm:inline">Theme</span>
                  </TabsTrigger>
                  <TabsTrigger value="language" className="flex items-center space-x-2">
                    <Globe className="h-4 w-4" />
                    <span className="hidden sm:inline">Language</span>
                  </TabsTrigger>
                  <TabsTrigger value="privacy" className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span className="hidden sm:inline">Privacy</span>
                  </TabsTrigger>
                  <TabsTrigger value="shortcuts" className="flex items-center space-x-2">
                    <Keyboard className="h-4 w-4" />
                    <span className="hidden sm:inline">Shortcuts</span>
                  </TabsTrigger>
                  <TabsTrigger value="data" className="flex items-center space-x-2">
                    <Download className="h-4 w-4" />
                    <span className="hidden sm:inline">Data</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="flex-1 overflow-y-auto p-6">
                <TabsContent value="appearance" className="space-y-6 mt-0">
                  <ThemeSettings />
                </TabsContent>

                <TabsContent value="language" className="space-y-6 mt-0">
                  <LanguageSettings />
                </TabsContent>

                <TabsContent value="privacy" className="space-y-6 mt-0">
                  <PrivacySettings />
                </TabsContent>

                <TabsContent value="shortcuts" className="space-y-6 mt-0">
                  <ShortcutSettings />
                </TabsContent>

                <TabsContent value="data" className="space-y-6 mt-0">
                  <ImportExportSettings />
                </TabsContent>
              </div>
            </Tabs>
          </div>

          {/* Bottom Actions */}
          <div className="border-t p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportAll}
                  className="flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Export All</span>
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetSettings}
                  className="flex items-center space-x-2 text-destructive hover:text-destructive"
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset Settings</span>
                </Button>
              </div>

              <div className="text-xs text-muted-foreground">
                HumanWhisper v1.0.0
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}




