import { PageLayout } from '@/components/layout/PageLayout'
import { FlashLandingPageClient } from './FlashLandingPageClient'
import type { Metadata } from 'next'

// Flash Landing Page页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Flash Landing Page Generator - Coming Soon Pages | HumanWhisper',
  description: 'Generate complete coming soon landing pages in minutes. Create hero copy, email capture, and social media teasers for your new domain launch.',
  keywords: [
    'landing page generator',
    'coming soon page',
    'domain launch',
    'email capture',
    'landing page copy',
    'website launch',
    'page builder'
  ],
  openGraph: {
    title: 'AI Flash Landing Page Generator - Coming Soon Pages | HumanWhisper',
    description: 'Generate complete coming soon landing pages in minutes. Create hero copy, email capture, and social media teasers for your new domain launch.',
    url: '/flash-landing-page',
    type: 'website',
  },
  twitter: {
    title: 'AI Flash Landing Page Generator - Coming Soon Pages | HumanWhisper',
    description: 'Generate complete coming soon landing pages in minutes. Create hero copy, email capture, and social media teasers for your new domain launch.',
  },
  alternates: {
    canonical: '/flash-landing-page',
  },
}

export default function FlashLandingPagePage() {
  return (
    <PageLayout>
      <FlashLandingPageClient />
    </PageLayout>
  )
}
