// AI服务相关类型定义

import type { AIModel, MessageRole } from './index'

// OpenAI API 相关类型
export interface OpenAIMessage {
  role: MessageRole
  content: string
  name?: string
}

export interface OpenAICompletionRequest {
  model: string
  messages: OpenAIMessage[]
  temperature?: number
  max_tokens?: number
  top_p?: number
  frequency_penalty?: number
  presence_penalty?: number
  stream?: boolean
  stop?: string | string[]
  user?: string
}

export interface OpenAICompletionResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: MessageRole
      content: string
    }
    finish_reason: 'stop' | 'length' | 'content_filter' | 'tool_calls' | null
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export interface OpenAIStreamChunk {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    delta: {
      role?: MessageRole
      content?: string
    }
    finish_reason?: 'stop' | 'length' | 'content_filter' | 'tool_calls' | null
  }>
}

// AIHubMix 特定类型
export interface AIHubMixConfig {
  apiKey: string
  baseURL: string
  timeout: number
  maxRetries: number
  retryDelay: number
}

export interface AIHubMixError {
  error: {
    message: string
    type: string
    param?: string
    code?: string
  }
  status?: number
}

// 模型能力定义
export interface ModelCapabilities {
  maxTokens: number
  contextWindow: number
  supportsStreaming: boolean
  supportsImages: boolean
  supportsTools: boolean
  languages: string[]
  specialties: string[]
}

// 模型性能指标
export interface ModelPerformance {
  speed: number        // 1-5 评分
  quality: number      // 1-5 评分
  cost: number         // 1-5 评分 (1=最便宜)
  reasoning: number    // 1-5 评分
  creativity: number   // 1-5 评分
  accuracy: number     // 1-5 评分
}

// 模型使用统计
export interface ModelUsageStats {
  totalRequests: number
  totalTokens: number
  totalCost: number
  averageResponseTime: number
  successRate: number
  errorRate: number
  lastUsed: Date
}

// 请求配置
export interface RequestConfig {
  model: AIModel
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  stream: boolean
  timeout: number
}

// 响应元数据
export interface ResponseMetadata {
  model: AIModel
  tokenUsage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  processingTime: number
  cost: number
  finishReason: string
  requestId: string
  timestamp: Date
}

// 流式响应处理器
export interface StreamHandler {
  onStart?: () => void
  onToken?: (token: string) => void
  onComplete?: (fullResponse: string, metadata: ResponseMetadata) => void
  onError?: (error: Error) => void
}

// 重试配置
export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  retryableErrors: string[]
}

// 缓存配置
export interface CacheConfig {
  enabled: boolean
  ttl: number // 生存时间（秒）
  maxSize: number // 最大缓存条目数
  keyGenerator: (request: OpenAICompletionRequest) => string
}

// 监控和日志
export interface RequestLog {
  id: string
  timestamp: Date
  model: AIModel
  request: OpenAICompletionRequest
  response?: OpenAICompletionResponse
  error?: AIHubMixError
  duration: number
  tokenUsage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  cost?: number
}

// 健康检查
export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: Date
  responseTime: number
  availableModels: string[]
  errors: string[]
}

// 配额和限制
export interface QuotaInfo {
  remaining: number
  limit: number
  resetTime: Date
  used: number
  percentage: number
}

// 模型比较结果
export interface ModelComparison {
  modelA: AIModel
  modelB: AIModel
  comparison: {
    speed: number      // -1 to 1
    quality: number    // -1 to 1
    cost: number       // -1 to 1
    reasoning: number  // -1 to 1
  }
  recommendation: AIModel
  reasons: string[]
}

// 智能路由配置
export interface SmartRoutingConfig {
  enabled: boolean
  rules: Array<{
    condition: (request: OpenAICompletionRequest) => boolean
    targetModel: AIModel
    reason: string
  }>
  fallbackModel: AIModel
}

// 批处理请求
export interface BatchRequest {
  id: string
  requests: OpenAICompletionRequest[]
  priority: 'low' | 'normal' | 'high'
  callback?: (results: BatchResult[]) => void
}

export interface BatchResult {
  requestId: string
  success: boolean
  response?: OpenAICompletionResponse
  error?: AIHubMixError
  processingTime: number
}

// 模型切换建议
export interface ModelSwitchSuggestion {
  shouldSwitch: boolean
  currentModel: AIModel
  suggestedModel: AIModel
  reasons: string[]
  benefits: string[]
  potentialDrawbacks: string[]
  confidence: number // 0-1
}

// 上下文管理
export interface ContextManager {
  maxTokens: number
  strategy: 'truncate' | 'summarize' | 'sliding_window'
  preserveSystemMessage: boolean
  preserveLastN: number
}

// 内容过滤
export interface ContentFilter {
  enabled: boolean
  categories: string[]
  action: 'block' | 'warn' | 'log'
  customRules: Array<{
    pattern: RegExp
    action: 'block' | 'warn' | 'log'
    message: string
  }>
}

// 模型预热
export interface ModelWarmup {
  models: AIModel[]
  interval: number // 秒
  testPrompt: string
  enabled: boolean
}
