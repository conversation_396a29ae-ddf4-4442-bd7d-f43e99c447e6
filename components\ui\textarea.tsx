import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// 文本域变体样式定义
const textareaVariants = cva(
  "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none",
  {
    variants: {
      variant: {
        default: "",
        error: "border-destructive focus-visible:ring-destructive",
        success: "border-green-500 focus-visible:ring-green-500",
      },
      size: {
        default: "min-h-[80px]",
        sm: "min-h-[60px] px-2 py-1 text-xs",
        lg: "min-h-[120px] px-4 py-3",
        xl: "min-h-[200px] px-4 py-3",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {
  error?: string
  success?: string
  label?: string
  helperText?: string
  maxLength?: number
  showCount?: boolean
  autoResize?: boolean
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    className, 
    variant, 
    size,
    error,
    success,
    label,
    helperText,
    maxLength,
    showCount = false,
    autoResize = false,
    value,
    onChange,
    id,
    ...props 
  }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null)
    const inputId = id || React.useId()
    const hasError = Boolean(error)
    const hasSuccess = Boolean(success)
    
    // 合并refs
    React.useImperativeHandle(ref, () => textareaRef.current!)
    
    // 根据状态确定变体
    const finalVariant = hasError ? "error" : hasSuccess ? "success" : variant
    
    // 字符计数
    const currentLength = typeof value === 'string' ? value.length : 0
    
    // 自动调整高度
    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
    }, [value, autoResize])
    
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (maxLength && e.target.value.length > maxLength) {
        return
      }
      onChange?.(e)
    }

    return (
      <div className="space-y-2">
        {label && (
          <div className="flex items-center justify-between">
            <label 
              htmlFor={inputId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {label}
            </label>
            {showCount && maxLength && (
              <span className={cn(
                "text-xs text-muted-foreground",
                currentLength > maxLength * 0.9 && "text-orange-500",
                currentLength >= maxLength && "text-destructive"
              )}>
                {currentLength}/{maxLength}
              </span>
            )}
          </div>
        )}
        
        <textarea
          className={cn(textareaVariants({ variant: finalVariant, size, className }))}
          ref={textareaRef}
          id={inputId}
          value={value}
          onChange={handleChange}
          maxLength={maxLength}
          {...props}
        />
        
        {/* 错误、成功或帮助文本 */}
        {(error || success || helperText) && (
          <div className="text-sm">
            {error && (
              <p className="text-destructive flex items-center gap-1">
                <span className="text-xs">⚠️</span>
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-green-600 flex items-center gap-1">
                <span className="text-xs">✅</span>
                {success}
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-muted-foreground">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea, textareaVariants }
