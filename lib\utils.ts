import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * 合并 Tailwind CSS 类名的工具函数
 * 使用 clsx 处理条件类名，使用 tailwind-merge 解决类名冲突
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化日期时间
 * @param date - 日期对象或时间戳
 * @param options - 格式化选项
 */
export function formatDate(
  date: Date | string | number,
  options: Intl.DateTimeFormatOptions = {}
) {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }
  
  return new Intl.DateTimeFormat('zh-CN', { ...defaultOptions, ...options }).format(
    new Date(date)
  )
}

/**
 * 格式化相对时间（如：2分钟前）
 * @param date - 日期对象或时间戳
 */
export function formatRelativeTime(date: Date | string | number) {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours}h ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays}d ago`
  }

  return formatDate(targetDate, { month: 'short', day: 'numeric' })
}

/**
 * 生成随机ID
 * @param length - ID长度，默认为8
 */
export function generateId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 防抖函数
 * @param func - 要防抖的函数
 * @param wait - 等待时间（毫秒）
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func - 要节流的函数
 * @param limit - 限制时间（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param obj - 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 检查是否为有效的URL
 * @param string - 要检查的字符串
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

/**
 * 截断文本
 * @param text - 要截断的文本
 * @param maxLength - 最大长度
 * @param suffix - 后缀，默认为'...'
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) {
    return text
  }
  
  return text.slice(0, maxLength - suffix.length) + suffix
}

/**
 * 将字节数转换为人类可读的格式
 * @param bytes - 字节数
 * @param decimals - 小数位数，默认为2
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 获取设备类型
 */
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  if (typeof window === 'undefined') return 'desktop'
  
  const width = window.innerWidth
  
  if (width < 768) return 'mobile'
  if (width < 1024) return 'tablet'
  return 'desktop'
}

/**
 * 将Markdown文本转换为纯文本
 * @param markdown - Markdown格式的文本
 * @returns 纯文本
 */
export function markdownToPlainText(markdown: string): string {
  return markdown
    // 移除代码块
    .replace(/```[\s\S]*?```/g, (match) => {
      // 提取代码块内容，移除语言标识
      const lines = match.split('\n')
      if (lines.length > 2) {
        return lines.slice(1, -1).join('\n')
      }
      return match.replace(/```/g, '')
    })
    // 移除行内代码
    .replace(/`([^`]+)`/g, '$1')
    // 移除标题标记
    .replace(/^#{1,6}\s+/gm, '')
    // 移除粗体和斜体
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // 移除删除线
    .replace(/~~([^~]+)~~/g, '$1')
    // 移除链接，保留文本
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 移除图片
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
    // 移除列表标记
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // 移除引用标记
    .replace(/^>\s+/gm, '')
    // 移除水平线
    .replace(/^[-*_]{3,}$/gm, '')
    // 移除表格
    .replace(/^\|.*\|$/gm, '')
    .replace(/^\|?[-:\s|]+\|?$/gm, '')
    // 清理多余的空行
    .replace(/\n{3,}/g, '\n\n')
    // 清理首尾空白
    .trim()
}

/**
 * 复制文本到剪贴板
 * @param text - 要复制的文本
 * @param convertMarkdown - 是否将Markdown转换为纯文本
 */
export async function copyToClipboard(text: string, convertMarkdown: boolean = false): Promise<boolean> {
  try {
    const textToCopy = convertMarkdown ? markdownToPlainText(text) : text

    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(textToCopy)
      return true
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = textToCopy
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      const success = document.execCommand('copy')
      textArea.remove()
      return success
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 导出格式类型
 */
export type ExportFormat = 'json' | 'markdown' | 'txt'

/**
 * 将对话导出为不同格式
 * @param messages - 消息列表
 * @param format - 导出格式
 * @returns 格式化的内容
 */
export function exportChatToFormat(messages: any[], format: ExportFormat): string {
  const timestamp = new Date().toLocaleString()

  switch (format) {
    case 'json':
      // 简化的JSON格式，只包含核心对话内容
      return JSON.stringify({
        exportedAt: timestamp,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      }, null, 2)

    case 'markdown':
      // Markdown格式
      let markdown = `# Chat Export\n\n*Exported on ${timestamp}*\n\n---\n\n`

      messages.forEach((msg, index) => {
        if (msg.role === 'user') {
          markdown += `## 🙋 User\n\n${msg.content}\n\n`
        } else if (msg.role === 'assistant') {
          markdown += `## 🤖 HumanWhisper\n\n${msg.content}\n\n`
        }

        // 添加分隔线（除了最后一条消息）
        if (index < messages.length - 1) {
          markdown += `---\n\n`
        }
      })

      return markdown

    case 'txt':
      // 纯文本格式
      let text = `Chat Export\n\nExported on ${timestamp}\n\n${'='.repeat(50)}\n\n`

      messages.forEach((msg, index) => {
        if (msg.role === 'user') {
          text += `USER:\n${markdownToPlainText(msg.content)}\n\n`
        } else if (msg.role === 'assistant') {
          text += `HUMANWHISPER:\n${markdownToPlainText(msg.content)}\n\n`
        }

        // 添加分隔线（除了最后一条消息）
        if (index < messages.length - 1) {
          text += `${'-'.repeat(30)}\n\n`
        }
      })

      return text

    default:
      throw new Error(`Unsupported export format: ${format}`)
  }
}

/**
 * 下载文件
 * @param content - 文件内容
 * @param filename - 文件名
 * @param mimeType - MIME类型
 */
export function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/**
 * 等待指定时间
 * @param ms - 等待时间（毫秒）
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
