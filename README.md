# HumanWhisper

> **会说人话的AI** - 让复杂话题变得简单易懂

[![Next.js](https://img.shields.io/badge/Next.js-15.4.2-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.4-blue)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.4.7-38B2AC)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🌟 什么是 HumanWhisper？

HumanWhisper 是一个综合性的 **AI 驱动平台**，专门将复杂信息转化为清晰易懂的语言。它不仅仅是一个聊天机器人，更是你的个人知识伙伴，能用通俗易懂的话解释任何事情 - 就像有一个永远不会厌烦你问题的耐心朋友。

### 🎯 我们的使命
**让知识对每个人都触手可及，无论你的背景或专业水平如何。**

我们相信，复杂的术语和技术语言不应该成为理解的障碍。无论你是学生、专业人士还是好奇的学习者，HumanWhisper 都能将复杂性分解为易于消化的、人性化的解释。

### 🚀 核心理念
- 🗣️ **人性化交流** - 没有术语，只有清晰的解释
- 🎯 **简化优先** - 将复杂话题分解为可理解的部分
- 🧠 **自适应学习** - 根据你的水平定制解释
- 🔒 **隐私保护** - 你的对话保持私密
- ⚡ **即时理解** - 实时简化技术

## ✨ 平台功能

### 🤖 AI 聊天助手
**HumanWhisper 的核心** - 一个智能对话AI，将复杂话题转化为简单易懂的解释。

- **多模型支持** - GPT-4.1-nano、Claude-3.5-haiku、Gemini-2.0-flash
- **自适应解释** - 根据你的需求自动调整复杂度
- **实时流式响应** - 即时回复，带有实时打字指示器
- **可定制设置** - 适合年龄的解释、回复长度、示例和类比

### 🎨 创意AI工具

#### 🖼️ **AI Logo 生成器**
无需设计技能，即时创建专业品牌标志。
- **行业专用模板** - 针对不同商业领域定制
- **多种标志风格** - 现代、经典、极简、创意等多种风格
- **可定制元素** - 颜色、字体、符号和布局
- **高质量输出** - 可缩放的专业级标志

#### 🎬 **AI 视频生成器**
用AI将静态图片转换为动态视频。
- **图片转视频** - 上传任何图片并让它动起来
- **多种动画风格** - 自然、电影、艺术、动态效果
- **自定义动作提示** - 精确描述你希望图片如何运动
- **专业品质** - 高分辨率视频输出，可直接使用

#### ✍️ **AI 提示词优化器**
完善你的AI提示词，在任何AI模型上获得更好的结果。
- **提示词工程** - 将模糊想法转化为结构化、有效的提示词
- **多用途模板** - 创意写作、编程、分析和通用用途
- **模型特定优化** - 针对不同AI模型量身定制
- **即时结果** - 实时提示词增强和建议

### 💡 智能功能
- **复杂度分析** - 自动评估问题难度
- **动态资源分配** - 智能token管理，优化性能
- **个性化体验** - 针对不同学习水平的可定制解释
- **隐私优先设计** - 本地聊天记录存储，无数据收集

## 🛠️ 技术栈

### 前端技术
- **Next.js 15.4.2** - 带有App Router的React全栈框架
- **TypeScript** - 类型安全的JavaScript，提供更好的开发体验
- **Tailwind CSS** - 实用优先的CSS框架，快速UI开发
- **Radix UI + shadcn/ui** - 可访问、可定制的UI组件库
- **Zustand** - 轻量级状态管理解决方案

### 后端与API
- **Clerk** - 完整的用户认证和管理系统
- **Creem.io** - 安全的支付处理和订阅管理
- **多AI提供商** - GPT、Claude、Gemini提供多样化AI能力
- **自定义API路由** - Next.js API路由处理服务端逻辑

### AI与创意工具
- **OpenAI GPT模型** - 先进的语言理解和生成
- **Anthropic Claude** - 复杂推理和分析
- **Google Gemini** - 多模态AI能力
- **图像生成API** - 专业标志创建
- **视频生成API** - 图片转视频技术

### 部署与基础设施
- **Vercel** - 带有全球CDN的无服务器部署平台
- **无数据库架构** - 利用Clerk元数据存储用户数据
- **企业级安全** - HMAC-SHA256签名、webhook验证
- **实时功能** - 流式响应和实时更新

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-username/humanwhisper.git
cd humanwhisper
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **环境配置**
```bash
cp .env.example .env.local
```

配置以下环境变量：
```env
# Clerk 用户认证
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# AI 服务配置
AIHUBMIX_API_KEY=your_aihubmix_api_key
SILICONFLOW_API_KEYS=your_api_keys_comma_separated
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# 支付处理
CREEM_API_KEY=your_creem_api_key
CREEM_PRODUCT_ID=your_product_id
CREEM_WEBHOOK_SECRET=your_webhook_secret

# 应用设置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SUPPORTED_MODELS=gpt-4.1-nano,claude-3-5-haiku-latest,gemini-2.0-flash
```

4. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用运行。

## 📁 项目结构

```
humanwhisper/
├── app/                    # Next.js App Router 页面
│   ├── api/               # API 路由
│   ├── chat/              # 聊天页面
│   ├── dashboard/         # 用户仪表板
│   └── ...
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── chat/             # 聊天相关组件
│   ├── auth/             # 认证组件
│   └── ...
├── lib/                   # 工具函数和客户端
│   ├── ai-client.ts      # AI 服务客户端
│   ├── creem-client.ts   # 支付客户端
│   └── ...
├── store/                 # Zustand 状态管理
├── types/                 # TypeScript 类型定义
└── public/               # 静态资源
```

## 🔧 开发脚本

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 项目初始化
npm run setup
```

## 🌐 部署

### Vercel 部署（推荐）

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 手动部署

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 🔒 安全特性

- **签名验证** - 所有 webhook 请求都经过 HMAC-SHA256 验证
- **幂等性保护** - 防止重复处理支付事件
- **时序攻击防护** - 使用安全的字符串比较
- **请求验证** - 完整的请求体和头部验证

## 📊 监控和分析

- **用户使用统计** - 完整的积分使用和模型选择分析
- **性能监控** - 响应时间和错误率追踪
- **支付监控** - 完整的支付事件日志

## 🤝 贡献指南

我们欢迎所有形式的贡献！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - 强大的 React 框架
- [Clerk](https://clerk.com/) - 现代化的用户认证
- [Radix UI](https://www.radix-ui.com/) - 无障碍的 UI 组件
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Creem.io](https://creem.io/) - 简单的支付处理

## 📞 联系我们

- 网站: [https://humanwhisper.com](https://humanwhisper.com)
- 邮箱: <EMAIL>
- Twitter: [@humanwhisper](https://twitter.com/humanwhisper)

---

**让复杂变简单，让 AI 更人性化** ❤️
