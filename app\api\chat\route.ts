import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { getAIClient } from '@/lib/ai-client'
import { buildOptimizedMessageSequence } from '@/lib/prompts/prompt-builder'
import { optimizePromptForModel } from '@/lib/prompt-optimizer'
import { estimateTokenCount, estimateCost } from '@/lib/ai-utils'
import { isSupportedModel } from '@/lib/ai-client'
import { RATE_LIMITS, ERROR_MESSAGES } from '@/lib/constants'
import { deductCreditsForModel } from '@/lib/user-management'

import type { ChatSettings, Message, APIRequest } from '@/types'

// 速率限制存储（生产环境应使用Redis等外部存储）
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * 聊天API - POST请求处理
 * 支持流式和非流式响应
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 解析请求体
    const body = await request.json()
    const {
      message,
      images,
      settings,
      conversationHistory = [],
      stream = false
    }: {
      message: string
      images?: string[]
      settings: ChatSettings
      conversationHistory?: Message[]
      stream?: boolean
    } = body

    // 输入验证
    const validationResult = validateRequest(message, settings)
    if (!validationResult.isValid) {
      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      )
    }

    // 检查并扣除积分（包含图像处理费用）
    const imageCount = images?.length || 0
    const creditsDeducted = await deductCreditsForModel(userId, settings.model, imageCount)
    if (!creditsDeducted) {
      return NextResponse.json(
        { 
          error: 'Insufficient credits, please upgrade to Premium or wait for credit reset',
          upgradeUrl: 'https://humanwhisper.com/pricing'
        },
        { status: 402 } // Payment Required
      )
    }

    // 速率限制检查
    const clientIP = getClientIP(request)
    const rateLimitResult = checkRateLimit(clientIP)
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { 
          error: ERROR_MESSAGES.RATE_LIMIT_ERROR,
          retryAfter: rateLimitResult.retryAfter 
        },
        { status: 429 }
      )
    }



    // 获取AI客户端
    const aiClient = getAIClient()

    // 构建优化的消息序列
    const messageSequence = buildOptimizedMessageSequence(
      message,
      settings,
      conversationHistory,
      images // 传递图像数据
    )

    // 针对模型优化提示词
    const systemMessage = messageSequence.find(m => m.role === 'system')
    if (systemMessage) {
      systemMessage.content = optimizePromptForModel(
        systemMessage.content,
        settings.model,
        settings
      )
    }

    // 构建API请求，包含复杂度评分
    const apiRequest: APIRequest = {
      model: settings.model,
      messages: messageSequence,
      settings,
      stream
    }

    // 处理流式响应
    if (stream) {
      return handleStreamResponse(aiClient, apiRequest, clientIP, message)
    }

    // 处理标准响应
    return handleStandardResponse(aiClient, apiRequest, clientIP)

  } catch (error) {
    // 聊天API错误，静默处理
    return NextResponse.json(
      { error: ERROR_MESSAGES.UNKNOWN_ERROR },
      { status: 500 }
    )
  }
}

/**
 * 验证请求参数
 */
function validateRequest(message: string, settings: ChatSettings) {
  if (!message || typeof message !== 'string') {
    return { isValid: false, error: 'Message content cannot be empty' }
  }

  if (message.length > 10000) {
    return { isValid: false, error: 'Message content too long, please keep within 10000 characters' }
  }

  if (!settings || !settings.model) {
    return { isValid: false, error: 'Missing required settings parameters' }
  }

  if (!isSupportedModel(settings.model)) {
    return { isValid: false, error: 'Unsupported AI model' }
  }

  return { isValid: true }
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown'
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

/**
 * 检查速率限制
 */
function checkRateLimit(clientIP: string) {
  const now = Date.now()
  const windowMs = 60 * 1000 // 1分钟窗口
  const limit = RATE_LIMITS.requestsPerMinute
  
  const clientData = rateLimitStore.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    // 重置或初始化
    rateLimitStore.set(clientIP, {
      count: 1,
      resetTime: now + windowMs
    })
    return { allowed: true }
  }
  
  if (clientData.count >= limit) {
    return { 
      allowed: false, 
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
    }
  }
  
  // 增加计数
  clientData.count++
  rateLimitStore.set(clientIP, clientData)
  
  return { allowed: true }
}

/**
 * 处理流式响应
 */
async function handleStreamResponse(
  aiClient: any,
  apiRequest: APIRequest,
  _clientIP: string,
  _userMessage: string
) {
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    async start(controller) {
      try {
        const startTime = Date.now()
        let fullResponse = ''
        let tokenCount = 0
        
        // 发送开始事件
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({
            type: 'start',
            model: apiRequest.model
          })}\n\n`)
        )



        // 处理AI流式响应
        for await (const chunk of aiClient.chatCompletionStream(apiRequest)) {
          const content = chunk.choices[0]?.delta?.content

          if (content) {
            fullResponse += content
            tokenCount = estimateTokenCount(fullResponse)

            // 发送内容块
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'content',
                content: content,
                tokenCount
              })}\n\n`)
            )
          }
          
          // 检查是否完成
          if (chunk.choices[0]?.finish_reason) {

            const endTime = Date.now()
            const processingTime = endTime - startTime
            const cost = estimateCost(tokenCount, apiRequest.model)

            // 发送完成事件
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'complete',
                fullResponse,
                metadata: {
                  model: apiRequest.model,
                  tokenCount,
                  processingTime,
                  cost,
                  finishReason: chunk.choices[0].finish_reason
                }
              })}\n\n`)
            )

            break
          }
        }
        
        // 发送结束标记
        controller.enqueue(encoder.encode('data: [DONE]\n\n'))
        controller.close()
        
      } catch (error) {
        // 流式响应错误，静默处理
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : ERROR_MESSAGES.API_ERROR
          })}\n\n`)
        )
        controller.close()
      }
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}

/**
 * 处理标准响应
 */
async function handleStandardResponse(
  aiClient: any,
  apiRequest: APIRequest,
  _clientIP: string
) {
  try {
    const startTime = Date.now()
    
    // 调用AI API
    const response = await aiClient.chatCompletion(apiRequest)
    
    const endTime = Date.now()
    const processingTime = endTime - startTime
    
    // 提取响应内容
    const content = response.choices[0]?.message?.content || ''
    const tokenUsage = response.usage
    const cost = estimateCost(tokenUsage.total_tokens, apiRequest.model)
    
    // 构建响应
    const result = {
      content,
      metadata: {
        model: apiRequest.model,
        tokenUsage,
        processingTime,
        cost,
        finishReason: response.choices[0]?.finish_reason,
        requestId: response.id,
        timestamp: new Date().toISOString()
      }
    }
    
    return NextResponse.json(result)
    
  } catch (error) {
    // 标准响应错误，静默处理

    // 根据错误类型返回适当的错误信息
    let errorMessage: string = ERROR_MESSAGES.API_ERROR
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'API configuration error'
        statusCode = 401
      } else if (error.message.includes('rate limit')) {
        errorMessage = ERROR_MESSAGES.RATE_LIMIT_ERROR
        statusCode = 429
      } else if (error.message.includes('network')) {
        errorMessage = ERROR_MESSAGES.NETWORK_ERROR
        statusCode = 503
      }
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}







/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    }
  })
}
