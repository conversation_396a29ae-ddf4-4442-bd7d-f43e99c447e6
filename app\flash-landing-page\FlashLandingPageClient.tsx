'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Rocket, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Clock, Mail, Share2, Target } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 项目类型选项
const PROJECT_TYPES = [
  { id: 'ai_tool', name: 'AI Tool', description: 'AI-powered software, automation, machine learning' },
  { id: 'game', name: 'Game', description: 'Video games, mobile games, gaming platforms' },
  { id: 'community', name: 'Community', description: 'Social platforms, forums, networking sites' },
  { id: 'podcast', name: 'Podcast', description: 'Audio content, podcast platforms, media shows' },
  { id: 'saas', name: 'SaaS Platform', description: 'Software as a service, business tools' },
  { id: 'ecommerce', name: 'E-commerce', description: 'Online stores, marketplaces, retail platforms' },
  { id: 'education', name: 'Education', description: 'Learning platforms, courses, educational tools' },
  { id: 'health', name: 'Health & Fitness', description: 'Wellness apps, fitness platforms, health tools' },
  { id: 'finance', name: 'Finance', description: 'Fintech, investment platforms, financial tools' },
  { id: 'custom', name: 'Custom Project', description: 'Define your own project category' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, web browsing, code execution'
  },
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Logical reasoning, structured output, marketing strategy'
  }
]

interface GeneratedLandingPage {
  heroSection: {
    headline: string
    subheadline: string
    cta: string
  }
  emailCapture: {
    title: string
    placeholder: string
    buttonText: string
    privacyNote: string
  }
  socialTeasers: {
    twitter: string
    linkedin: string
    facebook: string
  }
  countdown: {
    title: string
    description: string
  }
}

export function FlashLandingPageClient() {
  // 获取当前日期+2天作为默认发布日期
  const getDefaultLaunchDate = () => {
    const today = new Date()
    const launchDate = new Date(today)
    launchDate.setDate(today.getDate() + 2)
    return launchDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // 表单状态
  const [projectType, setProjectType] = useState('')
  const [customType, setCustomType] = useState('')
  const [domainName, setDomainName] = useState('')
  const [painPoint, setPainPoint] = useState('')
  const [launchDate, setLaunchDate] = useState(getDefaultLaunchDate())
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedPage, setGeneratedPage] = useState<GeneratedLandingPage | null>(null)
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成Landing Page
  const handleGenerate = async () => {
    if (!projectType || !domainName.trim() || !painPoint.trim() || !launchDate.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/flash-landing-page', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectType: projectType === 'custom' ? customType : projectType,
          domainName: domainName.trim(),
          painPoint: painPoint.trim(),
          launchDate: launchDate.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate landing page')
      }

      const data = await response.json()
      setGeneratedPage(data.landingPage)

      toast({
        title: "Landing Page Generated!",
        description: "Complete coming soon page content created successfully.",
      })

    } catch (error) {
      setError('Failed to generate landing page. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制内容到剪贴板
  const copyToClipboard = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard.`,
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                AI Flash Landing Page
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Coming Soon Pages
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>complete coming soon landing pages in minutes</strong>. 
              Create hero copy, email capture, and social media teasers for your domain launch.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                48-Hour Ready
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Complete Package
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Launch Ready
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Rocket className="h-5 w-5 text-primary" />
                      Landing Page Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Project Type */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Project Type *</label>
                      <Select value={projectType} onValueChange={setProjectType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your project category" />
                        </SelectTrigger>
                        <SelectContent>
                          {PROJECT_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Type Input */}
                    {projectType === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Project Type *</label>
                        <input
                          type="text"
                          placeholder="Describe your project category"
                          value={customType}
                          onChange={(e) => setCustomType(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Domain Name */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Domain Name *</label>
                      <input
                        type="text"
                        placeholder="e.g., myawesomeapp.com"
                        value={domainName}
                        onChange={(e) => setDomainName(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        The domain you want to launch (with or without .com)
                      </p>
                    </div>

                    {/* Pain Point */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Core Pain Point *</label>
                      <Textarea
                        placeholder="Describe the main problem your project solves in one sentence..."
                        value={painPoint}
                        onChange={(e) => setPainPoint(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        The key problem or need your project addresses
                      </p>
                    </div>

                    {/* Launch Date */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Expected Launch Date *</label>
                      <input
                        type="text"
                        placeholder={`e.g., ${getDefaultLaunchDate()}, Q2 2024, Summer 2024`}
                        value={launchDate}
                        onChange={(e) => setLaunchDate(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        When you plan to launch (can be approximate, defaults to 2 days from today)
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Landing Page...
                        </>
                      ) : (
                        <>
                          <Rocket className="h-4 w-4 mr-2" />
                          Generate Landing Page
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Generated Landing Page
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedPage ? (
                      <div className="space-y-6">
                        {/* Hero Section */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Target className="h-4 w-4" />
                              Hero Section
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                `${generatedPage.heroSection.headline}\n${generatedPage.heroSection.subheadline}\n${generatedPage.heroSection.cta}`,
                                'Hero Section'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950 p-4 rounded-lg space-y-2">
                            <h3 className="text-lg font-bold">{generatedPage.heroSection.headline}</h3>
                            <p className="text-sm text-muted-foreground">{generatedPage.heroSection.subheadline}</p>
                            <div className="pt-2">
                              <span className="bg-primary text-primary-foreground px-3 py-1 rounded text-sm font-medium">
                                {generatedPage.heroSection.cta}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Email Capture */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              Email Capture
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                `${generatedPage.emailCapture.title}\nPlaceholder: ${generatedPage.emailCapture.placeholder}\nButton: ${generatedPage.emailCapture.buttonText}\n${generatedPage.emailCapture.privacyNote}`,
                                'Email Capture'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-3">
                            <h4 className="font-medium">{generatedPage.emailCapture.title}</h4>
                            <div className="flex gap-2">
                              <input
                                type="email"
                                placeholder={generatedPage.emailCapture.placeholder}
                                className="flex-1 px-3 py-2 border rounded text-sm"
                                disabled
                              />
                              <button className="bg-primary text-primary-foreground px-4 py-2 rounded text-sm font-medium">
                                {generatedPage.emailCapture.buttonText}
                              </button>
                            </div>
                            <p className="text-xs text-muted-foreground">{generatedPage.emailCapture.privacyNote}</p>
                          </div>
                        </div>

                        {/* Countdown */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              Countdown Section
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                `${generatedPage.countdown.title}\n${generatedPage.countdown.description}`,
                                'Countdown Section'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg text-center space-y-2">
                            <h4 className="font-medium">{generatedPage.countdown.title}</h4>
                            <p className="text-sm text-muted-foreground">{generatedPage.countdown.description}</p>
                            <div className="flex justify-center gap-4 pt-2">
                              {['Days', 'Hours', 'Minutes', 'Seconds'].map((unit) => (
                                <div key={unit} className="text-center">
                                  <div className="bg-white dark:bg-gray-800 rounded p-2 min-w-[50px]">
                                    <div className="text-lg font-bold">00</div>
                                  </div>
                                  <div className="text-xs mt-1">{unit}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Social Teasers */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Share2 className="h-4 w-4" />
                              Social Media Teasers
                            </h4>
                          </div>
                          <div className="space-y-3">
                            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-xs font-medium text-blue-600">Twitter/X</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(generatedPage.socialTeasers.twitter, 'Twitter teaser')}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm">{generatedPage.socialTeasers.twitter}</p>
                            </div>
                            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-xs font-medium text-blue-600">LinkedIn</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(generatedPage.socialTeasers.linkedin, 'LinkedIn teaser')}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm">{generatedPage.socialTeasers.linkedin}</p>
                            </div>
                            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-xs font-medium text-blue-600">Facebook</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(generatedPage.socialTeasers.facebook, 'Facebook teaser')}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm">{generatedPage.socialTeasers.facebook}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Rocket className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your landing page content will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Launch with a Coming Soon Page?
            </h2>
            <p className="text-xl text-muted-foreground">
              Build anticipation, capture leads, and validate your idea before the official launch
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Email List Building</h3>
                <p className="text-sm text-muted-foreground">
                  Capture interested prospects before launch to ensure day-one success
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Create Urgency</h3>
                <p className="text-sm text-muted-foreground">
                  Countdown timers and limited access create FOMO and drive action
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Share2 className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Social Buzz</h3>
                <p className="text-sm text-muted-foreground">
                  Ready-made social media content to generate excitement and shares
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Market Validation</h3>
                <p className="text-sm text-muted-foreground">
                  Test demand and gather feedback before investing in full development
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">48-Hour Setup</h3>
                <p className="text-sm text-muted-foreground">
                  Complete landing page copy ready to deploy within 48 hours
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Complete Package</h3>
                <p className="text-sm text-muted-foreground">
                  Hero copy, email capture, countdown, and social media content included
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              From domain to launch-ready page in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Project Type</h3>
              <p className="text-sm text-muted-foreground">
                Select from AI tools, games, communities, SaaS, or define your own category
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-red-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Add Domain & Details</h3>
              <p className="text-sm text-muted-foreground">
                Input your domain name, core pain point, and expected launch timeline
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Generate Content</h3>
              <p className="text-sm text-muted-foreground">
                AI creates hero copy, email capture, countdown, and social media teasers
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Launch & Promote</h3>
              <p className="text-sm text-muted-foreground">
                Deploy your page and use the social content to build anticipation
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Landing Page Examples
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different project types create compelling coming soon pages
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🤖</span>
                  AI Tool Launch
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-3">
                  <h3 className="font-bold text-lg">"AI Writing Just Got Personal"</h3>
                  <p className="text-sm text-muted-foreground">
                    "Stop struggling with writer's block. Our AI learns your voice and creates content that sounds authentically you."
                  </p>
                  <div className="pt-2">
                    <span className="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium">
                      Join the Waitlist
                    </span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-3">
                  Perfect for AI tools focusing on personalization and solving creative blocks
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🎮</span>
                  Game Launch
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg space-y-3">
                  <h3 className="font-bold text-lg">"Adventure Awaits This Summer"</h3>
                  <p className="text-sm text-muted-foreground">
                    "Tired of the same old games? Discover a world where every choice matters and every player's story is unique."
                  </p>
                  <div className="pt-2">
                    <span className="bg-purple-600 text-white px-3 py-1 rounded text-sm font-medium">
                      Get Early Access
                    </span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-3">
                  Great for games emphasizing unique experiences and player agency
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💼</span>
                  SaaS Platform
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg space-y-3">
                  <h3 className="font-bold text-lg">"Project Management, Simplified"</h3>
                  <p className="text-sm text-muted-foreground">
                    "Stop juggling multiple tools. One platform for planning, tracking, and delivering projects on time, every time."
                  </p>
                  <div className="pt-2">
                    <span className="bg-green-600 text-white px-3 py-1 rounded text-sm font-medium">
                      Start Free Trial
                    </span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-3">
                  Ideal for SaaS tools focusing on simplification and efficiency
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">👥</span>
                  Community Platform
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg space-y-3">
                  <h3 className="font-bold text-lg">"Where Creators Connect"</h3>
                  <p className="text-sm text-muted-foreground">
                    "Tired of creating in isolation? Join a community where creators collaborate, learn, and grow together."
                  </p>
                  <div className="pt-2">
                    <span className="bg-orange-600 text-white px-3 py-1 rounded text-sm font-medium">
                      Join the Community
                    </span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-3">
                  Perfect for communities emphasizing connection and collaboration
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about creating flash landing pages
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How quickly can I get my landing page live?",
                  answer: "With our generated copy, you can have a complete coming soon page live within 48 hours. The copy is ready-to-use and just needs to be implemented on your domain with basic HTML/CSS."
                },
                {
                  question: "What's included in the complete package?",
                  answer: "You get hero section copy (headline, subheadline, CTA), email capture form content, countdown section text, and social media teasers for Twitter, LinkedIn, and Facebook - everything needed for a successful launch."
                },
                {
                  question: "Can I customize the generated copy?",
                  answer: "Absolutely! The generated copy is a professional starting point that you can customize to match your brand voice, add specific details, or adjust the tone as needed."
                },
                {
                  question: "How do I implement the countdown timer?",
                  answer: "We provide the copy and structure for the countdown section. You'll need to add JavaScript functionality for the actual timer, or use tools like Countdown.js or similar libraries."
                },
                {
                  question: "What makes a coming soon page effective?",
                  answer: "Effective coming soon pages create curiosity, clearly communicate value, capture emails with incentives, create urgency with countdowns, and provide social proof through early interest."
                },
                {
                  question: "Should I collect emails before launching?",
                  answer: "Yes! Building an email list before launch ensures you have an audience ready on day one. Offer early access, exclusive content, or special pricing to incentivize signups."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
