import { MessageCircle } from 'lucide-react'

// 全局加载组件
export default function Loading() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        {/* 品牌图标动画 */}
        <div className="relative">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary animate-pulse">
            <MessageCircle className="h-8 w-8 text-primary-foreground" />
          </div>
          
          {/* 环形加载动画 */}
          <div className="absolute inset-0 rounded-full border-4 border-primary/20 border-t-primary animate-spin"></div>
        </div>
        
        {/* 加载文本 */}
        <div className="text-center">
          <h2 className="text-lg font-semibold text-foreground mb-2">
            HumanWhisper is preparing...
          </h2>
          <p className="text-sm text-muted-foreground">
            Preparing the best conversation experience for you
          </p>
        </div>
        
        {/* 点状加载指示器 */}
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  )
}
