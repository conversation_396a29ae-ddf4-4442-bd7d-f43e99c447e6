'use client'

import { useState } from 'react'
import { Crown, Sparkles, <PERSON>, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'

export function PremiumUpgrade() {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleUpgrade = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/payment/create-checkout', {
        method: 'POST'
      })

      const result = await response.json()

      if (result.success && result.checkout_url) {
        // 跳转到Creem支付页面
        window.location.href = result.checkout_url
      } else {
        // 根据错误类型提供更友好的错误信息
        let errorTitle = "Payment Creation Failed"
        let errorDescription = result.error || "Please try again later"

        if (result.error?.includes('Payment service not configured')) {
          errorTitle = "Payment Service Unavailable"
          errorDescription = "Payment functionality is under maintenance, please try again later"
        } else if (result.error?.includes('Unauthorized')) {
          errorTitle = "Please Login First"
          errorDescription = "Please login before upgrading"
        } else if (result.error?.includes('Customer not found')) {
          errorTitle = "Account Information Error"
          errorDescription = "Please refresh the page and try again. If the problem persists, contact support"
        } else if (result.error?.includes('Creem API Error')) {
          errorTitle = "Payment Service Error"
          errorDescription = "Payment service is temporarily unavailable, please try again later"
        }

        toast({
          title: errorTitle,
          description: errorDescription,
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Network Error",
        description: "Please check your network connection and try again",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const premiumFeatures = [
    { icon: Sparkles, text: "3600 credits monthly", highlight: true },
    { icon: Check, text: "Unlock all AI models" },
    { icon: Check, text: "Priority customer support" },
    { icon: Check, text: "Ad-free experience" },
    { icon: Check, text: "Early access to new features" },
    { icon: Check, text: "Invitation code generation" }
  ]

  return (
    <Card className="w-full max-w-md border-2 border-gradient-to-r from-purple-500 to-blue-500">
      <CardHeader className="text-center bg-gradient-to-r from-purple-500/10 to-blue-500/10">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Crown className="h-6 w-6 text-yellow-500" />
          <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
            Premium
          </Badge>
        </div>
        <CardTitle className="text-2xl">Upgrade to Premium</CardTitle>
        <div className="flex items-center justify-center gap-2">
          <span className="text-3xl font-bold">$29.99</span>
          <span className="text-muted-foreground">/month</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Premium特权列表 */}
        <div className="space-y-3">
          {premiumFeatures.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <feature.icon className={`h-4 w-4 ${feature.highlight ? 'text-purple-500' : 'text-green-500'}`} />
              <span className={`text-sm ${feature.highlight ? 'font-medium text-purple-700' : ''}`}>
                {feature.text}
              </span>
            </div>
          ))}
        </div>

        {/* 对比说明 */}
        <div className="bg-muted rounded-lg p-4 space-y-2">
          <h4 className="font-medium text-sm">Free vs Premium</h4>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <p className="font-medium text-muted-foreground">Free Users</p>
              <p>• 100 credits on signup</p>
              <p>• Basic models only</p>
              <p>• Limited features</p>
            </div>
            <div>
              <p className="font-medium text-purple-600">Premium Users</p>
              <p>• 3600 credits monthly</p>
              <p>• All AI models</p>
              <p>• Exclusive privileges</p>
            </div>
          </div>
        </div>

        {/* 升级按钮 */}
        <Button 
          onClick={handleUpgrade}
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
          size="lg"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Creating payment...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Crown className="h-4 w-4" />
              Upgrade to Premium Now
              <ArrowRight className="h-4 w-4" />
            </div>
          )}
        </Button>

        {/* 安全提示 */}
        <p className="text-xs text-muted-foreground text-center">
          🔒 Secure payment powered by Creem.io
        </p>
      </CardContent>
    </Card>
  )
}
