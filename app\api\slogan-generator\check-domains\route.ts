import { NextRequest, NextResponse } from 'next/server'
import { promises as dns } from 'dns'

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const { domains } = body

    // 输入验证
    if (!Array.isArray(domains) || domains.length === 0) {
      return NextResponse.json(
        { error: 'Domains array is required' },
        { status: 400 }
      )
    }

    // 限制批量检查数量
    if (domains.length > 20) {
      return NextResponse.json(
        { error: 'Too many domains to check at once (max 20)' },
        { status: 400 }
      )
    }

    // 验证所有域名格式
    const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/
    const cleanDomains = domains
      .map(domain => domain?.trim()?.toLowerCase())
      .filter(domain => domain && domainRegex.test(domain))

    if (cleanDomains.length === 0) {
      return NextResponse.json(
        { error: 'No valid domains provided' },
        { status: 400 }
      )
    }

    // 并行检查所有域名
    const results = await Promise.allSettled(
      cleanDomains.map(async (domain) => {
        const available = await checkDomainAvailability(domain)
        return {
          domain,
          available,
          checkedAt: new Date().toISOString()
        }
      })
    )

    // 处理结果
    const domainResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        return {
          domain: cleanDomains[index],
          available: false, // 检查失败时保守地认为不可用
          error: 'Check failed',
          checkedAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({
      results: domainResults,
      summary: {
        total: domainResults.length,
        available: domainResults.filter(r => r.available).length,
        taken: domainResults.filter(r => !r.available).length
      },
      checkedAt: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to check domain availability' },
      { status: 500 }
    )
  }
}

// 检查域名可用性的函数
async function checkDomainAvailability(domain: string): Promise<boolean> {
  try {
    // 尝试解析 .com 域名
    const fullDomain = `${domain}.com`
    
    // 使用DNS查询检查域名是否已注册
    await dns.resolve(fullDomain, 'A')
    
    // 如果能解析到A记录，说明域名已被注册
    return false
    
  } catch (error: any) {
    // 如果DNS查询失败，可能是域名未注册
    if (error.code === 'ENOTFOUND' || error.code === 'ENODATA') {
      // 进一步检查是否有其他记录类型
      try {
        await dns.resolve(`${domain}.com`, 'CNAME')
        return false // 有CNAME记录，域名已注册
      } catch (cnameError: any) {
        if (cnameError.code === 'ENOTFOUND' || cnameError.code === 'ENODATA') {
          try {
            await dns.resolve(`${domain}.com`, 'MX')
            return false // 有MX记录，域名已注册
          } catch (mxError: any) {
            if (mxError.code === 'ENOTFOUND' || mxError.code === 'ENODATA') {
              // 没有找到任何DNS记录，可能可用
              return true
            }
            return false
          }
        }
        return false
      }
    }
    
    // 其他错误情况，保守地认为不可用
    return false
  }
}
