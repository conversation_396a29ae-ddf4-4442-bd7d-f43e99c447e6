// Creem.io API客户端

interface CreemCheckoutRequest {
  request_id?: string
  product_id: string
  units?: number
  customer?: {
    id?: string
    email?: string
  }
  success_url?: string
  metadata?: Record<string, any>
}

interface CreemCheckoutResponse {
  id: string
  checkout_url: string
  success_url: string
  status: string
  customer: string
  subscription?: string
  order?: {
    id: string
    customer: string
    product: string
    amount: number
    currency: string
    status: string
  }
}

class CreemClient {
  private apiKey: string
  private baseUrl: string

  constructor(apiKey: string, testMode = false) {
    this.apiKey = apiKey
    this.baseUrl = testMode 
      ? 'https://test-api.creem.io' 
      : 'https://api.creem.io'
  }

  async createCheckoutSession(data: CreemCheckoutRequest): Promise<CreemCheckoutResponse> {
    const response = await fetch(`${this.baseUrl}/v1/checkouts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Creem API Error: ${response.status} - ${error}`)
    }

    return response.json()
  }

  async getCheckoutSession(checkoutId: string) {
    console.log('Getting checkout session:', checkoutId)
    console.log('URL:', `${this.baseUrl}/v1/checkouts/${checkoutId}`)

    const response = await fetch(`${this.baseUrl}/v1/checkouts/${checkoutId}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    })

    console.log('Checkout session response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Checkout session error:', errorText)
      throw new Error(`Failed to get checkout session: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('Checkout session result:', result)
    return result
  }

  async getCustomer(customerId: string) {
    const response = await fetch(`${this.baseUrl}/v1/customers?customer_id=${encodeURIComponent(customerId)}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    })

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Customer not found: ${customerId}`)
      }
      throw new Error(`Failed to get customer: ${response.status}`)
    }

    return response.json()
  }

  async getCustomerByEmail(email: string) {
    const response = await fetch(`${this.baseUrl}/v1/customers?email=${encodeURIComponent(email)}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    })

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Customer not found: ${email}`)
      }
      throw new Error(`Failed to get customer by email: ${response.status}`)
    }

    return response.json()
  }

  async checkCustomerExists(customerId: string): Promise<boolean> {
    try {
      await this.getCustomer(customerId)
      return true
    } catch (error) {
      if (error instanceof Error && error.message.includes('Customer not found')) {
        return false
      }
      throw error
    }
  }

  async getSubscription(subscriptionId: string) {
    const response = await fetch(`${this.baseUrl}/v1/subscriptions/${subscriptionId}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to get subscription: ${response.status}`)
    }

    return response.json()
  }

  // 验证webhook签名
  verifyWebhookSignature(payload: string, signature: string): boolean {
    try {
      // 获取webhook密钥
      const webhookSecret = process.env.CREEM_WEBHOOK_SECRET
      if (!webhookSecret) {
        return false
      }

      // 验证签名格式
      if (!signature || typeof signature !== 'string') {
        return false
      }

      // Creem webhook密钥格式通常是 "whsec_xxx"，需要提取实际密钥
      let actualSecret = webhookSecret
      if (webhookSecret.startsWith('whsec_')) {
        actualSecret = webhookSecret.slice(6) // 移除 "whsec_" 前缀
      }

      // Creem使用HMAC-SHA256签名
      const crypto = require('crypto')
      const expectedSignature = crypto
        .createHmac('sha256', actualSecret)
        .update(payload, 'utf8')
        .digest('hex')

      // 处理不同的签名格式
      let providedSignature = signature
      if (signature.startsWith('sha256=')) {
        providedSignature = signature.slice(7)
      } else if (signature.startsWith('v1=')) {
        providedSignature = signature.slice(3)
      }

      // 验证签名长度（SHA256 hex应该是64字符）
      if (providedSignature.length !== 64 || expectedSignature.length !== 64) {
        return false
      }

      // 使用时间安全的比较防止时序攻击
      try {
        return crypto.timingSafeEqual(
          Buffer.from(expectedSignature, 'hex'),
          Buffer.from(providedSignature, 'hex')
        )
      } catch (bufferError) {
        // 如果Buffer创建失败（比如非hex字符），返回false
        return false
      }
    } catch (error) {
      return false
    }
  }
}

// 创建Creem客户端实例
export const creem = new CreemClient(
  process.env.CREEM_API_KEY!,
  process.env.NEXT_PUBLIC_CREEM_TEST_MODE === 'true'
)

export type { CreemCheckoutRequest, CreemCheckoutResponse }
