/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"], // 基于class的暗色模式
  content: [
    './pages/**/*.{ts,tsx}', // Pages目录下的所有TS/TSX文件
    './components/**/*.{ts,tsx}', // Components目录下的所有TS/TSX文件
    './app/**/*.{ts,tsx}', // App目录下的所有TS/TSX文件
    './src/**/*.{ts,tsx}', // Src目录下的所有TS/TSX文件
  ],
  prefix: "", // CSS类名前缀
  theme: {
    container: {
      center: true, // 容器居中
      padding: "2rem", // 容器内边距
      screens: {
        "2xl": "1400px", // 超大屏幕断点
      },
    },
    extend: {
      colors: {
        // shadcn/ui 基础颜色系统
        border: "hsl(var(--border))", // 边框颜色
        input: "hsl(var(--input))", // 输入框颜色
        ring: "hsl(var(--ring))", // 焦点环颜色
        background: "hsl(var(--background))", // 背景颜色
        foreground: "hsl(var(--foreground))", // 前景颜色
        primary: {
          DEFAULT: "hsl(var(--primary))", // 主色调
          foreground: "hsl(var(--primary-foreground))", // 主色调前景
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))", // 次要色调
          foreground: "hsl(var(--secondary-foreground))", // 次要色调前景
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))", // 危险色调
          foreground: "hsl(var(--destructive-foreground))", // 危险色调前景
        },
        muted: {
          DEFAULT: "hsl(var(--muted))", // 静音色调
          foreground: "hsl(var(--muted-foreground))", // 静音色调前景
        },
        accent: {
          DEFAULT: "hsl(var(--accent))", // 强调色调
          foreground: "hsl(var(--accent-foreground))", // 强调色调前景
        },
        popover: {
          DEFAULT: "hsl(var(--popover))", // 弹出层颜色
          foreground: "hsl(var(--popover-foreground))", // 弹出层前景
        },
        card: {
          DEFAULT: "hsl(var(--card))", // 卡片颜色
          foreground: "hsl(var(--card-foreground))", // 卡片前景
        },
        // HumanWhisper品牌色彩系统
        whisper: {
          50: '#f0f9ff', // 最浅蓝色
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9', // 主蓝色
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e', // 最深蓝色
        },
        human: {
          50: '#fef7ee', // 最浅橙色
          100: '#fdedd3',
          200: '#fbd7a5',
          300: '#f8bb6d',
          400: '#f59532',
          500: '#f2750a', // 主橙色
          600: '#e35d05',
          700: '#bc4508',
          800: '#95370e',
          900: '#792f0f', // 最深橙色
        },
      },
      borderRadius: {
        lg: "var(--radius)", // 大圆角
        md: "calc(var(--radius) - 2px)", // 中圆角
        sm: "calc(var(--radius) - 4px)", // 小圆角
      },
      keyframes: {
        // 手风琴展开动画
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        // 手风琴收起动画
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        // 淡入动画
        "fade-in": {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        // 滑入动画
        "slide-in": {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(0)" },
        },
        // 温和脉冲动画
        "pulse-gentle": {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: "0.8" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out", // 手风琴展开
        "accordion-up": "accordion-up 0.2s ease-out", // 手风琴收起
        "fade-in": "fade-in 0.3s ease-out", // 淡入效果
        "slide-in": "slide-in 0.3s ease-out", // 滑入效果
        "pulse-gentle": "pulse-gentle 2s ease-in-out infinite", // 温和脉冲
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'], // 无衬线字体
        mono: ['JetBrains Mono', 'Consolas', 'monospace'], // 等宽字体
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none', // 不限制最大宽度
            color: 'hsl(var(--foreground))', // 基础文本颜色
            '--tw-prose-body': 'hsl(var(--foreground))', // 正文颜色
            '--tw-prose-headings': 'hsl(var(--foreground))', // 标题颜色
            '--tw-prose-lead': 'hsl(var(--muted-foreground))', // 引言颜色
            '--tw-prose-links': 'hsl(var(--primary))', // 链接颜色
            '--tw-prose-bold': 'hsl(var(--foreground))', // 粗体颜色
            '--tw-prose-counters': 'hsl(var(--muted-foreground))', // 计数器颜色
            '--tw-prose-bullets': 'hsl(var(--muted-foreground))', // 项目符号颜色
            '--tw-prose-hr': 'hsl(var(--border))', // 分割线颜色
            '--tw-prose-quotes': 'hsl(var(--foreground))', // 引用颜色
            '--tw-prose-quote-borders': 'hsl(var(--border))', // 引用边框颜色
            '--tw-prose-captions': 'hsl(var(--muted-foreground))', // 标题颜色
            '--tw-prose-code': 'hsl(var(--foreground))', // 行内代码颜色
            '--tw-prose-pre-code': 'hsl(var(--muted-foreground))', // 代码块代码颜色
            '--tw-prose-pre-bg': 'hsl(var(--muted))', // 代码块背景颜色
            '--tw-prose-th-borders': 'hsl(var(--border))', // 表格头边框颜色
            '--tw-prose-td-borders': 'hsl(var(--border))', // 表格单元格边框颜色
          },
        },
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"), // 排版插件
  ],
}
