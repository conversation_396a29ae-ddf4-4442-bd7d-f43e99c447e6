import { PageLayout } from '@/components/layout/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Brain, Heart, Cpu, Zap, Factory, Scale, Sparkles, MessageSquare, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import type { Metadata } from 'next'

// Examples页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Real Examples of AI Simplification - HumanWhisper',
  description: 'Explore how HumanWhisper simplifies real-world topics from science to finance. See AI in action for clear, accessible understanding.',
  keywords: [
    'AI examples',
    'simplification demos',
    'real AI cases',
    'how AI simplifies',
    'HumanWhisper examples'
  ],
  openGraph: {
    title: 'Real Examples of AI Simplification - HumanWhisper',
    description: 'Explore how HumanWhisper simplifies real-world topics from science to finance. See AI in action for clear, accessible understanding.',
    url: '/examples',
    type: 'website',
  },
  twitter: {
    title: 'Real Examples of AI Simplification - HumanWhisper',
    description: 'Explore how Human<PERSON><PERSON><PERSON> simplifies real-world topics from science to finance. See AI in action for clear, accessible understanding.',
  },
  alternates: {
    canonical: '/examples',
  },
}

// 金融经济示例
const financeExamples = [
  {
    id: 'compound-interest',
    question: "What is Compound Interest and How Does It Work?",
    answer: "Think of compound interest as a snowball rolling downhill - it gets bigger as it goes! When you save money, you earn interest. But here's the magic: next year, you earn interest on your original money PLUS the interest from last year. It's like your money is making babies, and those babies start making babies too! Key point: Time is your best friend - the earlier you start, the bigger your snowball gets.",
    tip: "💡 Even $100 saved at age 20 can become $1,600 by retirement!"
  },
  {
    id: 'stock-market',
    question: "How Does the Stock Market Actually Function?",
    answer: "Imagine the stock market as a giant farmers market, but instead of buying apples, people buy tiny pieces of companies (stocks). When a company does well, more people want to buy their 'piece,' so the price goes up. When it struggles, fewer people want it, so the price drops. Key insight: You're not gambling - you're becoming a tiny owner of real businesses that make real products and services.",
    tip: "📈 The market goes up and down daily, but historically trends upward over decades."
  }
]

// 医疗健康示例
const healthcareExamples = [
  {
    id: 'vaccines',
    question: "How Do Vaccines Train Your Immune System?",
    answer: "Your immune system is like a security team protecting your body. Vaccines are like showing this team a 'wanted poster' of a criminal (virus) before they actually show up. The security team studies the poster, learns to recognize the bad guy, and prepares their response. When the real criminal arrives, they're ready to catch them immediately! The brilliant part: Your security team remembers this training for years, sometimes for life.",
    tip: "🛡️ It's like a fire drill for your immune system - practice makes perfect!"
  },
  {
    id: 'heart-attack',
    question: "What Happens During a Heart Attack?",
    answer: "Think of your heart as a hardworking muscle that needs its own blood supply to keep pumping. A heart attack happens when one of the 'delivery trucks' (coronary arteries) bringing oxygen-rich blood to your heart muscle gets blocked - usually by a blood clot. Without oxygen, that part of the heart muscle starts to 'suffocate' and can die. Time is critical: The faster the blockage is cleared, the more heart muscle can be saved.",
    tip: "⚡ Call 911 immediately - every minute counts in saving heart muscle!"
  }
]

// 科技计算示例
const technologyExamples = [
  {
    id: 'blockchain',
    question: "What Is Blockchain and How Does It Secure Transactions?",
    answer: "Imagine a notebook that everyone in town has an identical copy of. When someone wants to send money, they announce it to everyone. Each person writes it in their notebook, but here's the trick: they can only add new pages if everyone agrees the math checks out. Once written, you can't erase or change anything without everyone noticing. The security: To cheat, you'd need to somehow change everyone's notebook at the same time - nearly impossible!",
    tip: "🔗 Each 'page' is linked to the previous one, creating an unbreakable chain."
  },
  {
    id: 'quantum-computing',
    question: "How Does Quantum Computing Differ from Classical Computing?",
    answer: "Regular computers are like reading one book at a time - they process information step by step, using bits that are either 0 or 1. Quantum computers are like reading all books in a library simultaneously! They use 'quantum bits' that can be 0, 1, or both at the same time. This lets them explore many possible solutions at once. The catch: They're extremely delicate and only work for specific types of problems.",
    tip: "🌟 Think parallel universe computing - exploring all possibilities at once!"
  }
]

export default function ExamplesPage() {

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Complex Industry Problems
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Explained Simply
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              See how HumanWhisper transforms complex industry knowledge into 
              <strong> clear, accessible explanations</strong> that anyone can understand. 
              From finance to technology, we make the complicated simple.
            </p>
          </div>
        </div>
      </section>

      {/* Finance & Economics */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Brain className="h-8 w-8 text-green-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Finance & Economics Made Easy</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Money matters don't have to be mysterious. See how we break down complex financial concepts 
              into everyday language that makes sense.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {financeExamples.map((example) => (
              <Card key={example.id} className="h-full">
                <CardHeader>
                  <CardTitle className="text-lg text-primary">{example.question}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed">{example.answer}</p>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <p className="text-sm font-medium">{example.tip}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Healthcare & Medicine */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Heart className="h-8 w-8 text-red-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Healthcare & Medicine Simplified</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Your health is important, and understanding it shouldn't require a medical degree. 
              Here's how we explain medical concepts in human terms.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {healthcareExamples.map((example) => (
              <Card key={example.id} className="h-full">
                <CardHeader>
                  <CardTitle className="text-lg text-primary">{example.question}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed">{example.answer}</p>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <p className="text-sm font-medium">{example.tip}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technology & Computing */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Cpu className="h-8 w-8 text-blue-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Technology & Computing Demystified</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Technology shapes our world, but it doesn't have to be intimidating. 
              We translate tech jargon into concepts you can actually grasp.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {technologyExamples.map((example) => (
              <Card key={example.id} className="h-full">
                <CardHeader>
                  <CardTitle className="text-lg text-primary">{example.question}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground leading-relaxed">{example.answer}</p>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <p className="text-sm font-medium">{example.tip}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Energy & Environment */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Zap className="h-8 w-8 text-yellow-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Energy & Environment Explained</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Climate change and energy solutions are complex topics that affect us all.
              Here's how we make environmental science accessible.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">How Do Solar Panels Generate Electricity?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Solar panels work like tiny energy converters made of special materials called photovoltaic cells.
                  When sunlight hits these cells, it knocks electrons loose, creating an electric current - like water flowing
                  through a pipe. The amazing part: This happens silently with no moving parts! The electricity flows
                  through wires to power your home or gets stored in batteries for later use.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">☀️ Even on cloudy days, panels can generate 10-25% of their peak power!</p>
                </div>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">What Is Carbon Capture Technology?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Think of carbon capture as a giant vacuum cleaner for CO2. These systems suck carbon dioxide
                  out of the air (or from factory smokestacks) and either store it underground in rock formations
                  or turn it into useful products like concrete or fuel. The goal: Remove the excess CO2 that's
                  causing climate change, like cleaning up a spill before it spreads further.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">🌍 One large facility can capture the CO2 equivalent of 40 million trees!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Manufacturing & Supply Chain */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Factory className="h-8 w-8 text-purple-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Manufacturing & Supply Chain Insights</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Ever wonder how products get from idea to your doorstep? We break down the complex world
              of manufacturing and global logistics.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">What Is Supply Chain Management?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Supply chain management is like conducting a massive orchestra where every musician
                  (supplier, manufacturer, shipper) must play their part perfectly in sync. It coordinates
                  raw materials, production, warehousing, and delivery to get products to customers efficiently.
                  The challenge: One missed note (delayed shipment, factory shutdown) can throw off the entire performance.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">📦 Your smartphone traveled through 6+ countries before reaching you!</p>
                </div>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">What Is Just-In-Time Manufacturing?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Just-In-Time (JIT) manufacturing is like cooking a meal exactly when guests arrive - no earlier,
                  no later. Instead of storing huge inventories, companies produce items only when needed, in the exact
                  quantities required. The benefits: Less waste, lower costs, fresher products. The risk:
                  If anything goes wrong (like a pandemic), the whole system can break down quickly.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">⏰ Toyota pioneered this method - parts arrive hours before assembly!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Legal & Regulatory */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Scale className="h-8 w-8 text-indigo-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Legal & Regulatory Simplifications</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Legal language doesn't have to be intimidating. We translate complex laws and regulations
              into plain English you can actually understand.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">What Are Intellectual Property Rights?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Intellectual property rights are like invisible fences around ideas. Just as you own your house
                  and can decide who enters, you can own ideas, inventions, creative works, and brand names.
                  Types include: Patents (inventions), copyrights (creative works), trademarks (brand names),
                  and trade secrets (secret recipes). These rights let creators profit from their ideas and prevent others from stealing them.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">💡 Even this explanation could be copyrighted as creative writing!</p>
                </div>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">How Does GDPR Protect Your Data?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  GDPR (General Data Protection Regulation) is like a strict bouncer for your personal information.
                  It forces companies to ask permission before collecting your data, explain what they'll do with it,
                  and let you see, correct, or delete it anytime. Your rights: Know what data is collected,
                  say no to collection, get copies of your data, and have it deleted when you want.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">🛡️ Companies can be fined up to 4% of global revenue for violations!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Fun & Unusual Questions */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Sparkles className="h-8 w-8 text-pink-600" />
              <h2 className="text-3xl md:text-4xl font-bold">Bonus: Fun & Unusual Industry Questions</h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Sometimes the most interesting questions come from unexpected places.
              Here are some fun industry mysteries solved simply.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">Why Are Diamonds So Expensive?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Diamonds are expensive because of brilliant marketing, not just rarity. While they're hard to find,
                  the De Beers company controlled supply for decades, creating artificial scarcity. They also convinced
                  everyone that "diamonds are forever" and essential for engagement rings. The reality: Diamonds aren't
                  actually that rare, and lab-created ones are chemically identical but cost 60-80% less!
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">💎 The "2 months salary" rule was invented by De Beers' ad campaign!</p>
                </div>
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg text-primary">How Does 3D Printing Work?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  3D printing is like building with incredibly thin pancakes. The printer melts plastic (or other materials)
                  and squeezes it out through a tiny nozzle, layer by layer, following a digital blueprint. Each layer is
                  paper-thin, but when you stack hundreds or thousands of them, you get a solid 3D object. The magic:
                  You can print moving parts, complex internal structures, and custom shapes impossible to make any other way.
                </p>
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-medium">🖨️ NASA uses 3D printers on the International Space Station!</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Ask Your Own Question */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-6">
              <MessageSquare className="h-8 w-8" />
              <h2 className="text-3xl md:text-4xl font-bold">Ask Your Own Question</h2>
            </div>
            <p className="text-xl mb-8 opacity-90">
              Have a complex topic you'd like simplified? Try HumanWhisper's AI and get clear,
              jargon-free explanations tailored to your understanding level.
            </p>
            <Link
              href="/chat"
              className="bg-white text-primary px-8 py-4 rounded-lg text-lg font-medium hover:bg-white/90 transition-colors inline-flex items-center gap-2 group"
            >
              Start Asking Questions
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            <p className="text-sm opacity-75 mt-6">
              ✓ Free trial available ✓ Instant explanations ✓ Multiple AI models available
            </p>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
