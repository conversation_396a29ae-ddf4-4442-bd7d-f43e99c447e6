import type { ChatSettings, Message, AIModel } from '@/types'
// import { buildContextAwarePrompt, analyzeQuestionType } from './prompts/prompt-builder'
import { estimateTokenCount } from './ai-utils'

/**
 * 提示词优化器
 * 根据模型特性、用户反馈和性能数据优化提示词
 */

// 模型特定的优化策略
export const MODEL_OPTIMIZATIONS: Record<AIModel, {
  maxPromptTokens: number
  preferredStyle: string
  strengths: string[]
  optimizationTips: string[]
}> = {
  'gpt-4.1-nano': {
    maxPromptTokens: 2000,
    preferredStyle: 'concise_structured',
    strengths: ['speed', 'efficiency', 'clear_instructions'],
    optimizationTips: [
      '保持提示词简洁明了',
      '使用清晰的结构化指令',
      '避免过度复杂的嵌套逻辑',
      '优先使用直接的指令而非暗示'
    ]
  },
  'claude-3-5-haiku-latest': {
    maxPromptTokens: 4000,
    preferredStyle: 'detailed_contextual',
    strengths: ['reasoning', 'context_understanding', 'nuanced_responses'],
    optimizationTips: [
      '可以提供更详细的背景信息',
      '利用其强大的推理能力',
      '包含更多上下文和细节',
      '使用复杂的逻辑结构'
    ]
  },
  'gemini-2.0-flash': {
    maxPromptTokens: 3000,
    preferredStyle: 'creative_adaptive',
    strengths: ['creativity', 'adaptability', 'multimodal_understanding'],
    optimizationTips: [
      '鼓励创意和多样化的回答',
      '利用其适应性强的特点',
      '可以包含更多创意元素',
      '支持更灵活的指令格式'
    ]
  }
}

// 优化历史记录
// interface OptimizationHistory {
//   promptVersion: string
//   performance: {
//     responseQuality: number // 1-10
//     responseTime: number // 毫秒
//     userSatisfaction: number // 1-10
//     tokenEfficiency: number // 1-10
//   }
//   feedback: string[]
//   timestamp: Date
// }

/**
 * 根据模型特性优化提示词
 * @param basePrompt - 基础提示词
 * @param model - 目标AI模型
 * @param settings - 聊天设置
 * @returns string
 */
export function optimizePromptForModel(
  basePrompt: string,
  model: AIModel,
  settings: ChatSettings
): string {
  const optimization = MODEL_OPTIMIZATIONS[model]
  if (!optimization) {
    return basePrompt
  }
  
  let optimizedPrompt = basePrompt
  
  // 根据模型的token限制调整
  const currentTokens = estimateTokenCount(basePrompt)
  if (currentTokens > optimization.maxPromptTokens) {
    optimizedPrompt = truncatePromptIntelligently(basePrompt, optimization.maxPromptTokens)
  }
  
  // 根据模型特性添加优化指令
  const modelSpecificInstructions = buildModelSpecificInstructions(model, settings)
  if (modelSpecificInstructions) {
    optimizedPrompt += '\n\n' + modelSpecificInstructions
  }
  
  return optimizedPrompt
}

/**
 * 智能截断提示词
 * @param prompt - 原始提示词
 * @param maxTokens - 最大token数
 * @returns string
 */
function truncatePromptIntelligently(prompt: string, maxTokens: number): string {
  const sections = prompt.split('\n\n')
  const priorities = [
    'Core Principles',
    'Communication Style',
    'Response Structure',
    'Quality Checks',
    'Language Adaptation',
    'Example Requirements',
    'Analogy Requirements'
  ]
  
  let result = ''
  let currentTokens = 0
  
  // 按优先级保留sections
  for (const priority of priorities) {
    const section = sections.find(s => s.includes(priority))
    if (section) {
      const sectionTokens = estimateTokenCount(section)
      if (currentTokens + sectionTokens <= maxTokens) {
        result += (result ? '\n\n' : '') + section
        currentTokens += sectionTokens
      }
    }
  }
  
  // 如果还有空间，添加其他重要sections
  for (const section of sections) {
    if (!result.includes(section)) {
      const sectionTokens = estimateTokenCount(section)
      if (currentTokens + sectionTokens <= maxTokens) {
        result += '\n\n' + section
        currentTokens += sectionTokens
      }
    }
  }
  
  return result
}

/**
 * 构建模型特定的指令
 * @param model - AI模型
 * @param settings - 聊天设置
 * @returns string
 */
function buildModelSpecificInstructions(model: AIModel, _settings: ChatSettings): string {
  // const optimization = MODEL_OPTIMIZATIONS[model]
  
  let instructions = `### ${model} 特定优化:\n`
  
  switch (model) {
    case 'gpt-4.1-nano':
      instructions += `
- 保持回答简洁高效，避免冗余
- 使用清晰的段落结构
- 优先提供最核心的信息
- 如果需要详细解释，使用编号列表`
      break
      
    case 'claude-3-5-haiku-latest':
      instructions += `
- 可以提供更深入的分析和推理
- 利用强大的上下文理解能力
- 包含更多细节和背景信息
- 展示思考过程和逻辑链条`
      break
      
    case 'gemini-2.0-flash':
      instructions += `
- 鼓励创意和多角度思考
- 可以使用更灵活的表达方式
- 适应用户的具体需求和偏好
- 展示创新的解释方法`
      break
  }
  
  return instructions
}

/**
 * 基于用户反馈优化提示词
 * @param currentPrompt - 当前提示词
 * @param feedback - 用户反馈
 * @param conversationHistory - 对话历史
 * @returns string
 */
export function optimizeBasedOnFeedback(
  currentPrompt: string,
  feedback: {
    rating: number // 1-10
    comments: string[]
    issues: string[] // 'too_long', 'too_technical', 'not_clear', etc.
  },
  _conversationHistory: Message[]
): string {
  let optimizedPrompt = currentPrompt
  
  // 根据具体问题调整
  for (const issue of feedback.issues) {
    switch (issue) {
      case 'too_long':
        optimizedPrompt += '\n\n### 长度控制:\n- 保持回答简洁，避免不必要的重复\n- 优先提供最重要的信息'
        break
        
      case 'too_technical':
        optimizedPrompt += '\n\n### 简化要求:\n- 进一步简化技术术语\n- 使用更多日常生活的例子\n- 避免专业词汇'
        break
        
      case 'not_clear':
        optimizedPrompt += '\n\n### 清晰度提升:\n- 使用更简单的句子结构\n- 提供更多具体例子\n- 确保逻辑连贯性'
        break
        
      case 'needs_examples':
        optimizedPrompt += '\n\n### 例子要求:\n- 必须包含至少2个具体例子\n- 例子要贴近用户的生活经验\n- 使用对比来突出要点'
        break
        
      case 'too_formal':
        optimizedPrompt += '\n\n### 语调调整:\n- 使用更轻松、友好的语调\n- 增加互动性问题\n- 使用更多口语化表达'
        break
    }
  }
  
  // 根据评分调整整体策略
  if (feedback.rating <= 5) {
    optimizedPrompt += '\n\n### 质量提升:\n- 特别注意用户的理解水平\n- 确保每个概念都有清晰的解释\n- 主动询问是否需要进一步澄清'
  }
  
  return optimizedPrompt
}

/**
 * A/B测试提示词变体
 * @param basePrompt - 基础提示词
 * @param variations - 变体配置
 * @returns string[]
 */
export function generatePromptVariations(
  basePrompt: string,
  variations: {
    tone: 'formal' | 'casual' | 'enthusiastic'
    structure: 'linear' | 'modular' | 'conversational'
    examples: 'minimal' | 'moderate' | 'extensive'
  }
): string[] {
  // const variants: string[] = []
  
  // 语调变体
  const toneModifications = {
    formal: '\n\n### 语调调整: 使用更正式、专业的表达方式',
    casual: '\n\n### 语调调整: 使用轻松、随意的对话风格',
    enthusiastic: '\n\n### 语调调整: 展现热情和兴奋，使用感叹号和积极词汇'
  }
  
  // 结构变体
  const structureModifications = {
    linear: '\n\n### 结构要求: 按逻辑顺序线性展开，一步步建立理解',
    modular: '\n\n### 结构要求: 使用模块化结构，每个概念独立解释',
    conversational: '\n\n### 结构要求: 使用对话式结构，像聊天一样自然展开'
  }
  
  // 例子变体
  const exampleModifications = {
    minimal: '\n\n### 例子要求: 只在必要时使用例子，保持简洁',
    moderate: '\n\n### 例子要求: 适度使用例子来说明要点',
    extensive: '\n\n### 例子要求: 大量使用例子、类比和具体场景'
  }
  
  // 生成组合变体
  const toneVariant = basePrompt + toneModifications[variations.tone]
  const structureVariant = basePrompt + structureModifications[variations.structure]
  const exampleVariant = basePrompt + exampleModifications[variations.examples]
  
  // 完整组合变体
  const fullVariant = basePrompt + 
    toneModifications[variations.tone] +
    structureModifications[variations.structure] +
    exampleModifications[variations.examples]
  
  return [basePrompt, toneVariant, structureVariant, exampleVariant, fullVariant]
}

/**
 * 评估提示词性能
 * @param prompt - 提示词
 * @param responses - AI回复样本
 * @param userFeedback - 用户反馈
 * @returns object
 */
export function evaluatePromptPerformance(
  _prompt: string,
  responses: string[],
  userFeedback: number[]
) {
  const metrics = {
    averageLength: responses.reduce((sum, r) => sum + r.length, 0) / responses.length,
    averageRating: userFeedback.reduce((sum, r) => sum + r, 0) / userFeedback.length,
    consistency: calculateConsistency(responses),
    clarity: calculateClarity(responses),
    engagement: calculateEngagement(responses)
  }
  
  const score = (
    (metrics.averageRating / 10) * 0.4 +
    (metrics.consistency / 10) * 0.2 +
    (metrics.clarity / 10) * 0.2 +
    (metrics.engagement / 10) * 0.2
  ) * 100
  
  return {
    score: Math.round(score),
    metrics,
    recommendations: generateOptimizationRecommendations(metrics)
  }
}

/**
 * 计算回复一致性
 * @param responses - 回复列表
 * @returns number
 */
function calculateConsistency(responses: string[]): number {
  // 简化的一致性计算 - 实际实现会更复杂
  const avgLength = responses.reduce((sum, r) => sum + r.length, 0) / responses.length
  const lengthVariance = responses.reduce((sum, r) => sum + Math.pow(r.length - avgLength, 2), 0) / responses.length
  const consistencyScore = Math.max(0, 10 - (lengthVariance / 1000))
  
  return Math.min(10, consistencyScore)
}

/**
 * 计算回复清晰度
 * @param responses - 回复列表
 * @returns number
 */
function calculateClarity(responses: string[]): number {
  // 简化的清晰度计算
  let clarityScore = 0
  
  for (const response of responses) {
    let score = 5 // 基础分
    
    // 检查结构化元素
    if (response.includes('\n\n')) score += 1 // 有段落分隔
    if (/\d+\./.test(response)) score += 1 // 有编号列表
    if (response.includes('例如') || response.includes('比如')) score += 1 // 有例子
    if (response.length > 100 && response.length < 1000) score += 1 // 长度适中
    
    clarityScore += score
  }
  
  return Math.min(10, clarityScore / responses.length)
}

/**
 * 计算回复吸引力
 * @param responses - 回复列表
 * @returns number
 */
function calculateEngagement(responses: string[]): number {
  // 简化的吸引力计算
  let engagementScore = 0
  
  for (const response of responses) {
    let score = 5 // 基础分
    
    // 检查吸引力元素
    if (/[!?]/.test(response)) score += 1 // 有感叹号或问号
    if (/想象|思考|考虑/.test(response)) score += 1 // 有引导思考的词汇
    if (/就像|好比|类似/.test(response)) score += 1 // 有类比
    if (response.includes('你') || response.includes('我们')) score += 1 // 有人称代词
    
    engagementScore += score
  }
  
  return Math.min(10, engagementScore / responses.length)
}

/**
 * 生成优化建议
 * @param metrics - 性能指标
 * @returns string[]
 */
function generateOptimizationRecommendations(metrics: any): string[] {
  const recommendations: string[] = []
  
  if (metrics.consistency < 7) {
    recommendations.push('提高回复一致性：使用更明确的结构指导')
  }
  
  if (metrics.clarity < 7) {
    recommendations.push('提升清晰度：增加更多结构化元素和例子')
  }
  
  if (metrics.engagement < 7) {
    recommendations.push('增强吸引力：使用更多互动元素和个人化语言')
  }
  
  if (metrics.averageLength > 1500) {
    recommendations.push('控制长度：指导AI提供更简洁的回答')
  }
  
  if (metrics.averageLength < 200) {
    recommendations.push('增加深度：鼓励提供更详细的解释')
  }
  
  return recommendations
}
