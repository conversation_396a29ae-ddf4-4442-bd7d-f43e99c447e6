import { PageLayout } from '@/components/layout/PageLayout'
import { LimerickGeneratorClient } from './LimerickGeneratorClient'
import type { Metadata } from 'next'

// 404 Limerick Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI 404 Page Limerick Generator - Creative Error Pages | HumanWhisper',
  description: 'Generate creative 404 error page limericks for your brand. Transform boring error pages into engaging brand experiences with humor and style.',
  keywords: [
    '404 page generator',
    'error page design',
    'limerick generator',
    'creative 404',
    'brand humor',
    'web design',
    'user experience'
  ],
  openGraph: {
    title: 'AI 404 Page Limerick Generator - Creative Error Pages | HumanWhisper',
    description: 'Generate creative 404 error page limericks for your brand. Transform boring error pages into engaging brand experiences with humor and style.',
    url: '/404-limerick',
    type: 'website',
  },
  twitter: {
    title: 'AI 404 Page Limerick Generator - Creative Error Pages | HumanWhisper',
    description: 'Generate creative 404 error page limericks for your brand. Transform boring error pages into engaging brand experiences with humor and style.',
  },
  alternates: {
    canonical: '/404-limerick',
  },
}

export default function LimerickGeneratorPage() {
  return (
    <PageLayout>
      <LimerickGeneratorClient />
    </PageLayout>
  )
}
