import { NextRequest, NextResponse } from 'next/server'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// Logo生成API路由
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      brandName,
      industry,
      logoStyle,
      logoType,
      coreMessage,
      colorPreference,
      imageSize,
      numImages,
      seed,
      inferenceSteps,
      guidanceScale,
      negativePrompt
    } = body

    // 输入验证
    if (!brandName || typeof brandName !== 'string') {
      return NextResponse.json(
        { error: 'Brand name is required' },
        { status: 400 }
      )
    }

    if (!industry || !logoStyle) {
      return NextResponse.json(
        { error: 'Industry and logo style are required' },
        { status: 400 }
      )
    }

    // 构建Logo生成提示词
    const prompt = buildLogoPrompt({
      brandName,
      industry,
      logoStyle,
      logoType,
      coreMessage,
      colorPreference
    })

    // 构建负面提示词
    const fullNegativePrompt = buildNegativePrompt(negativePrompt)

    // 获取随机的 API key 和配置
    const apiKey = getRandomSiliconFlowApiKey()
    const baseUrl = getSiliconFlowBaseUrl()

    // 调用图像生成API
    const response = await fetch(`${baseUrl}/images/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'Kwai-Kolors/Kolors',
        prompt: prompt,
        negative_prompt: fullNegativePrompt,
        image_size: imageSize || '1024x1024',
        batch_size: Math.min(numImages || 1, 4), // 限制最多4张
        num_inference_steps: inferenceSteps || 25,
        guidance_scale: guidanceScale || 7.5,
        ...(seed && { seed: parseInt(seed) })
      }),
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`)
    }

    const data = await response.json()

    return NextResponse.json({
      images: data.images,
      metadata: {
        prompt,
        negativePrompt: fullNegativePrompt,
        brandName,
        industry,
        logoStyle,
        logoType,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    let errorMessage = 'Failed to generate logo. Please try again.'
    let statusCode = 500

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'API configuration error'
        statusCode = 500
      } else if (error.message.includes('rate limit')) {
        errorMessage = 'Too many requests. Please try again later.'
        statusCode = 429
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout. Please try again.'
        statusCode = 504
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}

// 构建Logo生成提示词
function buildLogoPrompt({
  brandName,
  industry,
  logoStyle,
  logoType,
  coreMessage,
  colorPreference
}: {
  brandName: string
  industry: string
  logoStyle: string
  logoType: string
  coreMessage?: string
  colorPreference?: string
}) {
  // 行业映射
  const industryMap: Record<string, string> = {
    'technology': 'technology and software',
    'business': 'business and consulting',
    'creative': 'creative and design',
    'health': 'healthcare and wellness',
    'education': 'education and learning',
    'food': 'food and beverage',
    'fashion': 'fashion and lifestyle',
    'real-estate': 'real estate and property'
  }

  // 风格映射
  const styleMap: Record<string, string> = {
    'modern': 'modern minimalist, clean and contemporary',
    'classic': 'classic business, professional and traditional',
    'creative': 'creative artistic, unique and expressive',
    'friendly': 'friendly approachable, warm and welcoming',
    'luxury': 'luxury premium, elegant and sophisticated',
    'tech': 'tech futuristic, digital and innovative'
  }

  // Logo类型映射
  const typeMap: Record<string, string> = {
    'combination': 'combination mark with both text and icon',
    'wordmark': 'wordmark with typography focus',
    'pictorial': 'pictorial mark with symbolic icon',
    'abstract': 'abstract mark with geometric shapes'
  }

  const industryDesc = industryMap[industry] || industry
  const styleDesc = styleMap[logoStyle] || logoStyle
  const typeDesc = typeMap[logoType] || 'combination mark'

  let prompt = `Create a professional ${typeDesc} logo for "${brandName}", a company in the ${industryDesc} industry. `
  prompt += `Style: ${styleDesc}. `

  if (coreMessage) {
    prompt += `The logo should convey: ${coreMessage}. `
  }

  if (colorPreference) {
    prompt += `Color scheme: ${colorPreference}. `
  }

  // 添加通用质量要求
  prompt += `The logo should be clean, scalable, memorable, and professional. `
  prompt += `High quality design suitable for business use, clear and legible, modern aesthetic.`

  return prompt
}

// 构建负面提示词
function buildNegativePrompt(userNegativePrompt?: string) {
  const baseNegative = [
    'blurry',
    'low quality',
    'pixelated',
    'distorted',
    'ugly',
    'amateur',
    'cluttered',
    'messy',
    'unreadable text',
    'poor typography',
    'bad composition',
    'watermark',
    'signature'
  ]

  if (userNegativePrompt) {
    return `${baseNegative.join(', ')}, ${userNegativePrompt}`
  }

  return baseNegative.join(', ')
}

/**
 * OPTIONS请求处理（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}
