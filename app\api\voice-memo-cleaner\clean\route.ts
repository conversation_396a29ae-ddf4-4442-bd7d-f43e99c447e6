import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 场景映射
const SCENARIO_MAP: Record<string, string> = {
  'meeting': 'business meeting or professional discussion',
  'brainstorm': 'brainstorming session or creative ideation',
  'lecture': 'educational lecture or classroom content',
  'podcast': 'podcast recording or audio content',
  'interview': 'interview or Q&A session',
  'voice_memo': 'personal voice memo or note',
  'presentation': 'presentation or speech practice'
}

// 英文口头禅清理词库
const FILLER_WORDS = {
  // 犹豫音 - 严格删除
  hesitation: ['uh', 'um', 'er', 'hmm', 'ah', 'eh'],

  // 口头填充词 - 严格删除
  fillers: ['like', 'you know', 'kind of', 'sort of', 'i mean', 'you see'],

  // 开场口水词 - 条件删除
  starters: ['so', 'well', 'yeah', 'actually', 'basically', 'literally', 'right'],

  // 尾部填充 - 严格删除
  endings: ['or whatever', 'or something', 'and stuff', 'you know what i mean'],

  // 语气词 - 条件保留
  qualifiers: ['i guess', 'maybe', 'i think', 'i feel like', 'honestly', 'really', 'just', 'totally'],

  // 重复词检测
  repetitions: ['the the', 'and and', 'to to', 'is is', 'was was', 'will will']
}

// 清理级别映射
const CLEANING_LEVEL_MAP: Record<string, string> = {
  'light': 'Remove basic filler words (um, uh, like, you know) while preserving natural speech patterns and personal tone',
  'moderate': 'Remove filler words, fix repetitions, and correct obvious grammatical errors while maintaining conversational tone',
  'thorough': 'Complete cleanup removing all filler words, fixing grammar, improving sentence structure, and enhancing clarity',
  'professional': 'Business-ready transcript with comprehensive filler word removal, professional language, proper formatting, and polished presentation'
}

// 构建文本清理prompt
function buildCleaningPrompt(
  text: string,
  scenario: string,
  cleaningLevel: string
): string {
  const scenarioDesc = SCENARIO_MAP[scenario] || scenario
  const cleaningDesc = CLEANING_LEVEL_MAP[cleaningLevel] || cleaningLevel

  // 根据清理级别生成具体的口头禅清理指令
  const getFillerWordInstructions = (level: string): string => {
    const { hesitation, fillers, starters, endings, qualifiers, repetitions } = FILLER_WORDS

    switch (level) {
      case 'light':
        return `- Remove basic hesitation sounds: ${hesitation.join(', ')}
- Remove common filler words: ${fillers.slice(0, 4).join(', ')}
- Keep natural speech patterns and personal tone`

      case 'moderate':
        return `- Remove all hesitation sounds: ${hesitation.join(', ')}
- Remove filler words: ${fillers.join(', ')}
- Remove repetitive endings: ${endings.join(', ')}
- Fix word repetitions: ${repetitions.join(', ')}
- Preserve conversational tone`

      case 'thorough':
        return `- Remove all hesitation sounds: ${hesitation.join(', ')}
- Remove all filler words: ${fillers.join(', ')}
- Remove unnecessary starters: ${starters.join(', ')}
- Remove repetitive endings: ${endings.join(', ')}
- Reduce excessive qualifiers: ${qualifiers.join(', ')}
- Fix all word repetitions and false starts`

      case 'professional':
        return `- Remove all hesitation sounds and filler words completely
- Remove all unnecessary starters and endings
- Replace informal qualifiers with professional language
- Fix all repetitions, false starts, and grammatical errors
- Use business-appropriate vocabulary and sentence structure`

      default:
        return cleaningDesc
    }
  }

  const fillerInstructions = getFillerWordInstructions(cleaningLevel)

  const prompt = `You are a professional transcript editor specializing in English speech cleanup. Your task is to clean and improve the provided transcript by removing filler words and improving clarity.

**Context:**
- Content type: ${scenarioDesc}
- Cleaning level: ${cleaningLevel}
- Output language: English

**Original transcript:**
"""
${text}
"""

**Specific Cleaning Instructions:**
${fillerInstructions}

**General Guidelines:**
1. Maintain the original meaning and intent
2. Preserve important context and details
3. Use clear, professional English appropriate for the scenario
4. Format the text with proper paragraphs and punctuation
5. Ensure smooth flow and readability
6. Do not add new information or change the speaker's intended message

**Examples of cleaning:**
- "So, um, I was like thinking, you know, maybe we could kind of just wait a bit, or whatever..."
  → "I was thinking we could wait a bit."
- "Well, uh, the meeting went, like, really well, I guess."
  → "The meeting went really well."

**Output format:**
Provide only the cleaned transcript without any additional commentary, explanations, or meta-text.

Clean the transcript now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      text,
      scenario,
      cleaningLevel,
      model = 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B'
    } = body

    // 输入验证
    if (!text?.trim() || !scenario || !cleaningLevel) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 检查文本长度
    if (text.length > 10000) {
      return NextResponse.json(
        { error: 'Text is too long for processing' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildCleaningPrompt(
      text.trim(),
      scenario,
      cleaningLevel
    )

    // 模型参数配置
    const modelParams = {
      temperature: 0.6,
      max_tokens: 8192,
      top_p: 0.95,
      frequency_penalty: 0.0
    }

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...modelParams
    })

    // 提取清理后的文本
    const cleanedText = response.choices[0]?.message?.content?.trim()

    if (!cleanedText) {
      throw new Error('No cleaned text generated')
    }

    return NextResponse.json({
      cleanedText,
      metadata: {
        scenario,
        cleaningLevel,
        originalLength: text.length,
        cleanedLength: cleanedText.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Text processing model not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Text is too long for processing' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to clean transcript' },
      { status: 500 }
    )
  }
}
