# HumanWhisper 环境变量配置

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuaHVtYW53aGlzcGVyLmNvbSQ
CLERK_SECRET_KEY=**************************************************

# Creem.io Payment
CREEM_API_KEY=creem_1WPLUKkrv3pT5KmWl45gc6
CREEM_PRODUCT_ID=prod_rko8lucOO8qUTbKznH7i9
NEXT_PUBLIC_CREEM_TEST_MODE=false

# Creem Webhook密钥
CREEM_WEBHOOK_SECRET=whsec_KwRvP4475iHeCKBqvhlvm

# AIHubMix API 配置
AIHUBMIX_API_KEY=sk-BLNEevqwXmBZ6A6R4a7bC745D89d45758f354fA3683c69Ae
AIHUBMIX_BASE_URL=https://aihubmix.com/v1

# AI API 配置
AI_API_BASE_URL=https://api.siliconflow.cn/v1

# AI API Keys 池（多个密钥用于负载均衡和备用，共31个）
SILICONFLOW_API_KEYS=sk-qwykjbciiodizhzjaootmjbhvyldqkrbcgoqdumhjrfteefc,sk-tpnctfmbdnclbebyanqusjugmhyihchqgmtygwdnmdmxgfld,sk-wlyyuexignnhiacbdcfesjvgblpjvedqbfsvcbaemhrtkdmj,sk-jloddmynmrchnftxylnazlrlxqdvriqoyzncdffstxcbhzlu,sk-ujnolkugizqmbcqrmwqqqsgzxnuaskxqorrjuhbiboymkynr,sk-ylcztlmwntaywovwgnospfgmvrqixjdlqyppftxfydjxswqs,sk-bcoqfjawtdupexhgjavzsebfyratvowktudcztjhvonlvveg,sk-aljkzahnywxxvlbonjufyzltqfrqoxerqcvitlpykhadmmtg,sk-wybmkvrvehesxlgfuekmwkmsmpsrrlmchuavsncpgizcpmqw,sk-jqgnhxlnuxkgiukplyoxhwpftbczludutbitfcsuhhtbfzuz,sk-sobkbzoodnamfrnnwxwjqvvqawxejmoyrpvekykrktzbzhsl,sk-hkroqdonprzufhtdznylpifbojyivigplssrwszortmuljxx,sk-blxdnpuwsvnlixzugzhnxeamnzhhupenhnahblnzllqoattm,sk-gziotxmwvxlpgoyuwcdmowjoteasufchunkltmqykddrqjxg,sk-qfihoekseigzzpnltkqkcajqtimczoysuedenkerccejrwco,sk-qlwrpbtkefpegfjsgikaqihokmzuyjkgyfvcjxejaqlxjvmq,sk-unuttijidxfumzuzugzeczinadtovxbwbdgckztddavgfceg,sk-mdwtufwkrmgmbawneqyyjvlmuhjuozkhckxjzecjmkkrxxyk,sk-rufuhrculfhezukvzdfchuppjtvplfyuxqqtlhiogirdlpqk,sk-wqquoqfsttmhezdehjgvhoeznwqujcaeyooywrvztibhubdt,sk-fcipzvsaaadoylqwgzriqfqvboicrmvehsaqbogynzjiwwbi,sk-fehvttrlgxgxwomdpegycdldtqlumyxsawgvgblaquzhhrfz,sk-ajvfzehkfccwdubkbeabvdrsbbynewsahsgjagrekuurygpj,sk-fklepkmxcnauiifrsyljpeyyacpkfnvluhfsbtvjalpaqwcx,sk-okwdpuknhfmmwnfmzausjuunlxxqkywbwgzqzqvpyblfugjh,sk-qwivtxchrwmjtxvdvueyuwocenezvirniradbjszbubztigs,sk-tjiknzksydigzkumbnlxffkpcxvoaayjsjrhirnhkstjdodt,sk-rajzhpycftamypsugbwuutumdofqciyjoabhzrmxspmcbvio,sk-ratkbattgegoaysxiorpzobojvtcizqhovkzasjwyqbjpeyl,sk-flcgjvwqtrkernpjonehbmxpgnfdcbexgeuskxqealzxkuam,sk-cfihcvoitjleoqjfefrhzuolszccmervvuywwyvhkukirfls

# 支持的AI模型列表
NEXT_PUBLIC_SUPPORTED_MODELS=gpt-4.1-nano,claude-3-5-haiku-latest,gemini-2.0-flash

# AI模型列表（用于提示词优化工具）
NEXT_PUBLIC_AI_MODELS=Qwen/Qwen2-7B-Instruct,THUDM/glm-4-9b-chat

# 应用程序配置
NEXT_PUBLIC_APP_NAME=HumanWhisper
NEXT_PUBLIC_APP_URL=https://humanwhisper.com

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false
NEXT_PUBLIC_ENABLE_PROMPT_OPTIMIZER=true

# 速率限制（每分钟请求数）
RATE_LIMIT_RPM=60

# 默认设置
DEFAULT_MODEL=gpt-4.1-nano
DEFAULT_LANGUAGE=en
DEFAULT_RESPONSE_LENGTH=MEDIUM
DEFAULT_TARGET_AGE=12

# 开发环境设置
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true