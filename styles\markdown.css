/* 自定义Markdown样式 */

/* 代码高亮样式优化 */
.hljs {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: 0.5rem;
  padding: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
}

/* 关键字 */
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: hsl(var(--primary)) !important;
  font-weight: 600;
}

/* 字符串 */
.hljs-string {
  color: #22c55e !important;
}

/* 注释 */
.hljs-comment,
.hljs-quote {
  color: hsl(var(--muted-foreground)) !important;
  font-style: italic;
}

/* 数字 */
.hljs-number,
.hljs-regexp,
.hljs-literal {
  color: #f59e0b !important;
}

/* 函数名 */
.hljs-function .hljs-title,
.hljs-title.function_ {
  color: #3b82f6 !important;
  font-weight: 600;
}

/* 变量 */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute {
  color: #8b5cf6 !important;
}

/* 类型 */
.hljs-type,
.hljs-class .hljs-title {
  color: #06b6d4 !important;
  font-weight: 600;
}

/* 操作符 */
.hljs-operator,
.hljs-punctuation {
  color: hsl(var(--foreground)) !important;
}

/* 内联代码样式 */
code:not(pre code) {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* 列表样式优化 */
.prose ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.prose li::marker {
  color: hsl(var(--primary));
  font-weight: 600;
}

/* 表格样式优化 */
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
}

.prose th {
  background: hsl(var(--muted));
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1rem;
  border: 1px solid hsl(var(--border));
}

.prose td {
  padding: 0.75rem 1rem;
  border: 1px solid hsl(var(--border));
  vertical-align: top;
}

.prose tr:nth-child(even) {
  background: hsl(var(--muted) / 0.3);
}

/* 引用块样式 */
.prose blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
  background: hsl(var(--muted) / 0.3);
  padding: 1rem;
  border-radius: 0.5rem;
}

/* 标题样式 */
.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: hsl(var(--foreground));
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.prose h1 {
  font-size: 1.875rem;
  border-bottom: 2px solid hsl(var(--border));
  padding-bottom: 0.5rem;
}

.prose h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.25rem;
}

.prose h3 {
  font-size: 1.25rem;
}

.prose h4 {
  font-size: 1.125rem;
}

/* 分隔线 */
.prose hr {
  border: none;
  border-top: 2px solid hsl(var(--border));
  margin: 2rem 0;
}

/* 链接样式 */
.prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-color: hsl(var(--primary) / 0.3);
  text-underline-offset: 0.25rem;
  transition: all 0.2s ease;
}

.prose a:hover {
  text-decoration-color: hsl(var(--primary));
  color: hsl(var(--primary) / 0.8);
}

/* 段落间距 */
.prose p {
  margin: 1rem 0;
  line-height: 1.7;
}

/* 强调文本 */
.prose strong {
  font-weight: 700;
  color: hsl(var(--foreground));
}

.prose em {
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* 删除线 */
.prose del {
  text-decoration: line-through;
  color: hsl(var(--muted-foreground));
}

/* 代码块容器 */
.prose pre {
  margin: 1.5rem 0;
  overflow-x: auto;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
}

/* 响应式优化 */
@media (max-width: 768px) {
  .prose {
    font-size: 0.875rem;
  }
  
  .prose h1 {
    font-size: 1.5rem;
  }
  
  .prose h2 {
    font-size: 1.25rem;
  }
  
  .prose h3 {
    font-size: 1.125rem;
  }
  
  .prose table {
    font-size: 0.75rem;
  }
  
  .prose th,
  .prose td {
    padding: 0.5rem;
  }
}
