'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Palette, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Eye, Brush, Heart, Target } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 设计场景选项
const DESIGN_SCENARIOS = [
  { id: 'brand_identity', name: 'Brand Identity', description: 'Logo, business cards, brand guidelines' },
  { id: 'website', name: 'Website Design', description: 'Web interfaces and digital experiences' },
  { id: 'mobile_app', name: 'Mobile App', description: 'iOS and Android app interfaces' },
  { id: 'poster', name: 'Poster Design', description: 'Print advertising and promotional materials' },
  { id: 'packaging', name: 'Product Packaging', description: 'Product boxes and packaging design' },
  { id: 'interior', name: 'Interior Design', description: 'Room and space color schemes' },
  { id: 'presentation', name: 'Presentation', description: 'Slides and business presentations' },
  { id: 'social_media', name: 'Social Media', description: 'Instagram, Facebook, Twitter graphics' },
  { id: 'custom', name: 'Custom Scenario', description: 'Define your own design context' }
]

// 预设色彩方案
const COLOR_PRESETS = {
  'brand_identity': [
    { hex: '#FF6B6B', name: 'Primary' },
    { hex: '#4ECDC4', name: 'Secondary' },
    { hex: '#45B7D1', name: 'Accent' },
    { hex: '#96CEB4', name: 'Supporting' },
    { hex: '#FFEAA7', name: 'Highlight' }
  ],
  'website': [
    { hex: '#2D3E50', name: 'Primary' },
    { hex: '#3498DB', name: 'Secondary' },
    { hex: '#1ABC9C', name: 'Accent' },
    { hex: '#ECF0F1', name: 'Background' },
    { hex: '#E74C3C', name: 'Call to Action' }
  ],
  'mobile_app': [
    { hex: '#6C5CE7', name: 'Primary' },
    { hex: '#00CECE', name: 'Secondary' },
    { hex: '#FDCB6E', name: 'Accent' },
    { hex: '#FFFFFF', name: 'Background' },
    { hex: '#FF7675', name: 'Notification' }
  ],
  'poster': [
    { hex: '#D63031', name: 'Primary' },
    { hex: '#0A3D62', name: 'Secondary' },
    { hex: '#FEF9E7', name: 'Background' },
    { hex: '#F0DF87', name: 'Accent' },
    { hex: '#2C3335', name: 'Text' }
  ],
  'packaging': [
    { hex: '#2E86DE', name: 'Primary' },
    { hex: '#FF9F43', name: 'Secondary' },
    { hex: '#10AC84', name: 'Accent' },
    { hex: '#F5F6FA', name: 'Background' },
    { hex: '#576574', name: 'Text' }
  ]
}

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/GLM-4.1V-9B-Thinking',
    name: 'GLM-4.1V',
    description: 'Multimodal vision-language model, color understanding'
  },
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative expression, design concept articulation'
  }
]

interface ColorInput {
  hex: string
  name: string
}

export function ColorPaletteStorytellerClient() {
  // 表单状态
  const [scenario, setScenario] = useState('')
  const [customScenario, setCustomScenario] = useState('')
  const [colors, setColors] = useState<ColorInput[]>([
    { hex: '#FF6B6B', name: 'Primary' },
    { hex: '#4ECDC4', name: 'Secondary' },
    { hex: '#45B7D1', name: 'Accent' },
    { hex: '#96CEB4', name: 'Supporting' },
    { hex: '#FFEAA7', name: 'Highlight' }
  ])
  const [emotions, setEmotions] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/GLM-4.1V-9B-Thinking')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedStory, setGeneratedStory] = useState('')
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 更新颜色
  const updateColor = (index: number, field: 'hex' | 'name', value: string) => {
    const newColors = [...colors]
    if (newColors[index]) {
      newColors[index] = { ...newColors[index], [field]: value }
      setColors(newColors)
    }
  }

  // 添加颜色
  const addColor = () => {
    if (colors.length < 8) {
      setColors([...colors, { hex: '#000000', name: `Color ${colors.length + 1}` }])
    }
  }

  // 删除颜色
  const removeColor = (index: number) => {
    if (colors.length > 2) {
      const newColors = colors.filter((_, i) => i !== index)
      setColors(newColors)
    }
  }

  // 验证HEX颜色格式
  const isValidHex = (hex: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex)
  }

  // 应用预设色彩方案
  const applyColorPreset = (scenarioId: string) => {
    const preset = COLOR_PRESETS[scenarioId as keyof typeof COLOR_PRESETS]
    if (preset) {
      setColors([...preset])
      toast({
        title: "Color Preset Applied",
        description: `Applied ${DESIGN_SCENARIOS.find(s => s.id === scenarioId)?.name} color scheme.`,
      })
    }
  }

  // 生成配色故事
  const handleGenerate = async () => {
    if (!scenario || !emotions.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    // 验证所有颜色格式
    const invalidColors = colors.filter(color => !isValidHex(color.hex))
    if (invalidColors.length > 0) {
      toast({
        title: "Invalid Color Format",
        description: "Please use valid HEX color codes (e.g., #FF6B6B).",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/color-palette-storyteller', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scenario: scenario === 'custom' ? customScenario : scenario,
          colors: colors.filter(color => isValidHex(color.hex)),
          emotions: emotions.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate color story')
      }

      const data = await response.json()
      setGeneratedStory(data.story)

      toast({
        title: "Color Story Generated!",
        description: "Professional design explanation created successfully.",
      })

    } catch (error) {
      setError('Failed to generate color story. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制故事到剪贴板
  const copyStory = async () => {
    try {
      await navigator.clipboard.writeText(generatedStory)
      toast({
        title: "Copied!",
        description: "Color story copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-950 dark:to-purple-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                AI Color Palette Storyteller
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Design Color Stories
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>professional color palette explanations</strong> for clients. 
              Input your colors and emotions, get design rationale with color psychology.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Stories
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Color Psychology
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Professional Quality
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5 text-primary" />
                      Color Story Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Design Scenario */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Design Scenario *</label>
                      <Select value={scenario} onValueChange={setScenario}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose design context" />
                        </SelectTrigger>
                        <SelectContent>
                          {DESIGN_SCENARIOS.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Scenario Input */}
                    {scenario === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Scenario *</label>
                        <input
                          type="text"
                          placeholder="Describe your design context"
                          value={customScenario}
                          onChange={(e) => setCustomScenario(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Color Palette */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Color Palette *</label>

                      {/* Color Presets */}
                      {scenario && COLOR_PRESETS[scenario as keyof typeof COLOR_PRESETS] && (
                        <div className="mb-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => applyColorPreset(scenario)}
                            className="text-xs"
                          >
                            <Palette className="h-3 w-3 mr-1" />
                            Use {DESIGN_SCENARIOS.find(s => s.id === scenario)?.name} Colors
                          </Button>
                        </div>
                      )}

                      <div className="space-y-3">
                        {colors.map((color, index) => (
                          <div key={index} className="flex items-center gap-3">
                            <div className="flex items-center gap-2 flex-1">
                              <div 
                                className="w-10 h-10 rounded-lg border-2 border-gray-200 flex-shrink-0"
                                style={{ backgroundColor: isValidHex(color.hex) ? color.hex : '#cccccc' }}
                              />
                              <input
                                type="text"
                                placeholder="#FF6B6B"
                                value={color.hex}
                                onChange={(e) => updateColor(index, 'hex', e.target.value)}
                                className="w-24 px-2 py-1 text-sm border border-input rounded"
                              />
                              <input
                                type="text"
                                placeholder="Color name"
                                value={color.name}
                                onChange={(e) => updateColor(index, 'name', e.target.value)}
                                className="flex-1 px-2 py-1 text-sm border border-input rounded"
                              />
                            </div>
                            {colors.length > 2 && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => removeColor(index)}
                                className="text-red-500 hover:text-red-700"
                              >
                                ×
                              </Button>
                            )}
                          </div>
                        ))}
                        {colors.length < 8 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={addColor}
                            className="w-full"
                          >
                            + Add Color
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Emotions */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Target Emotions *</label>
                      <input
                        type="text"
                        placeholder="e.g., trustworthy, energetic, sophisticated"
                        value={emotions}
                        onChange={(e) => setEmotions(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter 3 emotions you want the colors to convey
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Story...
                        </>
                      ) : (
                        <>
                          <Palette className="h-4 w-4 mr-2" />
                          Generate Color Story
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-primary" />
                        Design Story
                      </span>
                      {generatedStory && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copyStory}
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedStory ? (
                      <div className="space-y-4">
                        {/* Color Palette Preview */}
                        <div className="flex gap-2 mb-4">
                          {colors.filter(color => isValidHex(color.hex)).map((color, index) => (
                            <div key={index} className="text-center">
                              <div 
                                className="w-12 h-12 rounded-lg border-2 border-gray-200 mb-1"
                                style={{ backgroundColor: color.hex }}
                              />
                              <div className="text-xs text-muted-foreground">{color.name}</div>
                            </div>
                          ))}
                        </div>
                        
                        {/* Generated Story */}
                        <div className="bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-950 dark:to-purple-950 p-4 rounded-lg">
                          <div className="prose prose-sm max-w-none">
                            <pre className="whitespace-pre-wrap text-sm font-sans">
                              {generatedStory}
                            </pre>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Palette className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your color story will appear here</p>
                        <p className="text-sm">Set up your palette and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Use Our Color Palette Storyteller?
            </h2>
            <p className="text-xl text-muted-foreground">
              Transform color choices into compelling design narratives that clients understand and appreciate
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Eye className="h-6 w-6 text-pink-600" />
                </div>
                <h3 className="font-semibold mb-2">Color Psychology</h3>
                <p className="text-sm text-muted-foreground">
                  Explain the psychological impact and emotional resonance of each color choice
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Brush className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Design Theory</h3>
                <p className="text-sm text-muted-foreground">
                  Professional explanations based on color theory and visual harmony principles
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Client-Ready</h3>
                <p className="text-sm text-muted-foreground">
                  Professional language that clients can understand and appreciate
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Emotional Connection</h3>
                <p className="text-sm text-muted-foreground">
                  Connect colors to specific emotions and brand personality traits
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Generate comprehensive color explanations in seconds, not hours
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Usage Guidelines</h3>
                <p className="text-sm text-muted-foreground">
                  Practical recommendations for implementing colors in real designs
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Palette className="h-6 w-6 text-yellow-600" />
                </div>
                <h3 className="font-semibold mb-2">Color Presets</h3>
                <p className="text-sm text-muted-foreground">
                  Quick-start with professionally curated color schemes for each scenario
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create professional color explanations in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-pink-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Scenario</h3>
              <p className="text-sm text-muted-foreground">
                Select your design context from brand identity, web, app, poster, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Input Colors</h3>
              <p className="text-sm text-muted-foreground">
                Add your color palette with HEX codes and descriptive names
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Define Emotions</h3>
              <p className="text-sm text-muted-foreground">
                Specify the emotions and feelings you want the colors to convey
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Get Story</h3>
              <p className="text-sm text-muted-foreground">
                Receive a professional explanation ready to present to clients
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Every Design Project
            </h2>
            <p className="text-xl text-muted-foreground">
              Create compelling color stories for any design scenario
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎨</span>
                </div>
                <h3 className="font-semibold mb-2">Brand Identity</h3>
                <p className="text-sm text-muted-foreground">
                  Explain logo colors, brand guidelines, and visual identity choices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💻</span>
                </div>
                <h3 className="font-semibold mb-2">Web & App Design</h3>
                <p className="text-sm text-muted-foreground">
                  Justify interface colors, user experience, and accessibility choices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📦</span>
                </div>
                <h3 className="font-semibold mb-2">Product Packaging</h3>
                <p className="text-sm text-muted-foreground">
                  Explain packaging colors that attract customers and convey quality
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏠</span>
                </div>
                <h3 className="font-semibold mb-2">Interior Design</h3>
                <p className="text-sm text-muted-foreground">
                  Create mood explanations for room colors and spatial design
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our color palette storyteller
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How does the AI understand color psychology?",
                  answer: "Our AI is trained on color theory, psychology research, and design principles. It understands how different colors affect emotions, cultural associations, and user behavior in various contexts."
                },
                {
                  question: "Can I use the generated explanations with clients?",
                  answer: "Absolutely! The explanations are written in professional, client-friendly language that you can use directly in presentations, proposals, and design documentation."
                },
                {
                  question: "What color formats are supported?",
                  answer: "We support HEX color codes (e.g., #FF6B6B). You can input 2-8 colors in your palette, and each color should have a descriptive name for better context."
                },
                {
                  question: "How accurate are the color psychology explanations?",
                  answer: "Our explanations are based on established color psychology principles and design theory. However, color perception can be subjective and culturally influenced."
                },
                {
                  question: "Can I customize the explanation style?",
                  answer: "The explanations are automatically tailored to your chosen design scenario (brand, web, app, etc.) and target emotions to ensure relevance and accuracy."
                },
                {
                  question: "What if I need to explain a monochromatic palette?",
                  answer: "Our AI can work with any palette size, including monochromatic schemes. It will focus on tonal variations, contrast, and the psychological impact of your chosen color family."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
