'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'
import Link from 'next/link'
import {
  Coins,
  Crown,
  User,
  Calendar,
  TrendingUp,
  MessageSquare,
  ArrowRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface UserStats {
  plan: 'free' | 'premium'
  credits: number
  usedCredits: number
  totalCreditsEarned: number
  subscriptionPeriodEnd?: string
}

export function DashboardClient() {
  const { user } = useUser()
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch('/api/user/stats')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        }
      } catch (error) {
        // Failed to fetch user stats
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      fetchUserStats()
    }
  }, [user])

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-32 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Unable to load user information</p>
          <Link href="/chat">
            <Button className="mt-4">Back to Chat</Button>
          </Link>
        </div>
      </div>
    )
  }

  const remainingCredits = stats.credits - stats.usedCredits
  const isPremium = stats.plan === 'premium'
  const usagePercentage = stats.credits > 0 ? (stats.usedCredits / stats.credits) * 100 : 0

  return (
    <div className="min-h-screen bg-background">
      <main className="max-w-6xl mx-auto p-6 space-y-6">

        {/* 用户信息卡片 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold">
                    {user?.fullName || user?.firstName || "User"}
                  </h2>
                  <p className="text-muted-foreground">
                    {user?.primaryEmailAddress?.emailAddress}
                  </p>
                </div>
              </div>
              <Badge
                variant={isPremium ? "default" : "secondary"}
                className={isPremium ? "bg-gradient-to-r from-purple-500 to-blue-500" : ""}
              >
                {isPremium ? (
                  <div className="flex items-center gap-1">
                    <Crown className="h-3 w-3" />
                    Premium Member
                  </div>
                ) : (
                  "Free User"
                )}
              </Badge>
            </div>
          </CardHeader>
        </Card>

        {/* Key Features Section */}
        <div className="mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">
            Key Features of Your Dashboard
          </h2>
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <MessageSquare className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Simplify Complex Texts Instantly</h3>
                <p className="text-sm text-muted-foreground">
                  Transform any complex content into clear, understandable language with our AI
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <User className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Personalized AI Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Customize explanation styles and complexity levels to match your preferences
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Manage Account & Preferences</h3>
                <p className="text-sm text-muted-foreground">
                  Track usage, manage credits, and control your AI dashboard settings
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Account Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Available Credits */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Coins className="h-5 w-5 text-yellow-500" />
                Available Credits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary mb-2">
                {remainingCredits}
              </div>
              <div className="w-full bg-muted rounded-full h-2 mb-2">
                <div
                  className="bg-primary rounded-full h-2 transition-all"
                  style={{ width: `${Math.max(0, 100 - usagePercentage)}%` }}
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Used {stats.usedCredits} / {stats.credits}
              </p>
            </CardContent>
          </Card>

          {/* Membership Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Calendar className="h-5 w-5 text-blue-500" />
                Membership Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isPremium ? (
                <div>
                  <div className="text-lg font-bold text-purple-600 mb-2">
                    Premium Member
                  </div>
                  {stats.subscriptionPeriodEnd && (
                    <p className="text-sm text-muted-foreground">
                      Expires: {new Date(stats.subscriptionPeriodEnd).toLocaleDateString()}
                    </p>
                  )}
                </div>
              ) : (
                <div>
                  <div className="text-lg font-bold text-muted-foreground mb-2">
                    Free Plan
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    Upgrade to Premium for higher request limits
                  </p>
                  <Button
                    size="sm"
                    className="w-full"
                    onClick={async () => {
                      try {
                        const response = await fetch('/api/payments/create-checkout', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                        })
                        const data = await response.json()
                        if (data.success && data.checkout_url) {
                          window.location.href = data.checkout_url
                        } else {
                          alert('Failed to start payment process. Please try again.')
                        }
                      } catch (error) {
                        alert('Failed to start payment process. Please try again.')
                      }
                    }}
                  >
                    Upgrade to Premium Now
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>





        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Quick Actions</CardTitle>
            <p className="text-muted-foreground">Get started with these essential features</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href="/chat">
                <Button className="h-auto p-4 w-full flex items-center justify-between group hover:scale-105 transition-transform">
                  <div className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">Start Simplifying</div>
                      <div className="text-sm opacity-80">Begin AI-powered text simplification</div>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Link href="/dashboard/credits">
                <Button variant="outline" className="h-auto p-4 w-full flex items-center justify-between group hover:scale-105 transition-transform">
                  <div className="flex items-center gap-3">
                    <Coins className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">View Credits</div>
                      <div className="text-sm opacity-80">Check usage and credit details</div>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
