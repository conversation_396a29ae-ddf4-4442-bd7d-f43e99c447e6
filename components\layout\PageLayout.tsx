import { Header } from './Header'
import { Footer } from './Footer'

interface PageLayoutProps {
  children: React.ReactNode
  showHeader?: boolean
  showFooter?: boolean
  className?: string
}

export function PageLayout({
  children,
  showHeader = true,
  showFooter = true,
  className = ""
}: PageLayoutProps) {
  return (
    <div className={`flex flex-col min-h-screen ${className}`} suppressHydrationWarning>
      {showHeader && <Header />}
      <main className="flex-1">
        {children}
      </main>
      {showFooter && <Footer />}
    </div>
  )
}
