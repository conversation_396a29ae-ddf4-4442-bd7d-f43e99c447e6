import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { ChatPageClient } from './ChatPageClient'
import type { Metadata } from 'next'

// Chat页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Real-Time AI Chat for Clear Answers - HumanWhisper',
  description: 'Talk to HumanWhisper\'s AI to get fast, simplified responses to any topic. Perfect for questions, ideas, and learning without jargon.',
  keywords: [
    'AI chat',
    'ask AI questions',
    'simplify with AI',
    'real-time AI help',
    'conversational AI'
  ],
  openGraph: {
    title: 'Real-Time AI Chat for Clear Answers - HumanWhisper',
    description: 'Talk to HumanWhisper\'s AI to get fast, simplified responses to any topic. Perfect for questions, ideas, and learning without jargon.',
    url: '/chat',
    type: 'website',
  },
  twitter: {
    title: 'Real-Time AI Chat for Clear Answers - HumanWhisper',
    description: 'Talk to HumanWhisper\'s AI to get fast, simplified responses to any topic. Perfect for questions, ideas, and learning without jargon.',
  },
  alternates: {
    canonical: '/chat',
  },
}

export default async function ChatPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  return <ChatPageClient />
}
