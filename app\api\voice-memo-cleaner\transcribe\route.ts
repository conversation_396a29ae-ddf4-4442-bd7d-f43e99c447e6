import { NextRequest, NextResponse } from 'next/server'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

export async function POST(request: NextRequest) {
  try {
    // 解析表单数据
    const formData = await request.formData()
    const audioFile = formData.get('audio') as File
    const model = formData.get('model') as string || 'FunAudioLLM/SenseVoiceSmall'

    // 输入验证
    if (!audioFile) {
      return NextResponse.json(
        { error: 'Audio file is required' },
        { status: 400 }
      )
    }

    // 检查文件类型 - 包含所有常见的音频MIME类型
    const allowedTypes = [
      // MP3
      'audio/mpeg', 'audio/mp3',
      // WAV
      'audio/wav', 'audio/x-wav', 'audio/wave',
      // M4A
      'audio/m4a', 'audio/mp4', 'audio/x-m4a',
      // OGG
      'audio/ogg', 'audio/ogg; codecs=vorbis',
      // WebM
      'audio/webm'
    ]
    if (!allowedTypes.includes(audioFile.type)) {
      return NextResponse.json(
        { error: 'Invalid audio file type' },
        { status: 400 }
      )
    }

    // 检查文件大小 (25MB限制)
    if (audioFile.size > 25 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Audio file too large' },
        { status: 400 }
      )
    }

    // 准备API请求
    const apiFormData = new FormData()
    apiFormData.append('file', audioFile)
    apiFormData.append('model', model)

    // 调用硅基流动语音转文字API
    const response = await fetch(`${getSiliconFlowBaseUrl()}/audio/transcriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getRandomSiliconFlowApiKey()}`,
      },
      body: apiFormData
    })

    if (!response.ok) {
      throw new Error('Transcription API request failed')
    }

    const result = await response.json()

    // 提取转录文本
    const transcribedText = result.text?.trim()

    if (!transcribedText) {
      throw new Error('No text was transcribed from the audio')
    }

    return NextResponse.json({
      text: transcribedText,
      metadata: {
        model,
        audioSize: audioFile.size,
        audioType: audioFile.type,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Transcription model not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'file_too_large') {
      return NextResponse.json(
        { error: 'Audio file is too large' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to transcribe audio' },
      { status: 500 }
    )
  }
}
