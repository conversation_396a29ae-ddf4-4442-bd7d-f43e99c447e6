import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 受众类型映射
const AUDIENCE_TYPE_MAP: Record<string, string> = {
  'fitness': 'fitness enthusiasts focused on workout routines, nutrition, and physical health goals',
  'writing': 'writers and authors interested in creative writing, journaling, and storytelling skills',
  'finance': 'people seeking financial wellness through budgeting, saving, investment, and money management',
  'photography': 'photography enthusiasts wanting to improve photo skills, composition, editing, and visual storytelling',
  'mindfulness': 'individuals interested in mindfulness, meditation, mental health, stress relief, and self-awareness',
  'productivity': 'productivity-focused people working on time management, organization, and efficiency habits',
  'creativity': 'creative individuals interested in drawing, painting, crafts, and artistic expression',
  'learning': 'lifelong learners focused on new skills, languages, and professional development',
  'social': 'people wanting to improve social connection, networking, relationships, and communication skills',
  'wellness': 'individuals focused on general wellness, holistic health, lifestyle improvement, and self-care'
}

// 构建30天挑战生成prompt
function buildChallengePrompt(
  audienceType: string,
  painPoint: string,
  completionFormat: string
): string {
  const audienceDesc = AUDIENCE_TYPE_MAP[audienceType] || audienceType

  const prompt = `You are a community engagement expert specializing in creating successful 30-day challenges that boost member retention and build lasting habits. Your task is to design a comprehensive challenge program that addresses specific pain points and keeps participants engaged throughout the entire month.

**Target Community:**
- Audience: ${audienceDesc}
- Pain Point: ${painPoint}
- Check-in Format: ${completionFormat}

**Instructions:**
Create a complete 30-day challenge program that includes:

1. **Challenge Title**: Catchy, motivational name that clearly communicates the goal
2. **Daily Tasks** (30 items): Progressive daily activities that build toward the goal
3. **Check-in Prompts** (7-10 items): Engaging questions/prompts to encourage community interaction
4. **Reward System**: Weekly rewards, milestone achievements, and completion reward
5. **Engagement Tips**: Strategies to maintain participation and prevent dropoff

**Design Principles:**
- **Progressive Difficulty**: Start easy, gradually increase challenge level
- **Habit Formation**: Focus on building sustainable, long-term habits
- **Community Building**: Encourage interaction, support, and accountability
- **Retention Focus**: Combat the "3-day dropout" problem with strategic engagement
- **Measurable Progress**: Clear, trackable activities using the specified format (${completionFormat})
- **Psychological Motivation**: Use gamification, social proof, and achievement psychology

**Daily Task Requirements:**
- Days 1-7: Foundation building, easy wins, habit establishment
- Days 8-14: Skill development, increased commitment, community bonding
- Days 15-21: Challenge intensification, pushing comfort zones
- Days 22-30: Mastery, reflection, preparation for long-term continuation

**Check-in Prompt Guidelines:**
- Encourage sharing progress and challenges
- Build community connection and support
- Prompt reflection and learning
- Create opportunities for peer encouragement
- Include both individual and group-focused prompts

**Reward System Structure:**
- Weekly rewards that acknowledge progress and effort
- Milestone rewards for key achievements (Day 7, 14, 21)
- Ultimate completion reward that feels significant and meaningful
- Mix of intrinsic (personal satisfaction) and extrinsic (tangible) rewards

**Output Format:**
Return a valid JSON object:

{
  "challengeTitle": "Motivational challenge name",
  "dailyTasks": ["Day 1 task", "Day 2 task", ..., "Day 30 task"],
  "checkInPrompts": ["Prompt 1", "Prompt 2", ..., "Prompt 8"],
  "rewardSystem": {
    "weekly": ["Week 1 reward", "Week 2 reward", "Week 3 reward", "Week 4 reward"],
    "milestones": ["Day 7 milestone", "Day 14 milestone", "Day 21 milestone"],
    "completion": "30-day completion reward"
  },
  "engagementTips": ["Tip 1", "Tip 2", "Tip 3", "Tip 4", "Tip 5"]
}

**Important Guidelines:**
- Ensure all 30 daily tasks are unique and progressively challenging
- Make tasks specific and actionable, not vague
- Consider the specified completion format (${completionFormat}) in task design
- Address the pain point (${painPoint}) throughout the challenge
- Create genuine community value, not just individual achievement
- Design for long-term habit formation beyond the 30 days

Generate the complete 30-day challenge program now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      audienceType,
      painPoint,
      completionFormat,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!audienceType || !painPoint?.trim() || !completionFormat?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildChallengePrompt(
      audienceType,
      painPoint.trim(),
      completionFormat.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let challenge: any
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      challenge = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!challenge.challengeTitle || !Array.isArray(challenge.dailyTasks) || !challenge.rewardSystem) {
        throw new Error('Invalid response structure')
      }
      
      // 确保有30个每日任务
      if (challenge.dailyTasks.length < 30) {
        const additionalTasks = Array(30 - challenge.dailyTasks.length).fill(0).map((_, index) => 
          `Continue building your habit with today's focused practice session (Day ${challenge.dailyTasks.length + index + 1})`
        )
        challenge.dailyTasks = [...challenge.dailyTasks, ...additionalTasks]
      }
      
      // 确保有足够的check-in prompts
      if (!Array.isArray(challenge.checkInPrompts) || challenge.checkInPrompts.length < 5) {
        challenge.checkInPrompts = [
          'Share your biggest win from this week!',
          'What challenge are you facing and how can the community help?',
          'Post a photo/update of your progress using our format',
          'What has surprised you most about this journey so far?',
          'Encourage a fellow community member who is struggling',
          'Share a tip that has helped you stay consistent',
          'Reflect on how you\'ve grown since starting this challenge',
          'What will you do differently in the final week?'
        ]
      }
      
      // 确保奖励系统完整
      if (!challenge.rewardSystem.weekly || !Array.isArray(challenge.rewardSystem.weekly)) {
        challenge.rewardSystem.weekly = [
          'Recognition badge and community shoutout',
          'Access to exclusive bonus content',
          'Featured member spotlight',
          'Special completion certificate'
        ]
      }
      
      if (!challenge.rewardSystem.milestones || !Array.isArray(challenge.rewardSystem.milestones)) {
        challenge.rewardSystem.milestones = [
          'First week completion badge',
          'Halfway hero recognition',
          'Final stretch champion status'
        ]
      }
      
      if (!challenge.rewardSystem.completion) {
        challenge.rewardSystem.completion = 'Exclusive 30-day champion status and access to advanced community features'
      }
      
      // 确保有参与技巧
      if (!Array.isArray(challenge.engagementTips) || challenge.engagementTips.length < 3) {
        challenge.engagementTips = [
          'Create daily check-in threads to encourage consistent participation',
          'Pair members as accountability partners for mutual support',
          'Share success stories and progress photos to inspire others',
          'Host weekly live Q&A sessions to address challenges',
          'Use gamification elements like leaderboards and badges'
        ]
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，创建基本结构
      challenge = {
        challengeTitle: `30-Day ${audienceType.charAt(0).toUpperCase() + audienceType.slice(1)} Transformation Challenge`,
        dailyTasks: Array(30).fill(0).map((_, index) => 
          `Day ${index + 1}: Complete your daily ${completionFormat} and share your progress with the community`
        ),
        checkInPrompts: [
          'Share your biggest win from this week!',
          'What challenge are you facing and how can the community help?',
          'Post a photo/update of your progress',
          'What has surprised you most about this journey so far?',
          'Encourage a fellow community member',
          'Share a tip that has helped you stay consistent',
          'Reflect on how you\'ve grown since starting',
          'What will you do differently in the final week?'
        ],
        rewardSystem: {
          weekly: [
            'Recognition badge and community shoutout',
            'Access to exclusive bonus content',
            'Featured member spotlight',
            'Special completion certificate'
          ],
          milestones: [
            'First week completion badge',
            'Halfway hero recognition',
            'Final stretch champion status'
          ],
          completion: 'Exclusive 30-day champion status and advanced community access'
        },
        engagementTips: [
          'Create daily check-in threads for consistent participation',
          'Pair members as accountability partners',
          'Share success stories to inspire others',
          'Host weekly live sessions to address challenges',
          'Use gamification with leaderboards and badges'
        ]
      }
    }

    return NextResponse.json({
      challenge,
      metadata: {
        audienceType,
        painPoint,
        completionFormat,
        taskCount: challenge.dailyTasks.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate 30-day challenge' },
      { status: 500 }
    )
  }
}
