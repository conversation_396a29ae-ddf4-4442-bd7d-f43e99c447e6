import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { ChatSession, ChatHistoryState, Message, GroupedSessions, TimeGroup } from '@/types'
import { generateId } from '@/lib/utils'

interface ChatHistoryActions {
  // 会话管理
  createSession: (firstMessage?: Message) => string
  deleteSession: (sessionId: string) => void
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void
  setCurrentSession: (sessionId: string | null) => void
  
  // 消息管理
  addMessage: (sessionId: string, message: Message) => void
  updateSessionMessages: (sessionId: string, messages: Message[]) => void
  
  // 工具方法
  getSession: (sessionId: string) => ChatSession | undefined
  getGroupedSessions: () => GroupedSessions
  generateSessionTitle: (messages: Message[]) => string
  clearAllSessions: () => void
  
  // 批量操作
  deleteSessions: (sessionIds: string[]) => void
  deleteSessionsByTimeGroup: (timeGroup: TimeGroup) => void
}

type ChatHistoryStore = ChatHistoryState & ChatHistoryActions

// 时间分组工具函数
const getTimeGroup = (date: Date): TimeGroup => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const thisWeekStart = new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000)
  const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000)
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
  
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  if (targetDate.getTime() === today.getTime()) return 'today'
  if (targetDate.getTime() === yesterday.getTime()) return 'yesterday'
  if (targetDate >= thisWeekStart) return 'thisWeek'
  if (targetDate >= lastWeekStart) return 'lastWeek'
  if (targetDate >= thisMonthStart) return 'thisMonth'
  return 'older'
}

// 生成会话标题
const generateTitle = (messages: Message[]): string => {
  const firstUserMessage = messages.find(m => m.role === 'user')
  if (!firstUserMessage) return 'New Chat'

  // 提取纯文本内容（去除图片标记）
  let content = firstUserMessage.content.replace(/\[包含 \d+ 张图片\]/g, '').replace(/\[Contains \d+ images?\]/g, '').trim()

  // 限制长度
  if (content.length > 30) {
    content = content.substring(0, 30) + '...'
  }

  return content || 'New Chat'
}

// 生成预览文本
const generatePreviewText = (messages: Message[]): string => {
  const lastMessage = messages[messages.length - 1]
  if (!lastMessage) return ''
  
  let content = lastMessage.content
  if (content.length > 50) {
    content = content.substring(0, 50) + '...'
  }
  
  return content
}

export const useChatHistoryStore = create<ChatHistoryStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      sessions: [],
      currentSessionId: null,
      isLoading: false,

      // 创建新会话
      createSession: (firstMessage?: Message) => {
        const sessionId = generateId()
        const now = new Date()
        
        const newSession: ChatSession = {
          id: sessionId,
          title: firstMessage ? generateTitle([firstMessage]) : 'New Chat',
          messages: firstMessage ? [firstMessage] : [],
          createdAt: now,
          updatedAt: now,
          model: firstMessage?.model || 'gpt-4.1-nano',
          messageCount: firstMessage ? 1 : 0,
          previewText: firstMessage ? generatePreviewText([firstMessage]) : ''
        }

        set(state => ({
          sessions: [newSession, ...state.sessions],
          currentSessionId: sessionId
        }))

        return sessionId
      },

      // 删除会话
      deleteSession: (sessionId: string) => {
        set(state => ({
          sessions: state.sessions.filter(s => s.id !== sessionId),
          currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId
        }))
      },

      // 更新会话
      updateSession: (sessionId: string, updates: Partial<ChatSession>) => {
        set(state => ({
          sessions: state.sessions.map(session =>
            session.id === sessionId
              ? { ...session, ...updates, updatedAt: new Date() }
              : session
          )
        }))
      },

      // 设置当前会话
      setCurrentSession: (sessionId: string | null) => {
        set({ currentSessionId: sessionId })
      },

      // 添加消息到会话
      addMessage: (sessionId: string, message: Message) => {
        set(state => ({
          sessions: state.sessions.map(session => {
            if (session.id === sessionId) {
              const newMessages = [...session.messages, message]
              return {
                ...session,
                messages: newMessages,
                messageCount: newMessages.length,
                updatedAt: new Date(),
                title: session.title === 'New Chat' ? generateTitle(newMessages) : session.title,
                previewText: generatePreviewText(newMessages)
              }
            }
            return session
          })
        }))
      },

      // 更新会话的所有消息
      updateSessionMessages: (sessionId: string, messages: Message[]) => {
        set(state => ({
          sessions: state.sessions.map(session => {
            if (session.id === sessionId) {
              return {
                ...session,
                messages,
                messageCount: messages.length,
                updatedAt: new Date(),
                title: session.title === 'New Chat' ? generateTitle(messages) : session.title,
                previewText: generatePreviewText(messages)
              }
            }
            return session
          })
        }))
      },

      // 获取会话
      getSession: (sessionId: string) => {
        return get().sessions.find(s => s.id === sessionId)
      },

      // 获取分组后的会话
      getGroupedSessions: () => {
        const sessions = get().sessions
        const grouped: GroupedSessions = {}

        sessions.forEach(session => {
          const group = getTimeGroup(session.updatedAt)
          if (!grouped[group]) {
            grouped[group] = []
          }
          grouped[group]!.push(session)
        })

        // 对每个分组内的会话按时间排序（最新的在前）
        Object.keys(grouped).forEach(key => {
          const group = key as TimeGroup
          grouped[group]!.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        })

        return grouped
      },

      // 生成会话标题
      generateSessionTitle: (messages: Message[]) => {
        return generateTitle(messages)
      },

      // 清空所有会话
      clearAllSessions: () => {
        set({
          sessions: [],
          currentSessionId: null
        })
      },

      // 批量删除会话
      deleteSessions: (sessionIds: string[]) => {
        set(state => ({
          sessions: state.sessions.filter(s => !sessionIds.includes(s.id)),
          currentSessionId: sessionIds.includes(state.currentSessionId || '') ? null : state.currentSessionId
        }))
      },

      // 按时间分组删除会话
      deleteSessionsByTimeGroup: (timeGroup: TimeGroup) => {
        set(state => {
          const sessionsToDelete = state.sessions.filter(session => 
            getTimeGroup(session.updatedAt) === timeGroup
          )
          const sessionIdsToDelete = sessionsToDelete.map(s => s.id)
          
          return {
            sessions: state.sessions.filter(s => !sessionIdsToDelete.includes(s.id)),
            currentSessionId: sessionIdsToDelete.includes(state.currentSessionId || '') ? null : state.currentSessionId
          }
        })
      }
    }),
    {
      name: 'chat-history-storage',
      storage: createJSONStorage(() => localStorage),
      // 使用 onRehydrateStorage 来处理 Date 对象恢复
      onRehydrateStorage: () => (state) => {
        if (state?.sessions) {
          state.sessions = state.sessions.map((session: any) => ({
            ...session,
            createdAt: new Date(session.createdAt),
            updatedAt: new Date(session.updatedAt),
            messages: session.messages.map((message: any) => ({
              ...message,
              timestamp: new Date(message.timestamp)
            }))
          }))
        }
      }
    }
  )
)
