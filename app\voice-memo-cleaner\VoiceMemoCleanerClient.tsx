'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Mic, Upload, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, FileAudio, Trash2, Play, Pause } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 录音场景选项
const RECORDING_SCENARIOS = [
  { id: 'meeting', name: 'Meeting Recording', description: 'Business meetings and discussions' },
  { id: 'brainstorm', name: 'Brainstorm Session', description: 'Creative ideas and inspiration notes' },
  { id: 'lecture', name: 'Lecture Notes', description: 'Educational content and classes' },
  { id: 'podcast', name: 'Podcast Draft', description: 'Rough podcast recordings' },
  { id: 'interview', name: 'Interview Recording', description: 'Job interviews or research interviews' },
  { id: 'voice_memo', name: 'Voice Memo', description: 'Personal notes and reminders' },
  { id: 'presentation', name: 'Presentation Practice', description: 'Speech rehearsal and practice' },
  { id: 'custom', name: 'Custom Scenario', description: 'Define your own scenario' }
]

// 清理级别选项
const CLEANING_LEVELS = [
  { id: 'light', name: 'Light Cleaning', description: 'Remove basic filler words (um, uh, like) only' },
  { id: 'moderate', name: 'Moderate Cleaning', description: 'Remove fillers, repetitions, and fix obvious errors' },
  { id: 'thorough', name: 'Thorough Cleaning', description: 'Complete filler word removal with grammar correction' },
  { id: 'professional', name: 'Professional Polish', description: 'Business-ready transcript with comprehensive cleanup' }
]



export function VoiceMemoCleanerClient() {
  // 表单状态
  const [scenario, setScenario] = useState('')
  const [customScenario, setCustomScenario] = useState('')
  const [cleaningLevel, setCleaningLevel] = useState('')
  const [audioFile, setAudioFile] = useState<File | null>(null)
  
  // 处理状态
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStep, setCurrentStep] = useState<'upload' | 'transcribing' | 'cleaning' | 'complete'>('upload')
  const [rawTranscript, setRawTranscript] = useState('')
  const [cleanedTranscript, setCleanedTranscript] = useState('')
  const [error, setError] = useState('')
  
  // 音频播放状态
  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 拖拽状态
  const [isDragOver, setIsDragOver] = useState(false)
  
  const { toast } = useToast()

  // 文件验证函数
  const validateFile = (file: File): boolean => {
    // 检查文件类型 - 包含所有常见的音频MIME类型
    const allowedTypes = [
      // MP3
      'audio/mpeg', 'audio/mp3',
      // WAV
      'audio/wav', 'audio/x-wav', 'audio/wave',
      // M4A
      'audio/m4a', 'audio/mp4', 'audio/x-m4a',
      // OGG
      'audio/ogg', 'audio/ogg; codecs=vorbis',
      // WebM
      'audio/webm'
    ]
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an audio file (MP3, WAV, M4A, OGG, WebM).",
        variant: "destructive"
      })
      return false
    }

    // 检查文件大小 (25MB限制)
    if (file.size > 25 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please upload an audio file smaller than 25MB.",
        variant: "destructive"
      })
      return false
    }

    return true
  }

  // 处理文件设置
  const handleFileSet = (file: File) => {
    if (validateFile(file)) {
      setAudioFile(file)
      setCurrentStep('upload')
      setRawTranscript('')
      setCleanedTranscript('')
      setError('')
    }
  }

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSet(file)
    }
  }

  // 拖拽处理函数
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      if (file) {
        handleFileSet(file)
      }
    }
  }

  // 播放/暂停音频
  const toggleAudioPlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 处理语音转文字和清理
  const handleProcess = async () => {
    if (!audioFile || !scenario || !cleaningLevel) {
      toast({
        title: "Missing Information",
        description: "Please upload an audio file and fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsProcessing(true)
    setError('')
    
    try {
      // 步骤1: 语音转文字
      setCurrentStep('transcribing')
      const transcriptionResponse = await fetch('/api/voice-memo-cleaner/transcribe', {
        method: 'POST',
        body: (() => {
          const formData = new FormData()
          formData.append('audio', audioFile)
          formData.append('model', 'FunAudioLLM/SenseVoiceSmall')
          return formData
        })(),
      })

      if (!transcriptionResponse.ok) {
        throw new Error('Failed to transcribe audio')
      }

      const transcriptionData = await transcriptionResponse.json()
      setRawTranscript(transcriptionData.text)

      // 步骤2: 文本清理
      setCurrentStep('cleaning')
      const cleaningResponse = await fetch('/api/voice-memo-cleaner/clean', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: transcriptionData.text,
          scenario: scenario === 'custom' ? customScenario : scenario,
          cleaningLevel,
          model: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B'
        }),
      })

      if (!cleaningResponse.ok) {
        throw new Error('Failed to clean transcript')
      }

      const cleaningData = await cleaningResponse.json()
      setCleanedTranscript(cleaningData.cleanedText)
      setCurrentStep('complete')

      toast({
        title: "Processing Complete!",
        description: "Your voice memo has been transcribed and cleaned.",
      })

    } catch (error) {
      setError('Failed to process voice memo. Please try again.')
      toast({
        title: "Processing Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: "Transcript copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  // 清除文件
  const clearFile = () => {
    setAudioFile(null)
    setRawTranscript('')
    setCleanedTranscript('')
    setCurrentStep('upload')
    setError('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                AI Voice Memo Cleaner
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Clean Audio Transcripts
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Transform <strong>messy voice recordings</strong> into clean, professional transcripts. 
              Remove filler words, repetitions, and errors automatically.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                AI-Powered
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Privacy Protected
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Multilingual
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Processor Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mic className="h-5 w-5 text-primary" />
                      Audio Upload & Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* File Upload */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Audio File *</label>
                      <div
                        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                          isDragOver
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-primary/50'
                        }`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                      >
                        {audioFile ? (
                          <div className="space-y-3">
                            <FileAudio className="h-12 w-12 text-primary mx-auto" />
                            <div>
                              <p className="font-medium">{audioFile.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {(audioFile.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                            <div className="flex justify-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={toggleAudioPlayback}
                                disabled={!audioFile}
                              >
                                {isPlaying ? <Pause className="h-4 w-4 mr-1" /> : <Play className="h-4 w-4 mr-1" />}
                                {isPlaying ? 'Pause' : 'Play'}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearFile}
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Remove
                              </Button>
                            </div>
                            {audioFile && (
                              <audio
                                ref={audioRef}
                                src={URL.createObjectURL(audioFile)}
                                onEnded={() => setIsPlaying(false)}
                                className="hidden"
                              />
                            )}
                          </div>
                        ) : (
                          <div className="space-y-3">
                            <Upload className={`h-12 w-12 mx-auto transition-colors ${
                              isDragOver ? 'text-primary' : 'text-muted-foreground'
                            }`} />
                            <div>
                              <p className="font-medium">
                                {isDragOver ? 'Drop your audio file here' : 'Upload Audio File'}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {isDragOver
                                  ? 'Release to upload'
                                  : 'Drag & drop or click to browse • MP3, WAV, M4A, OGG, WebM (Max 25MB)'
                                }
                              </p>
                            </div>
                            {!isDragOver && (
                              <Button
                                variant="outline"
                                onClick={() => fileInputRef.current?.click()}
                              >
                                <Upload className="h-4 w-4 mr-2" />
                                Choose File
                              </Button>
                            )}
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="audio/*"
                              onChange={handleFileUpload}
                              className="hidden"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Recording Scenario */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Recording Scenario *</label>
                      <Select value={scenario} onValueChange={setScenario}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose recording type" />
                        </SelectTrigger>
                        <SelectContent>
                          {RECORDING_SCENARIOS.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Scenario Input */}
                    {scenario === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Scenario *</label>
                        <input
                          type="text"
                          placeholder="Describe your recording scenario"
                          value={customScenario}
                          onChange={(e) => setCustomScenario(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Cleaning Level */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Cleaning Level *</label>
                      <Select value={cleaningLevel} onValueChange={setCleaningLevel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose cleaning intensity" />
                        </SelectTrigger>
                        <SelectContent>
                          {CLEANING_LEVELS.map((level) => (
                            <SelectItem key={level.id} value={level.id}>
                              <div>
                                <div className="font-medium">{level.name}</div>
                                <div className="text-xs text-muted-foreground">{level.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Process Button */}
                    <Button 
                      onClick={handleProcess} 
                      disabled={isProcessing || !audioFile}
                      className="w-full"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          {currentStep === 'transcribing' && 'Transcribing Audio...'}
                          {currentStep === 'cleaning' && 'Cleaning Transcript...'}
                        </>
                      ) : (
                        <>
                          <Mic className="h-4 w-4 mr-2" />
                          Process Voice Memo
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                {/* Processing Steps */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Processing Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className={`flex items-center gap-3 ${currentStep === 'upload' ? 'text-primary' : currentStep === 'transcribing' || currentStep === 'cleaning' || currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'upload' ? 'bg-primary text-primary-foreground' : currentStep === 'transcribing' || currentStep === 'cleaning' || currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                          1
                        </div>
                        <span>Upload Audio File</span>
                      </div>
                      <div className={`flex items-center gap-3 ${currentStep === 'transcribing' ? 'text-primary' : currentStep === 'cleaning' || currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'transcribing' ? 'bg-primary text-primary-foreground' : currentStep === 'cleaning' || currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                          2
                        </div>
                        <span>Speech to Text</span>
                      </div>
                      <div className={`flex items-center gap-3 ${currentStep === 'cleaning' ? 'text-primary' : currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'cleaning' ? 'bg-primary text-primary-foreground' : currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                          3
                        </div>
                        <span>Clean & Polish</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                {(rawTranscript || cleanedTranscript) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="flex items-center gap-2">
                          <FileAudio className="h-5 w-5 text-primary" />
                          Transcript Results
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {rawTranscript && (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">Raw Transcript</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(rawTranscript)}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-muted/50 p-3 rounded-lg max-h-32 overflow-y-auto">
                            <p className="text-sm text-muted-foreground">{rawTranscript}</p>
                          </div>
                        </div>
                      )}
                      
                      {cleanedTranscript && (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">Cleaned Transcript</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(cleanedTranscript)}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg max-h-32 overflow-y-auto">
                            <p className="text-sm">{cleanedTranscript}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Error Display */}
                {error && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-red-500 text-sm p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Choose Our AI Voice Memo Cleaner?
            </h2>
            <p className="text-xl text-muted-foreground">
              Transform messy recordings into professional transcripts with advanced AI technology
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">AI-Powered Processing</h3>
                <p className="text-sm text-muted-foreground">
                  Advanced speech recognition and natural language processing for accurate results
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Privacy Protected</h3>
                <p className="text-sm text-muted-foreground">
                  Your audio files are processed securely and not stored on our servers
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Multilingual Support</h3>
                <p className="text-sm text-muted-foreground">
                  Supports multiple languages for international voice memo processing
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FileAudio className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Formats</h3>
                <p className="text-sm text-muted-foreground">
                  Supports MP3, WAV, M4A, OGG, and WebM audio file formats
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Smart Cleaning</h3>
                <p className="text-sm text-muted-foreground">
                  Removes filler words, repetitions, and errors while preserving meaning
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Professional Quality</h3>
                <p className="text-sm text-muted-foreground">
                  Business-ready transcripts with proper formatting and grammar
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Transform your voice memos in 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Upload Audio</h3>
              <p className="text-sm text-muted-foreground">
                Upload your voice memo file and select the recording scenario and cleaning level
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">AI Processing</h3>
              <p className="text-sm text-muted-foreground">
                Our AI transcribes your audio and intelligently removes filler words and errors
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Get Clean Transcript</h3>
              <p className="text-sm text-muted-foreground">
                Download or copy your polished transcript ready for professional use
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Every Scenario
            </h2>
            <p className="text-xl text-muted-foreground">
              From business meetings to creative brainstorming, clean up any voice recording
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏢</span>
                </div>
                <h3 className="font-semibold mb-2">Business Meetings</h3>
                <p className="text-sm text-muted-foreground">
                  Clean up meeting recordings for professional documentation and sharing
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💡</span>
                </div>
                <h3 className="font-semibold mb-2">Creative Ideas</h3>
                <p className="text-sm text-muted-foreground">
                  Transform brainstorming sessions into organized, readable content
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎓</span>
                </div>
                <h3 className="font-semibold mb-2">Educational Content</h3>
                <p className="text-sm text-muted-foreground">
                  Convert lecture recordings into clean study notes and materials
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎙️</span>
                </div>
                <h3 className="font-semibold mb-2">Podcast Production</h3>
                <p className="text-sm text-muted-foreground">
                  Polish rough podcast recordings for professional transcription
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our AI voice memo cleaner
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "What audio formats are supported?",
                  answer: "We support MP3, WAV, M4A, OGG, and WebM audio formats. Files must be under 25MB in size."
                },
                {
                  question: "How accurate is the speech recognition?",
                  answer: "Our AI uses advanced speech recognition technology with high accuracy rates. Quality depends on audio clarity, background noise, and speaker pronunciation."
                },
                {
                  question: "What languages are supported?",
                  answer: "Our speech recognition model supports multiple languages including English, Chinese, Japanese, Korean, and many others."
                },
                {
                  question: "Is my audio data secure?",
                  answer: "Yes, we prioritize your privacy. Audio files are processed securely and are not stored on our servers after processing is complete."
                },
                {
                  question: "What's the difference between cleaning levels?",
                  answer: "Light removes basic filler words (um, uh, like). Moderate removes more fillers and repetitions. Thorough removes all filler words and improves grammar. Professional creates business-ready transcripts with comprehensive cleanup."
                },
                {
                  question: "What filler words and phrases are removed?",
                  answer: "We remove hesitation sounds (um, uh, er), common fillers (like, you know, kind of), repetitive endings (or whatever, and stuff), and word repetitions. The cleaning level determines how comprehensive the removal is."
                },
                {
                  question: "Can I edit the transcript after cleaning?",
                  answer: "Yes, you can copy the cleaned transcript and edit it further in any text editor or word processor as needed."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
