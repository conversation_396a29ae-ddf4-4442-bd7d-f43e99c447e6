'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Heart, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, PenTool, MessageCircle, Users, Star } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 关系阶段选项
const RELATIONSHIP_STAGES = [
  { id: 'crush', name: 'Secret Crush', description: 'Hidden feelings, first confession, nervous energy' },
  { id: 'flirting', name: 'Flirting Stage', description: 'Playful banter, testing waters, building tension' },
  { id: 'dating', name: 'Early Dating', description: 'New relationship, getting to know each other' },
  { id: 'long_distance', name: 'Long Distance', description: 'Miles apart, missing each other, staying connected' },
  { id: 'anniversary', name: 'Anniversary', description: 'Celebrating milestones, reflecting on journey together' },
  { id: 'apology', name: 'Making Up', description: 'After a fight, seeking forgiveness, rebuilding trust' },
  { id: 'proposal', name: 'Proposal Prep', description: 'Ready for next level, marriage thoughts, future planning' },
  { id: 'long_term', name: 'Long-term Love', description: 'Years together, deep connection, comfortable intimacy' },
  { id: 'rekindling', name: 'Rekindling Romance', description: 'Reigniting spark, bringing back excitement' },
  { id: 'custom', name: 'Custom Situation', description: 'Define your unique relationship context' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, emotional expression, 26 languages'
  },
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Thinking mode, reasoning, emotional analysis'
  }
]

interface GeneratedLoveLetter {
  letter: string
  punchline: string
  wordCount: number
  tone: string
  explanation: string
}

export function AntiClicheLoveLetterClient() {
  // 表单状态
  const [relationshipStage, setRelationshipStage] = useState('')
  const [customStage, setCustomStage] = useState('')
  const [quirkyHabit, setQuirkyHabit] = useState('')
  const [sharedMemory, setSharedMemory] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLetter, setGeneratedLetter] = useState<GeneratedLoveLetter | null>(null)
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成情书
  const handleGenerate = async () => {
    if (!relationshipStage || !quirkyHabit.trim() || !sharedMemory.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/anti-cliche-love-letter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          relationshipStage: relationshipStage === 'custom' ? customStage : relationshipStage,
          quirkyHabit: quirkyHabit.trim(),
          sharedMemory: sharedMemory.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate love letter')
      }

      const data = await response.json()
      setGeneratedLetter(data.loveLetter)

      toast({
        title: "Love Letter Generated!",
        description: "Your personalized anti-cliche confession is ready.",
      })

    } catch (error) {
      setError('Failed to generate love letter. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制情书到剪贴板
  const copyLetter = async () => {
    if (!generatedLetter) return
    
    try {
      const fullLetter = `${generatedLetter.letter}\n\n${generatedLetter.punchline}`
      await navigator.clipboard.writeText(fullLetter)
      toast({
        title: "Copied!",
        description: "Love letter copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950 dark:to-rose-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                AI Anti-Cliche Love Letter
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Unique Confessions
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>personalized love letters that avoid cliches</strong>. 
              Create unique confessions with anti-routine punchlines based on your shared memories.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                100 Words
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Anti-Cliche
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Handwriting Ready
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-primary" />
                      Love Letter Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Relationship Stage */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Relationship Stage *</label>
                      <Select value={relationshipStage} onValueChange={setRelationshipStage}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your relationship context" />
                        </SelectTrigger>
                        <SelectContent>
                          {RELATIONSHIP_STAGES.map((stage) => (
                            <SelectItem key={stage.id} value={stage.id}>
                              <div>
                                <div className="font-medium">{stage.name}</div>
                                <div className="text-xs text-muted-foreground">{stage.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Stage Input */}
                    {relationshipStage === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Situation *</label>
                        <input
                          type="text"
                          placeholder="Describe your unique relationship context"
                          value={customStage}
                          onChange={(e) => setCustomStage(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Quirky Habit */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Their Quirky Habit *</label>
                      <Textarea
                        placeholder="Describe one small, endearing thing they do that makes you smile..."
                        value={quirkyHabit}
                        onChange={(e) => setQuirkyHabit(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Something specific and personal that only you would notice
                      </p>
                    </div>

                    {/* Shared Memory */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Shared Memory *</label>
                      <Textarea
                        placeholder="Describe a special moment you both experienced together..."
                        value={sharedMemory}
                        onChange={(e) => setSharedMemory(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        A meaningful experience that connects you both
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Writing Love Letter...
                        </>
                      ) : (
                        <>
                          <Heart className="h-4 w-4 mr-2" />
                          Generate Love Letter
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Your Love Letter
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedLetter ? (
                      <div className="space-y-6">
                        {/* Love Letter Display */}
                        <div>
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                              <PenTool className="h-4 w-4" />
                              <span className="font-medium text-sm">Love Letter</span>
                              <Badge variant="outline" className="text-xs">
                                {generatedLetter.wordCount} words
                              </Badge>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={copyLetter}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          
                          <div className="bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-950 dark:to-rose-950 p-6 rounded-lg border-2 border-dashed border-pink-200 dark:border-pink-800">
                            <div className="font-serif text-base leading-relaxed mb-4">
                              {generatedLetter.letter}
                            </div>
                            
                            {/* Punchline */}
                            <div className="border-t border-pink-200 dark:border-pink-800 pt-4">
                              <div className="flex items-center gap-2 mb-2">
                                <Star className="h-4 w-4 text-pink-600" />
                                <span className="text-sm font-medium text-pink-600">Anti-Cliche Punchline</span>
                              </div>
                              <div className="font-serif text-base italic text-pink-700 dark:text-pink-300">
                                {generatedLetter.punchline}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Letter Analysis */}
                        <div className="bg-muted/50 p-4 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <MessageCircle className="h-4 w-4" />
                            <span className="font-medium text-sm">Letter Analysis</span>
                          </div>
                          <div className="space-y-2 text-sm text-muted-foreground">
                            <div><strong>Tone:</strong> {generatedLetter.tone}</div>
                            <div><strong>Strategy:</strong> {generatedLetter.explanation}</div>
                          </div>
                        </div>

                        {/* Handwriting Tips */}
                        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <PenTool className="h-4 w-4 text-blue-600" />
                            <span className="font-medium text-sm text-blue-600">Handwriting Tips</span>
                          </div>
                          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Use quality paper and a smooth pen for best results</li>
                            <li>• Write slowly and deliberately for emotional impact</li>
                            <li>• Consider adding small doodles or hearts in margins</li>
                            <li>• Fold it nicely and present it at the right moment</li>
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Heart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your personalized love letter will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Anti-Cliche Love Letters Work
            </h2>
            <p className="text-xl text-muted-foreground">
              Skip the roses and diamonds for something that actually gets a response
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-pink-600" />
                </div>
                <h3 className="font-semibold mb-2">Personalized Details</h3>
                <p className="text-sm text-muted-foreground">
                  References to specific habits and memories that only you would notice
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-rose-100 dark:bg-rose-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageCircle className="h-6 w-6 text-rose-600" />
                </div>
                <h3 className="font-semibold mb-2">Authentic Voice</h3>
                <p className="text-sm text-muted-foreground">
                  Sounds like a real person, not a generic greeting card or AI
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Star className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Unexpected Punchline</h3>
                <p className="text-sm text-muted-foreground">
                  Ends with a clever twist that's memorable and makes them smile
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <PenTool className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Handwriting Ready</h3>
                <p className="text-sm text-muted-foreground">
                  Perfect 100-word length for handwritten notes on special paper
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Relationship Specific</h3>
                <p className="text-sm text-muted-foreground">
                  Tailored to your exact relationship stage, from crush to anniversary
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Response</h3>
                <p className="text-sm text-muted-foreground">
                  Creates the perfect balance of vulnerability and wit that gets replies
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create your personalized love letter in 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-pink-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Relationship Stage</h3>
              <p className="text-sm text-muted-foreground">
                Select from crush, flirting, dating, long distance, anniversary, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-rose-100 dark:bg-rose-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-rose-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Add Personal Details</h3>
              <p className="text-sm text-muted-foreground">
                Describe their quirky habit and a special memory you share together
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-red-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Get Your Letter</h3>
              <p className="text-sm text-muted-foreground">
                Receive a 100-word personalized letter with anti-cliche punchline
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Love Letter Examples
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different relationship stages create unique confessions
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💘</span>
                  Secret Crush
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-pink-50 dark:bg-pink-950 p-4 rounded-lg mb-4">
                  <div className="font-serif text-sm leading-relaxed mb-3">
                    I've noticed how you tap your pen three times before writing anything important. It's become the rhythm my heart follows when I see you. Remember when we got caught in that sudden rain after class? You offered me your jacket, even though you were shivering. I kept it longer than necessary, not because I was cold, but because it smelled like your sandalwood cologne.
                  </div>
                  <div className="font-serif text-sm italic text-pink-700 dark:text-pink-300">
                    I'm not saying I believe in fate, but I did check the weather forecast that day, and it said sunny.
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Perfect for first confessions with subtle humor and specific observations
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">✈️</span>
                  Long Distance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg mb-4">
                  <div className="font-serif text-sm leading-relaxed mb-3">
                    The way you always organize your desk before our video calls makes me smile. As if I'd judge your mess when mine is equally chaotic. I keep thinking about that sunset at the beach last summer, how you insisted we stay until the very last ray disappeared. 2,749 miles between us, yet somehow I feel your presence in the smallest things.
                  </div>
                  <div className="font-serif text-sm italic text-blue-700 dark:text-blue-300">
                    I've started talking to my houseplants about you. They're terrible at keeping secrets, but excellent listeners.
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Great for maintaining connection across distance with warmth and humor
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🎂</span>
                  Anniversary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg mb-4">
                  <div className="font-serif text-sm leading-relaxed mb-3">
                    Five years, and I still catch myself watching you fold laundry with that intense concentration, as if solving a complex puzzle. It's the same look you had when we got lost hiking and you insisted we weren't lost, just "exploring alternative routes." Your stubborn optimism has become my north star through our hardest days.
                  </div>
                  <div className="font-serif text-sm italic text-purple-700 dark:text-purple-300">
                    Here's to five more years of being gloriously lost together—I've hidden the map just in case.
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Perfect for celebrating milestones with depth and playful references
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🔥</span>
                  Rekindling Romance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg mb-4">
                  <div className="font-serif text-sm leading-relaxed mb-3">
                    I've been thinking about how you still can't make coffee without spilling at least three beans on the counter. It's endearing, especially now. Remember our first apartment, when we stayed up all night assembling that impossible IKEA bed? We were exhausted, frustrated, but laughing until 3 AM. I miss that laughter.
                  </div>
                  <div className="font-serif text-sm italic text-orange-700 dark:text-orange-300">
                    I bought an unnecessarily complicated coffee maker yesterday. Consider this your official invitation to come spill beans on my counter again.
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Ideal for reconnecting with nostalgia and gentle invitation
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about creating anti-cliche love letters
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "What makes a love letter 'anti-cliche'?",
                  answer: "Anti-cliche love letters avoid generic phrases like 'you complete me' or 'love at first sight' and instead focus on specific, personal details that only you would notice about the person. They use unexpected metaphors, reference modern life, include imperfections, and end with surprising but fitting punchlines."
                },
                {
                  question: "Why are the letters limited to 100 words?",
                  answer: "The 100-word format forces clarity and precision, making every word count. It's also the perfect length for handwriting on nice stationery or a card. Research shows that shorter, more specific love notes often have more impact than lengthy generic ones."
                },
                {
                  question: "Can I edit the generated letter?",
                  answer: "Absolutely! The generated letter is a starting point. Feel free to adjust the wording, add more personal details, or modify the tone to match your authentic voice. The most effective love letters combine AI creativity with your personal touch."
                },
                {
                  question: "How should I deliver the letter?",
                  answer: "Handwritten letters on quality paper make the strongest impression. Consider the timing and context—leave it somewhere they'll find it unexpectedly, pair it with a small meaningful gift (not roses!), or read it aloud in a private moment for maximum impact."
                },
                {
                  question: "Will this actually get a response?",
                  answer: "While we can't guarantee specific reactions, personalized anti-cliche letters typically receive much better responses than generic romantic gestures. The specificity shows you truly pay attention to them, which is often more meaningful than grand declarations."
                },
                {
                  question: "Is this appropriate for all relationship stages?",
                  answer: "Yes! The generator adapts to your specific relationship stage. For early stages like crushes, it creates more subtle, lighter content. For established relationships, it draws on deeper connection and shared history. Each stage has its own appropriate tone and approach."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
