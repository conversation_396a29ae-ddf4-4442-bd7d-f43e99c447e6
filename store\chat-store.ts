import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { generateId } from '@/lib/utils'
import { DEFAULT_SETTINGS, STORAGE_KEYS } from '@/lib/constants'
import type {
  Message,
  Conversation,
  ChatSettings
} from '@/types'

// 聊天状态接口
interface ChatState {
  // 当前对话
  currentConversation: Conversation | null
  conversations: Conversation[]
  
  // 聊天设置
  settings: ChatSettings
  
  // UI状态
  isLoading: boolean
  isStreaming: boolean
  streamingContent: string
  
  // 错误状态
  error: string | null
  
  // Actions
  createConversation: () => string
  setCurrentConversation: (id: string | null) => void
  addMessage: (message: Omit<Message, 'id'>) => void
  updateMessage: (id: string, updates: Partial<Message>) => void
  deleteMessage: (id: string) => void
  clearCurrentConversation: () => void
  deleteConversation: (id: string) => void
  
  // 设置相关
  updateSettings: (settings: Partial<ChatSettings>) => void
  resetSettings: () => void
  
  // 流式状态
  setLoading: (loading: boolean) => void
  setStreaming: (streaming: boolean) => void
  setStreamingContent: (content: string) => void
  
  // 错误处理
  setError: (error: string | null) => void
  clearError: () => void
  
  // 导入导出
  exportConversations: () => string
  importConversations: (data: string) => boolean
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentConversation: null,
      conversations: [],
      settings: DEFAULT_SETTINGS,
      isLoading: false,
      isStreaming: false,
      streamingContent: '',
      error: null,

      // 对话管理
      createConversation: () => {
        const id = generateId()
        const newConversation: Conversation = {
          id,
          title: '新对话',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          settings: get().settings,
          metadata: {
            totalTokens: 0,
            totalCost: 0,
            messageCount: 0,
            averageResponseTime: 0
          }
        }
        
        set(state => ({
          conversations: [newConversation, ...state.conversations],
          currentConversation: newConversation
        }))
        
        return id
      },

      setCurrentConversation: (id) => {
        if (!id) {
          set({ currentConversation: null })
          return
        }
        
        const conversation = get().conversations.find(c => c.id === id)
        if (conversation) {
          set({ currentConversation: conversation })
        }
      },

      addMessage: (messageData) => {
        const message: Message = {
          ...messageData,
          id: generateId(),
          timestamp: new Date()
        }
        
        set(state => {
          if (!state.currentConversation) {
            // 如果没有当前对话，创建一个新的
            const conversationId = get().createConversation()
            const newConversation = state.conversations.find(c => c.id === conversationId)!
            
            return {
              currentConversation: {
                ...newConversation,
                messages: [message],
                title: message.role === 'user' 
                  ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
                  : newConversation.title,
                updatedAt: new Date(),
                metadata: {
                  ...newConversation.metadata,
                  messageCount: 1
                }
              },
              conversations: state.conversations.map(c => 
                c.id === conversationId 
                  ? {
                      ...c,
                      messages: [message],
                      title: message.role === 'user' 
                        ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
                        : c.title,
                      updatedAt: new Date(),
                      metadata: {
                        ...c.metadata,
                        messageCount: 1
                      }
                    }
                  : c
              )
            }
          }
          
          // 更新现有对话
          const updatedConversation = {
            ...state.currentConversation,
            messages: [...state.currentConversation.messages, message],
            updatedAt: new Date(),
            metadata: {
              ...state.currentConversation.metadata,
              messageCount: state.currentConversation.messages.length + 1
            }
          }
          
          // 如果是第一条用户消息，更新标题
          if (message.role === 'user' && state.currentConversation.messages.length === 0) {
            updatedConversation.title = message.content.slice(0, 50) + 
              (message.content.length > 50 ? '...' : '')
          }
          
          return {
            currentConversation: updatedConversation,
            conversations: state.conversations.map(c => 
              c.id === state.currentConversation!.id ? updatedConversation : c
            )
          }
        })
      },

      updateMessage: (id, updates) => {
        set(state => {
          if (!state.currentConversation) return state
          
          const updatedMessages = state.currentConversation.messages.map(msg =>
            msg.id === id ? { ...msg, ...updates } : msg
          )
          
          const updatedConversation = {
            ...state.currentConversation,
            messages: updatedMessages,
            updatedAt: new Date()
          }
          
          return {
            currentConversation: updatedConversation,
            conversations: state.conversations.map(c => 
              c.id === state.currentConversation!.id ? updatedConversation : c
            )
          }
        })
      },

      deleteMessage: (id) => {
        set(state => {
          if (!state.currentConversation) return state
          
          const updatedMessages = state.currentConversation.messages.filter(msg => msg.id !== id)
          const updatedConversation = {
            ...state.currentConversation,
            messages: updatedMessages,
            updatedAt: new Date(),
            metadata: {
              ...state.currentConversation.metadata,
              messageCount: updatedMessages.length
            }
          }
          
          return {
            currentConversation: updatedConversation,
            conversations: state.conversations.map(c => 
              c.id === state.currentConversation!.id ? updatedConversation : c
            )
          }
        })
      },

      clearCurrentConversation: () => {
        set(state => {
          if (!state.currentConversation) return state
          
          const clearedConversation = {
            ...state.currentConversation,
            messages: [],
            title: '新对话',
            updatedAt: new Date(),
            metadata: {
              totalTokens: 0,
              totalCost: 0,
              messageCount: 0,
              averageResponseTime: 0
            }
          }
          
          return {
            currentConversation: clearedConversation,
            conversations: state.conversations.map(c => 
              c.id === state.currentConversation!.id ? clearedConversation : c
            )
          }
        })
      },

      deleteConversation: (id) => {
        set(state => ({
          conversations: state.conversations.filter(c => c.id !== id),
          currentConversation: state.currentConversation?.id === id 
            ? null 
            : state.currentConversation
        }))
      },

      // 设置管理
      updateSettings: (newSettings) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      resetSettings: () => {
        set({ settings: DEFAULT_SETTINGS })
      },

      // 状态管理
      setLoading: (loading) => set({ isLoading: loading }),
      setStreaming: (streaming) => set({ isStreaming: streaming }),
      setStreamingContent: (content) => set({ streamingContent: content }),

      // 错误处理
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // 导入导出
      exportConversations: () => {
        const state = get()
        const exportData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          conversations: state.conversations,
          settings: state.settings
        }
        return JSON.stringify(exportData, null, 2)
      },

      importConversations: (data) => {
        try {
          const parsed = JSON.parse(data)
          
          if (!parsed.conversations || !Array.isArray(parsed.conversations)) {
            return false
          }
          
          set(state => ({
            conversations: [...parsed.conversations, ...state.conversations],
            settings: parsed.settings || state.settings
          }))
          
          return true
        } catch {
          return false
        }
      }
    }),
    {
      name: STORAGE_KEYS.chatHistory,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        conversations: state.conversations,
        settings: state.settings
      })
    }
  )
)
