// Creem Webhook事件类型定义

export interface CreemWebhookEvent {
  id: string
  type: string
  created: number
  data: {
    object: CreemSubscription | CreemCustomer | CreemOrder
  }
}

export interface CreemSubscription {
  id: string
  customer: string
  product: string
  status: 'active' | 'canceled' | 'past_due' | 'trialing' | 'incomplete'
  current_period_start: number
  current_period_end: number
  cancel_at_period_end: boolean
  canceled_at?: number
  created: number
  metadata?: Record<string, any>
}

export interface CreemCustomer {
  id: string
  email: string
  created: number
  metadata?: Record<string, any>
}

export interface CreemOrder {
  id: string
  customer: string
  product: string
  amount: number
  currency: string
  status: 'pending' | 'paid' | 'failed' | 'canceled'
  created: number
  metadata?: Record<string, any>
}

// Webhook事件类型
export type WebhookEventType = 
  | 'subscription.created'
  | 'subscription.updated' 
  | 'subscription.renewed'
  | 'subscription.canceled'
  | 'subscription.payment_failed'
  | 'customer.created'
  | 'customer.updated'
  | 'order.created'
  | 'order.paid'
  | 'order.failed'

// 处理结果
export interface WebhookProcessResult {
  success: boolean
  message: string
  processed: boolean // 是否实际处理了事件（用于幂等性）
}

// 事件处理上下文
export interface WebhookContext {
  eventId: string
  eventType: WebhookEventType
  timestamp: number
  userId?: string
}
