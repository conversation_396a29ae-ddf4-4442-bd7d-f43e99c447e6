'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Send, Square, Paperclip, Coins, X } from 'lucide-react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { ModelSelector } from './ModelSelector'
import { cn } from '@/lib/utils'
import { toast } from '@/hooks/use-toast'
import type { AIModel } from '@/types'

interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: (message: string, images?: string[]) => void  // 修改：支持图像数据
  onStop?: () => void
  disabled?: boolean
  isLoading?: boolean
  isStreaming?: boolean
  placeholder?: string
  maxLength?: number
  className?: string
  currentModel: AIModel
  onModelChange: (model: AIModel) => void
  userCredits?: { credits: number; usedCredits: number } | null  // 新增：从父组件传入积分
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onStop,
  disabled = false,
  isLoading = false,
  isStreaming = false,
  placeholder = 'Type your message...',
  maxLength = 2000,
  className,
  currentModel,
  onModelChange,
  userCredits: propUserCredits
}: ChatInputProps) {
  const { user } = useUser()
  const [isFocused, setIsFocused] = useState(false)
  const [userCredits, setUserCredits] = useState<{ credits: number; usedCredits: number } | null>(null)
  const [uploadedImages, setUploadedImages] = useState<Array<{ file: File; preview: string }>>([])
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 监听全局事件，自动填入问题
  useEffect(() => {
    const handleFillQuestion = (event: CustomEvent) => {
      const question = event.detail?.question
      if (question && typeof question === 'string') {
        onChange(question)
        // 聚焦到输入框
        setTimeout(() => {
          textareaRef.current?.focus()
        }, 100)
      }
    }

    window.addEventListener('fillQuestion', handleFillQuestion as EventListener)
    return () => {
      window.removeEventListener('fillQuestion', handleFillQuestion as EventListener)
    }
  }, [onChange])

  // 获取用户积分
  const fetchUserCredits = async () => {
    if (!user?.id) return

    try {
      const response = await fetch('/api/user/metadata')
      if (response.ok) {
        const data = await response.json()
        setUserCredits({
          credits: data.credits || 0,
          usedCredits: data.usedCredits || 0
        })
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error)
    }
  }

  useEffect(() => {
    // 如果没有从父组件传入积分，则自己获取
    if (!propUserCredits) {
      fetchUserCredits()
    }
  }, [user?.id, propUserCredits])

  // 使用传入的积分或本地获取的积分
  const displayCredits = propUserCredits || userCredits

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter 换行
        return
      } else {
        // Enter 发送
        e.preventDefault()
        handleSend()
      }
    }
  }

  // 发送消息
  const handleSend = async () => {
    const trimmedValue = value.trim()
    if ((!trimmedValue && uploadedImages.length === 0) || disabled || isLoading) return

    // 如果有图片，需要转换为base64
    const imageData = await Promise.all(
      uploadedImages.map(async (image) => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(image.file)
        })
      })
    )

    // 发送消息和图片数据
    onSend(trimmedValue, imageData)
    setUploadedImages([]) // 清空上传的图片
  }

  // 停止生成
  const handleStop = () => {
    if (onStop) {
      onStop()
    }
  }

  // 处理图像上传
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid File Type",
          description: "Please select an image file",
          variant: "destructive"
        })
        return
      }

      // 检查文件大小 (限制为 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Image size cannot exceed 10MB",
          variant: "destructive"
        })
        return
      }

      // 创建预览
      const reader = new FileReader()
      reader.onload = (e) => {
        const preview = e.target?.result as string
        setUploadedImages(prev => [...prev, { file, preview }])
      }
      reader.readAsDataURL(file)
    })

    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }

  // 删除上传的图片
  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  // 触发文件选择
  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  // 计算字符数和进度
  const characterCount = value.length
  const isNearLimit = characterCount > maxLength * 0.8
  const isOverLimit = characterCount > maxLength

  return (
    <div className={cn('space-y-3', className)}>
      {/* 输入框容器 */}
      <div className={cn(
        'relative rounded-lg border transition-all duration-200',
        isFocused ? 'border-primary ring-2 ring-primary/20' : 'border-input',
        disabled && 'opacity-50 cursor-not-allowed'
      )}>
        <Textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          maxLength={maxLength}
          className="min-h-[60px] max-h-[200px] resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 pr-24"
          autoResize
        />
        
        {/* 右侧按钮组 */}
        <div className="absolute bottom-2 right-2 flex items-center space-x-1">
          {/* 图片上传按钮 */}
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={triggerFileSelect}
            disabled={disabled}
            className="h-8 w-8"
            title="Upload Images"
          >
            <Paperclip className="h-4 w-4" />
          </Button>

          {/* 发送/停止按钮 */}
          {isStreaming ? (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              onClick={handleStop}
              className="h-8 w-8"
              title="Stop Generation"
            >
              <Square className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              type="button"
              variant="default"
              size="icon"
              onClick={handleSend}
              disabled={disabled || !value.trim() || isOverLimit}
              className="h-8 w-8"
              title="Send Message (Enter)"
            >
              <Send className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* 底部信息栏 */}
      <div className="flex items-center justify-between text-xs text-muted-foreground">
        <div className="flex items-center space-x-4">
          {/* 模型选择器 */}
          <ModelSelector
            currentModel={currentModel}
            onModelChange={onModelChange}
            disabled={disabled}
            size="sm"
          />

          {/* 快捷键提示 */}
          <span>
            <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd> Send
          </span>
          <span>
            <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Shift</kbd> +
            <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs ml-1">Enter</kbd> New Line
          </span>

          {/* 用户积分显示 */}
          {displayCredits && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Coins className="h-3 w-3 text-yellow-500" />
              <span>{displayCredits.credits - displayCredits.usedCredits}</span>
            </div>
          )}

          {/* 上传的图片预览 */}
          {uploadedImages.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {uploadedImages.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={image.preview}
                    alt={`Upload ${index + 1}`}
                    className="w-16 h-16 object-cover rounded border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute -top-2 -right-2 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => removeImage(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 字符计数 */}
        <div className={cn(
          'flex items-center space-x-2',
          isNearLimit && 'text-orange-500',
          isOverLimit && 'text-destructive'
        )}>
          <span>
            {characterCount}/{maxLength}
          </span>
          {isOverLimit && (
            <Badge variant="destructive" className="text-xs">
              Over Limit
            </Badge>
          )}
        </div>
      </div>

      {/* 加载状态指示器 */}
      {isLoading && !isStreaming && (
        <div className="flex items-center justify-center py-2">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <div className="loading-dots">
              <div></div>
              <div></div>
              <div></div>
            </div>
            <span>HumanWhisper is thinking...</span>
          </div>
        </div>
      )}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  )
}
