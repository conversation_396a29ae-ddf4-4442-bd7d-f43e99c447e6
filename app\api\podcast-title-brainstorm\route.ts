import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 播客类型映射
const PODCAST_TYPE_MAP: Record<string, string> = {
  'tech': 'technology, programming, AI, startups, and tech innovation',
  'business': 'business strategy, entrepreneurship, leadership, and startup stories',
  'storytelling': 'personal narratives, true stories, fiction, and compelling storytelling',
  'self_improvement': 'personal development, productivity, wellness, and self-help',
  'education': 'learning, tutorials, academic topics, and educational content',
  'entertainment': 'comedy, pop culture, celebrity interviews, and entertainment',
  'news': 'current events, political analysis, journalism, and news commentary',
  'health': 'fitness, nutrition, mental health, and medical topics',
  'finance': 'personal finance, investing, economics, and financial literacy'
}

// 构建播客标题生成prompt
function buildTitlePrompt(
  podcastType: string,
  contentSummary: string,
  targetAudience: string
): string {
  const typeDesc = PODCAST_TYPE_MAP[podcastType] || podcastType

  const prompt = `You are a podcast marketing expert specializing in creating compelling episode titles that drive clicks and improve discoverability. Your task is to generate engaging, SEO-optimized podcast episode titles.

**Podcast Context:**
- Genre: ${typeDesc}
- Episode Content: ${contentSummary}
- Target Audience: ${targetAudience}

**Instructions:**
Generate exactly 8 unique podcast episode titles that:

1. **Click-Worthy**: Use psychological triggers that make people want to listen
2. **SEO Optimized**: Include relevant keywords for podcast discovery
3. **Genre-Appropriate**: Match the tone and style of ${typeDesc} podcasts
4. **Audience-Targeted**: Appeal specifically to ${targetAudience}
5. **Descriptive**: Clearly indicate what listeners will learn or experience
6. **Memorable**: Easy to remember and share

**Title Optimization Techniques:**
- Use numbers, questions, or "How to" formats when appropriate
- Include power words that create urgency or curiosity
- Keep titles between 40-60 characters for optimal display
- Include emotional hooks and benefit-driven language
- Use keywords that your target audience searches for

**Output Format:**
Return a valid JSON array with exactly 8 objects:

[
  {
    "title": "Your compelling episode title here",
    "explanation": "Brief explanation of why this title works and its strategy",
    "seoKeywords": ["keyword1", "keyword2", "keyword3"],
    "clickFactors": ["curiosity", "benefit", "urgency"]
  }
]

**Click Factor Categories:**
- curiosity: Creates intrigue and mystery
- benefit: Promises clear value or learning
- urgency: Suggests timely or important information
- controversy: Presents contrarian or surprising viewpoints
- social_proof: Implies popularity or expert endorsement
- specificity: Uses specific numbers, data, or examples
- emotion: Triggers emotional responses

**Important Guidelines:**
- Each title should be unique in approach and angle
- Ensure JSON format is valid and properly escaped
- Include 3-5 relevant SEO keywords per title
- Identify 2-4 click factors per title
- Make titles authentic to the content described
- Avoid clickbait that doesn't deliver on promises

Generate the 8 podcast episode titles now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      podcastType,
      contentSummary,
      targetAudience,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!podcastType || !contentSummary?.trim() || !targetAudience?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildTitlePrompt(
      podcastType,
      contentSummary.trim(),
      targetAudience.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let titles: Array<{title: string, explanation: string, seoKeywords: string[], clickFactors: string[]}>
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      titles = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!Array.isArray(titles)) {
        throw new Error('Response is not an array')
      }
      
      // 验证每个标题项目
      titles = titles.filter(title => 
        title && 
        typeof title.title === 'string' && 
        typeof title.explanation === 'string' &&
        Array.isArray(title.seoKeywords) &&
        Array.isArray(title.clickFactors) &&
        title.title.trim() && 
        title.explanation.trim()
      )
      
      if (titles.length === 0) {
        throw new Error('No valid title items found')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取标题
      const lines = generatedContent.split('\n').filter(line => line.trim())
      titles = []
      
      for (const line of lines) {
        if (line.includes('"') && line.length > 20 && line.length < 100) {
          // 尝试提取标题
          const titleMatch = line.match(/"([^"]+)"/g)
          if (titleMatch && titleMatch.length >= 1) {
            const cleanTitle = titleMatch[0].replace(/"/g, '')
            if (cleanTitle.length > 10) {
              titles.push({
                title: cleanTitle,
                explanation: 'Engaging podcast title designed to attract listeners',
                seoKeywords: [targetAudience, podcastType, 'podcast'],
                clickFactors: ['curiosity', 'benefit']
              })
            }
          }
        }
      }
      
      if (titles.length === 0) {
        throw new Error('Failed to parse title content')
      }
    }

    return NextResponse.json({
      titles: titles.slice(0, 8), // 确保不超过8个
      metadata: {
        podcastType,
        targetAudience,
        titleCount: titles.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate podcast titles' },
      { status: 500 }
    )
  }
}
