import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { creem } from '@/lib/creem-client'
import { processWebhookEvent } from '@/lib/webhook-handlers'
import type { CreemWebhookEvent } from '@/types/webhook'

/**
 * 验证webhook签名
 */
async function verifyWebhookSignature(
  payload: string,
  signature: string
): Promise<boolean> {
  try {
    // 使用Creem客户端的签名验证方法
    return creem.verifyWebhookSignature(payload, signature)
  } catch (error) {
    return false
  }
}

/**
 * 处理Creem webhook事件
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求头
    const headersList = await headers()
    const signature = headersList.get('creem-signature') || headersList.get('x-creem-signature')
    
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 400 }
      )
    }

    // 获取原始请求体
    const payload = await request.text()
    
    if (!payload) {
      return NextResponse.json(
        { error: 'Empty request body' },
        { status: 400 }
      )
    }

    // 验证webhook签名
    const webhookSecret = process.env.CREEM_WEBHOOK_SECRET
    if (!webhookSecret) {
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    const isValidSignature = await verifyWebhookSignature(payload, signature)
    if (!isValidSignature) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    // 解析webhook事件
    let event: CreemWebhookEvent
    try {
      event = JSON.parse(payload)
    } catch (error) {
      console.error('Failed to parse webhook payload:', error)
      return NextResponse.json(
        { error: 'Invalid JSON payload' },
        { status: 400 }
      )
    }

    // 验证事件结构
    if (!event.id || !event.type || !event.data) {
      console.error('Invalid webhook event structure:', event)
      return NextResponse.json(
        { error: 'Invalid event structure' },
        { status: 400 }
      )
    }

    console.log(`Processing webhook event: ${event.type} (${event.id})`)

    // 处理webhook事件
    const result = await processWebhookEvent(event)

    if (result.success) {
      console.log(`Webhook processed successfully: ${result.message}`)
      return NextResponse.json({
        success: true,
        message: result.message,
        processed: result.processed
      })
    } else {
      console.error(`Webhook processing failed: ${result.message}`)
      return NextResponse.json(
        { 
          error: result.message,
          processed: result.processed
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

/**
 * 处理GET请求（用于webhook端点验证）
 */
export async function GET() {
  return NextResponse.json({
    message: 'Creem webhook endpoint is active',
    timestamp: new Date().toISOString()
  })
}
