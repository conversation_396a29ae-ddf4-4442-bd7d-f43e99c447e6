'use client'

import React from 'react'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Eye } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useSettingsStore } from '@/store/settings-store'
import { useTheme } from '@/components/providers/ThemeProvider'
import { THEMES } from '@/lib/constants'
import type { Theme } from '@/types'

export function ThemeSettings() {
  const { 
    theme, 
    fontSize, 
    setTheme, 
    setFontSize,
    sidebarCollapsed,
    setSidebarCollapsed,
    showWelcomeMessage,
    setShowWelcomeMessage
  } = useSettingsStore()
  
  const { resolvedTheme } = useTheme()

  const themeOptions = [
    {
      value: THEMES.LIGHT,
      label: 'Light Mode',
      description: 'Classic light interface',
      icon: Sun
    },
    {
      value: THEMES.DARK,
      label: 'Dark Mode',
      description: 'Eye-friendly dark interface',
      icon: Moon
    },
    {
      value: THEMES.SYSTEM,
      label: 'Follow System',
      description: 'Automatically follow system settings',
      icon: Monitor
    }
  ]

  const fontSizeOptions = [
    { value: 'small', label: 'Small', description: '14px' },
    { value: 'medium', label: 'Medium', description: '16px' },
    { value: 'large', label: 'Large', description: '18px' }
  ]

  return (
    <div className="space-y-6">
      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Palette className="h-5 w-5 text-blue-500" />
            <CardTitle>Theme Appearance</CardTitle>
          </div>
          <CardDescription>
            Choose your preferred interface theme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {themeOptions.map((option) => {
              const Icon = option.icon
              const isSelected = theme === option.value
              
              return (
                <div
                  key={option.value}
                  className={`relative flex items-center space-x-3 rounded-lg border p-4 cursor-pointer transition-all hover:bg-muted/50 ${
                    isSelected ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => setTheme(option.value as Theme)}
                >
                  <div className={`flex h-10 w-10 items-center justify-center rounded-full ${
                    isSelected ? 'bg-primary text-primary-foreground' : 'bg-muted'
                  }`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium">{option.label}</h3>
                      {isSelected && (
                        <div className="h-2 w-2 rounded-full bg-primary" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {option.description}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Current Theme Preview */}
          <div className="mt-4 p-4 rounded-lg border bg-card">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Current Theme Preview</span>
              <span className="text-xs text-muted-foreground">
                {resolvedTheme === 'dark' ? 'Dark Mode' : 'Light Mode'}
              </span>
            </div>
            <div className="space-y-2">
              <div className="h-2 bg-primary rounded-full w-3/4" />
              <div className="h-2 bg-muted rounded-full w-1/2" />
              <div className="h-2 bg-muted rounded-full w-2/3" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Font Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Type className="h-5 w-5 text-green-500" />
            <CardTitle>Font Size</CardTitle>
          </div>
          <CardDescription>
            Adjust the size of interface text
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={fontSize} onValueChange={(value: any) => setFontSize(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontSizeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center justify-between w-full">
                    <span>{option.label}</span>
                    <span className="text-muted-foreground ml-2">
                      {option.description}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* Font Preview */}
          <div className="mt-4 p-4 rounded-lg border bg-card">
            <div className="space-y-2">
              <p className="font-semibold">Font Preview</p>
              <p className="text-muted-foreground">
                This is a preview text of the current font size. HumanWhisper makes complex concepts simple and easy to understand.
              </p>
              <p className="text-sm text-muted-foreground">
                Small text example for comparison
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interface Options */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-purple-500" />
            <CardTitle>Interface Options</CardTitle>
          </div>
          <CardDescription>
            Customize interface display and behavior
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Sidebar Collapse */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="text-sm font-medium">Default Collapse Sidebar</div>
              <div className="text-xs text-muted-foreground">
                Automatically collapse conversation history sidebar on startup
              </div>
            </div>
            <Switch
              checked={sidebarCollapsed}
              onCheckedChange={setSidebarCollapsed}
            />
          </div>

          {/* Welcome Message */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <div className="text-sm font-medium">Show Welcome Message</div>
              <div className="text-xs text-muted-foreground">
                Display welcome interface and example questions in new conversations
              </div>
            </div>
            <Switch
              checked={showWelcomeMessage}
              onCheckedChange={setShowWelcomeMessage}
            />
          </div>
        </CardContent>
      </Card>

      {/* Custom Colors (Coming Soon) */}
      <Card className="opacity-60">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Palette className="h-5 w-5 text-orange-500" />
            <CardTitle>Custom Colors</CardTitle>
          </div>
          <CardDescription>
            Customize theme colors (coming soon)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-2">
            {[
              '#0ea5e9', '#f2750a', '#10b981', '#f59e0b',
              '#ef4444', '#8b5cf6', '#ec4899', '#6b7280'
            ].map((color, index) => (
              <div
                key={index}
                className="h-10 w-10 rounded-lg border cursor-not-allowed"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Custom color functionality is under development, stay tuned
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
