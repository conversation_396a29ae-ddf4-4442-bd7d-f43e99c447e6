'use client'

import React, { useState, useEffect } from 'react'
import { ChevronDown, Brain, Check, AlertCircle, Bot, MessageSquare, Star, Coins } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getModelDisplayName, getModelProvider } from '@/lib/ai-client'
import { getAllModels, getModelPerformance } from '@/lib/ai-models'
import { getBaseModelCredits } from '@/lib/credits-utils'
import type { AIModel, ModelInfo } from '@/types'

interface ModelSelectorProps {
  currentModel: AIModel
  onModelChange: (model: AIModel) => void
  disabled?: boolean
  showDetails?: boolean
  className?: string
  size?: 'sm' | 'default'
}

export function ModelSelector({
  currentModel,
  onModelChange,
  disabled = false,
  showDetails = false,
  className,
  size = 'default'
}: ModelSelectorProps) {
  const [models, setModels] = useState<ModelInfo[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showComparison, setShowComparison] = useState(false)

  // 获取模型基础积分消耗（不含图像）
  const getModelCredits = (modelId: string): number => {
    return getBaseModelCredits(modelId)
  }

  // 加载模型列表
  useEffect(() => {
    const loadModels = async () => {
      setIsLoading(true)
      try {
        // 获取本地模型列表
        const localModels = getAllModels()
        setModels(localModels)

        // 可选：检查远程可用性
        try {
          const response = await fetch('/api/models?action=list')
          if (response.ok) {
            const data = await response.json()
            setModels(data.models || localModels)
          }
        } catch (error) {
          // 无法获取远程模型状态，静默处理
        }
      } catch (error) {
        // 加载模型列表失败，静默处理
        // 使用默认模型列表
        setModels(getAllModels())
      } finally {
        setIsLoading(false)
      }
    }

    loadModels()
  }, [])

  // 获取模型图标
  const getModelIcon = (modelId: AIModel) => {
    switch (modelId) {
      case 'gpt-4.1-nano':
        return <Bot className="h-4 w-4 text-green-500" />
      case 'claude-3-5-haiku-latest':
        return <MessageSquare className="h-4 w-4 text-purple-500" />
      case 'gemini-2.0-flash':
        return <Star className="h-4 w-4 text-blue-500" />
      default:
        return <Brain className="h-4 w-4 text-gray-500" />
    }
  }

  // 获取性能评级显示
  const getPerformanceDisplay = (modelId: AIModel) => {
    const performance = getModelPerformance(modelId)
    return {
      speed: '⚡'.repeat(performance.speed),
      quality: '⭐'.repeat(performance.quality),
      cost: '💰'.repeat(5 - performance.cost + 1), // 成本越低越好
      reasoning: '🧠'.repeat(performance.reasoning)
    }
  }

  // 简单模式选择器
  if (!showDetails) {
    return (
      <div className={className}>
        <Select
          value={currentModel}
          onValueChange={(value) => onModelChange(value as AIModel)}
          disabled={disabled || isLoading}
        >
          <SelectTrigger className={size === 'sm' ? 'w-[140px] h-8 text-xs' : 'w-[200px]'}>
            <div className="flex items-center space-x-2">
              {size !== 'sm' && getModelIcon(currentModel)}
              <SelectValue placeholder={size === 'sm' ? 'Model' : 'Select Model'} />
            </div>
          </SelectTrigger>
          <SelectContent>
            {models.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-2">
                    {size !== 'sm' && getModelIcon(model.id)}
                    <span>{model.name}</span>
                  </div>
                  <Badge variant="outline" className="text-xs px-2 py-0 ml-2 flex items-center gap-1">
                    {getModelCredits(model.id)}
                    <Coins className="h-3 w-3 text-yellow-500" />
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  // 详细模式选择器
  return (
    <div className={`space-y-4 ${className}`}>
      {/* 当前选择 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getModelIcon(currentModel)}
          <div>
            <h3 className="font-medium">{getModelDisplayName(currentModel)}</h3>
            <p className="text-sm text-muted-foreground">
              {getModelProvider(currentModel)}
            </p>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowComparison(!showComparison)}
        >
          {showComparison ? 'Hide' : 'Compare Models'}
          <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${
            showComparison ? 'rotate-180' : ''
          }`} />
        </Button>
      </div>

      {/* 模型比较面板 */}
      {showComparison && (
        <div className="grid gap-4 md:grid-cols-3">
          {models.map((model) => {
            const performance = getPerformanceDisplay(model.id)
            const isSelected = model.id === currentModel
            
            return (
              <Card 
                key={model.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => onModelChange(model.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getModelIcon(model.id)}
                      <CardTitle className="text-base">{model.name}</CardTitle>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                  <CardDescription>{model.description}</CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  {/* 性能指标 */}
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Speed:</span>
                      <span>{performance.speed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Quality:</span>
                      <span>{performance.quality}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cost:</span>
                      <span>{performance.cost}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Reasoning:</span>
                      <span>{performance.reasoning}</span>
                    </div>
                  </div>

                  {/* 规格信息 */}
                  <div className="pt-2 border-t space-y-1 text-xs text-muted-foreground">
                    <div className="flex justify-between">
                      <span>Max Tokens:</span>
                      <span>{model.maxTokens.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Per 1K Tokens:</span>
                      <span>${model.costPer1kTokens}</span>
                    </div>
                  </div>

                  {/* 状态指示 */}
                  <div className="flex items-center justify-between pt-2">
                    <Badge 
                      variant={isSelected ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {model.provider}
                    </Badge>
                    
                    <div className="flex items-center space-x-1 text-xs text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Online</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* 推荐提示 */}
      {showComparison && (
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300">
                Selection Guide
              </p>
              <ul className="mt-1 space-y-1 text-muted-foreground">
                <li>• <strong>GPT</strong>: Best for quick Q&A and daily conversations</li>
                <li>• <strong>Claude</strong>: Best for deep analysis and complex reasoning</li>
                <li>• <strong>Gemini</strong>: Best for creative tasks and diverse responses</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
