'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Loader2, Wand2, Download, Sparkles, Palette, Target, Zap, Crown, Heart } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 图像尺寸选项
const IMAGE_SIZES = [
  { id: '1024x1024', name: '1:1 Square', description: 'Perfect for social media profiles' },
  { id: '1024x2048', name: '1:2 Portrait', description: 'Great for mobile apps' },
  { id: '1536x1024', name: '3:2 Landscape', description: 'Ideal for websites' },
  { id: '1536x2048', name: '3:4 Portrait', description: 'Business cards and prints' },
  { id: '1920x1080', name: '16:9 Wide', description: 'Banners and headers' },
  { id: '1080x1920', name: '9:16 Tall', description: 'Stories and vertical displays' }
]

// 行业类型
const INDUSTRIES = [
  { id: 'technology', name: 'Technology', description: 'Software, AI, startups' },
  { id: 'business', name: 'Business', description: 'Consulting, finance, corporate' },
  { id: 'creative', name: 'Creative', description: 'Design, art, media' },
  { id: 'health', name: 'Healthcare', description: 'Medical, wellness, fitness' },
  { id: 'education', name: 'Education', description: 'Schools, training, learning' },
  { id: 'food', name: 'Food & Beverage', description: 'Restaurants, cafes, catering' },
  { id: 'fashion', name: 'Fashion', description: 'Clothing, beauty, lifestyle' },
  { id: 'real-estate', name: 'Real Estate', description: 'Property, construction, architecture' }
]

// Logo风格
const LOGO_STYLES = [
  { id: 'modern', name: 'Modern Minimalist', description: 'Clean, simple, contemporary' },
  { id: 'classic', name: 'Classic Business', description: 'Professional, traditional, trustworthy' },
  { id: 'creative', name: 'Creative Artistic', description: 'Unique, expressive, innovative' },
  { id: 'friendly', name: 'Friendly Approachable', description: 'Warm, welcoming, personal' },
  { id: 'luxury', name: 'Luxury Premium', description: 'Elegant, sophisticated, high-end' },
  { id: 'tech', name: 'Tech Futuristic', description: 'Digital, innovative, cutting-edge' }
]

// Logo类型
const LOGO_TYPES = [
  { id: 'combination', name: 'Text + Icon', description: 'Brand name with symbol' },
  { id: 'wordmark', name: 'Text Only', description: 'Typography-focused design' },
  { id: 'pictorial', name: 'Icon Only', description: 'Pure symbol or graphic' },
  { id: 'abstract', name: 'Abstract Mark', description: 'Geometric shapes and patterns' }
]

export function LogoGeneratorClient() {
  // 基础信息状态
  const [brandName, setBrandName] = useState('')
  const [industry, setIndustry] = useState('')
  const [logoStyle, setLogoStyle] = useState('')
  const [logoType, setLogoType] = useState('combination')
  const [coreMessage, setCoreMessage] = useState('')
  const [colorPreference, setColorPreference] = useState('')
  
  // 技术参数状态
  const [imageSize, setImageSize] = useState('1024x1024')
  const [numImages, setNumImages] = useState(1)
  const [seed, setSeed] = useState('')
  const [inferenceSteps, setInferenceSteps] = useState([25])
  const [guidanceScale, setGuidanceScale] = useState([7.5])
  const [negativePrompt, setNegativePrompt] = useState('')
  
  // 生成状态
  const [isLoading, setIsLoading] = useState(false)
  const [generatedLogos, setGeneratedLogos] = useState<string[]>([])
  const { toast } = useToast()

  const handleGenerate = async () => {
    if (!brandName.trim()) {
      toast({
        title: "Brand Name Required",
        description: "Please enter your brand or company name.",
        variant: "destructive"
      })
      return
    }

    if (!industry) {
      toast({
        title: "Industry Required", 
        description: "Please select your industry type.",
        variant: "destructive"
      })
      return
    }

    if (!logoStyle) {
      toast({
        title: "Style Required",
        description: "Please select a logo style.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/logo-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandName: brandName.trim(),
          industry,
          logoStyle,
          logoType,
          coreMessage: coreMessage.trim(),
          colorPreference: colorPreference.trim(),
          imageSize,
          numImages,
          seed: seed.trim() || undefined,
          inferenceSteps: inferenceSteps[0],
          guidanceScale: guidanceScale[0],
          negativePrompt: negativePrompt.trim()
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate logo')
      }

      const data = await response.json()
      setGeneratedLogos(data.images.map((img: any) => img.url))
      
      toast({
        title: "Logo Generated!",
        description: "Your professional logo has been created successfully.",
      })
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate logo. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = async (imageUrl: string, index: number) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${brandName.replace(/\s+/g, '-').toLowerCase()}-logo-${index + 1}.png`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast({
        title: "Downloaded!",
        description: "Logo has been saved to your device.",
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download logo. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleClear = () => {
    setBrandName('')
    setIndustry('')
    setLogoStyle('')
    setLogoType('combination')
    setCoreMessage('')
    setColorPreference('')
    setSeed('')
    setNegativePrompt('')
    setGeneratedLogos([])
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Free AI Logo Generator
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Create Professional Logos Instantly
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate stunning <strong>professional logos</strong> without design skills. 
              Just describe your brand and let AI create the perfect logo for you.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Generation
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Crown className="h-4 w-4 mr-1" />
                Professional Quality
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Heart className="h-4 w-4 mr-1" />
                No Design Skills Needed
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Input Section */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5 text-primary" />
                      Brand Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Brand Name *</label>
                      <Input
                        placeholder="Enter your brand or company name"
                        value={brandName}
                        onChange={(e) => setBrandName(e.target.value)}
                      />
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Industry *</label>
                        <Select value={industry} onValueChange={setIndustry}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your industry" />
                          </SelectTrigger>
                          <SelectContent>
                            {INDUSTRIES.map((item) => (
                              <SelectItem key={item.id} value={item.id}>
                                <div>
                                  <div className="font-medium">{item.name}</div>
                                  <div className="text-xs text-muted-foreground">{item.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-2 block">Logo Style *</label>
                        <Select value={logoStyle} onValueChange={setLogoStyle}>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose style" />
                          </SelectTrigger>
                          <SelectContent>
                            {LOGO_STYLES.map((style) => (
                              <SelectItem key={style.id} value={style.id}>
                                <div>
                                  <div className="font-medium">{style.name}</div>
                                  <div className="text-xs text-muted-foreground">{style.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Logo Type</label>
                      <Select value={logoType} onValueChange={setLogoType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LOGO_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Core Message (Optional)</label>
                      <Input
                        placeholder="What should your logo represent? e.g., innovation, trust, creativity"
                        value={coreMessage}
                        onChange={(e) => setCoreMessage(e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Color Preference (Optional)</label>
                      <Input
                        placeholder="Preferred colors, e.g., blue and white, vibrant colors, monochrome"
                        value={colorPreference}
                        onChange={(e) => setColorPreference(e.target.value)}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                {/* Generation Controls */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-primary" />
                      Generate Logo
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Image Size</label>
                      <Select value={imageSize} onValueChange={setImageSize}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {IMAGE_SIZES.map((size) => (
                            <SelectItem key={size.id} value={size.id}>
                              <div>
                                <div className="font-medium">{size.name}</div>
                                <div className="text-xs text-muted-foreground">{size.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Number of Images</label>
                      <Select value={numImages.toString()} onValueChange={(value) => setNumImages(parseInt(value))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Logo</SelectItem>
                          <SelectItem value="2">2 Logos</SelectItem>
                          <SelectItem value="3">3 Logos</SelectItem>
                          <SelectItem value="4">4 Logos</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex gap-3 justify-center">
                      <Button
                        onClick={handleGenerate}
                        disabled={isLoading || !brandName.trim() || !industry || !logoStyle}
                        className="px-8"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4 mr-2" />
                            Generate Logo
                          </>
                        )}
                      </Button>
                      <Button variant="outline" onClick={handleClear} className="px-8">
                        Clear
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Advanced Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Advanced Settings (Optional)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Inference Steps: {inferenceSteps[0]}
                      </label>
                      <Slider
                        value={inferenceSteps}
                        onValueChange={setInferenceSteps}
                        min={10}
                        max={50}
                        step={5}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        Higher values = better quality, slower generation
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Guidance Scale: {guidanceScale[0]}
                      </label>
                      <Slider
                        value={guidanceScale}
                        onValueChange={setGuidanceScale}
                        min={1}
                        max={20}
                        step={0.5}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        How closely to follow the description
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Seed (Optional)</label>
                      <Input
                        placeholder="Random number for reproducible results"
                        value={seed}
                        onChange={(e) => setSeed(e.target.value)}
                        type="number"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Negative Prompt (Optional)</label>
                      <Textarea
                        placeholder="What to avoid in the logo, e.g., blurry, low quality, text errors"
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Generated Logos */}
                {generatedLogos.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Your Generated Logos</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {generatedLogos.map((logoUrl, index) => (
                          <div key={index} className="space-y-2">
                            <div className="bg-muted/30 p-4 rounded-lg">
                              <img 
                                src={logoUrl} 
                                alt={`Generated logo ${index + 1}`}
                                className="w-full h-auto rounded"
                              />
                            </div>
                            <Button 
                              onClick={() => handleDownload(logoUrl, index)}
                              variant="outline" 
                              className="w-full"
                            >
                              <Download className="h-4 w-4 mr-2" />
                              Download Logo {index + 1}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {!generatedLogos.length && (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <div className="text-muted-foreground">
                        <Palette className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Your generated logos will appear here</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              How Our AI Logo Generator Works
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Create professional logos in minutes without any design experience. Our AI understands your brand and generates perfect logos.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Palette className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Describe Your Brand</h3>
              <p className="text-muted-foreground">
                Simply enter your brand name, industry, and preferred style. No complex prompts needed.
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wand2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">AI Creates Magic</h3>
              <p className="text-muted-foreground">
                Our advanced AI analyzes your requirements and generates multiple professional logo options.
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Download className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Download & Use</h3>
              <p className="text-muted-foreground">
                Choose your favorite design and download it instantly in high resolution for any use.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose Our AI Logo Generator?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Professional logo design made simple, fast, and completely free
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">100% Free</h3>
              <p className="text-muted-foreground text-sm">
                Generate unlimited logos without any cost or hidden fees
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Instant Results</h3>
              <p className="text-muted-foreground text-sm">
                Get professional logos in seconds, not days or weeks
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Crown className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Professional Quality</h3>
              <p className="text-muted-foreground text-sm">
                AI-generated logos that rival expensive designer work
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No Design Skills</h3>
              <p className="text-muted-foreground text-sm">
                Anyone can create stunning logos without design experience
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Target className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Multiple Formats</h3>
              <p className="text-muted-foreground text-sm">
                Various sizes and formats perfect for any use case
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Palette className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Industry Specific</h3>
              <p className="text-muted-foreground text-sm">
                Tailored designs for different industries and business types
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* AI Model Prompt Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              AI Model Prompt Examples
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Learn from professional prompt formulas used by top AI image generation models
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* DALL-E 3 Example */}
            <Card className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                  DALL-E 3 Formula
                </CardTitle>
                <p className="text-sm text-muted-foreground">OpenAI's flagship model - excels at understanding natural language</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Prompt Formula:</h4>
                  <code className="text-sm text-blue-700 dark:text-blue-300">
                    "A [style] logo for [brand name], [industry description]. [Visual elements]. [Color scheme]. [Mood/feeling]. Professional, clean, scalable design."
                  </code>
                </div>
                <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Example Prompt:</h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    "A modern minimalist logo for TechFlow, a software development company. Geometric shapes suggesting data flow. Blue and white color scheme. Clean, innovative, trustworthy feeling. Professional, clean, scalable design."
                  </p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <strong>Best for:</strong> Natural language descriptions, complex concepts, brand storytelling
                </div>
              </CardContent>
            </Card>

            {/* Midjourney Example */}
            <Card className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                  Midjourney Formula
                </CardTitle>
                <p className="text-sm text-muted-foreground">Artistic excellence with parameter control</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-2">Prompt Formula:</h4>
                  <code className="text-sm text-purple-700 dark:text-purple-300">
                    "[Subject] logo, [style keywords], [color palette] --ar 1:1 --v 6 --s [stylize value] --q 2"
                  </code>
                </div>
                <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Example Prompt:</h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    "Luxury jewelry brand logo, elegant serif typography, gold and black, minimalist, premium --ar 1:1 --v 6 --s 250 --q 2"
                  </p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <strong>Best for:</strong> Artistic styles, fine control over aesthetics, premium designs
                </div>
              </CardContent>
            </Card>

            {/* Stable Diffusion Example */}
            <Card className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-3 w-3 bg-orange-500 rounded-full"></div>
                  Stable Diffusion Formula
                </CardTitle>
                <p className="text-sm text-muted-foreground">Open-source powerhouse with detailed control</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">Prompt Formula:</h4>
                  <code className="text-sm text-orange-700 dark:text-orange-300">
                    "(best quality, masterpiece), [subject] logo, [detailed description], [style modifiers], [technical specs]"
                  </code>
                </div>
                <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Example Prompt:</h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    "(best quality, masterpiece), coffee shop logo, steaming cup with geometric coffee beans, warm brown and cream colors, hand-drawn illustration style, vector art, clean lines, 8k resolution"
                  </p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <strong>Best for:</strong> Detailed control, custom models, specific artistic styles
                </div>
              </CardContent>
            </Card>

            {/* Kolors (Current Model) Example */}
            <Card className="overflow-hidden border-2 border-primary">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <div className="h-3 w-3 bg-primary rounded-full"></div>
                  Kolors Formula (Our Model)
                </CardTitle>
                <p className="text-sm text-muted-foreground">Kwai-Kolors/Kolors - Advanced image generation with superior quality</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-primary/10 p-4 rounded-lg">
                  <h4 className="font-medium text-primary mb-2">Prompt Formula:</h4>
                  <code className="text-sm">
                    "Create a professional [type] logo for [brand], [industry]. Style: [description]. [Elements]. [Colors]. High quality, scalable, business use."
                  </code>
                </div>
                <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Example Prompt:</h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    "Create a professional combination mark logo for GreenLeaf, organic food company. Style: modern minimalist, clean and contemporary. Leaf symbol with typography. Green and white colors. High quality, scalable, business use."
                  </p>
                </div>
                <div className="text-xs text-muted-foreground">
                  <strong>Best for:</strong> Professional business logos, brand identity, balanced quality and speed
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 text-center">
            <div className="bg-blue-50 dark:bg-blue-950/20 p-6 rounded-lg max-w-4xl mx-auto">
              <h3 className="text-lg font-semibold mb-3 text-blue-800 dark:text-blue-200">
                💡 Pro Tips for Better Logo Prompts
              </h3>
              <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
                <div>
                  <strong>Structure:</strong> Always include brand name, industry, style, and quality keywords
                </div>
                <div>
                  <strong>Colors:</strong> Be specific - "corporate blue" vs "bright blue" vs "navy blue"
                </div>
                <div>
                  <strong>Style:</strong> Use clear descriptors - "minimalist", "vintage", "hand-drawn"
                </div>
                <div>
                  <strong>Format:</strong> Specify "logo", "vector", "scalable" for business use
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


    </>
  )
}
