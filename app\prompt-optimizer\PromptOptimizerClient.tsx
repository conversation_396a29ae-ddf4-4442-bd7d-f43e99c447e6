'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Loader2, Wand2, Copy, CheckCircle, Sparkles, Brain, Target } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'Qwen/Qwen2-7B-Instruct',
    name: 'Qwen',
    description: 'Fast and efficient for general prompt optimization'
  },
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM',
    description: 'Advanced reasoning for complex prompt structures'
  }
]

// 提示词类型
const PROMPT_TYPES = [
  { id: 'creative', name: 'Creative Writing', description: 'Stories, poems, creative content' },
  { id: 'analytical', name: 'Analysis & Research', description: 'Data analysis, research tasks' },
  { id: 'coding', name: 'Code Generation', description: 'Programming and technical tasks' },
  { id: 'business', name: 'Business & Marketing', description: 'Business plans, marketing copy' },
  { id: 'educational', name: 'Educational Content', description: 'Tutorials, explanations, learning' },
  { id: 'general', name: 'General Purpose', description: 'General conversation and tasks' }
]

export function PromptOptimizerClient() {
  const [userInput, setUserInput] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'Qwen/Qwen2-7B-Instruct')
  const [selectedType, setSelectedType] = useState('general')
  const [optimizedPrompt, setOptimizedPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const { toast } = useToast()

  const handleOptimize = async () => {
    if (!userInput.trim()) {
      toast({
        title: "Input Required",
        description: "Please enter your idea or rough prompt first.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/prompt-optimizer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userInput: userInput.trim(),
          model: selectedModel,
          promptType: selectedType
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to optimize prompt')
      }

      const data = await response.json()
      // 清理生成的提示词，去除markdown格式符号
      const cleanedPrompt = data.optimizedPrompt
        .replace(/^\*+\s*/gm, '') // 去除行首的*号
        .replace(/^#+\s*/gm, '') // 去除行首的#号
        .replace(/\*\*(.*?)\*\*/g, '$1') // 去除粗体标记
        .replace(/\*(.*?)\*/g, '$1') // 去除斜体标记
        .trim()

      setOptimizedPrompt(cleanedPrompt)

      toast({
        title: "Prompt Optimized!",
        description: "Your prompt has been successfully optimized.",
      })
    } catch (error) {
      toast({
        title: "Optimization Failed",
        description: "Failed to optimize your prompt. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopy = async () => {
    if (!optimizedPrompt) return
    
    try {
      await navigator.clipboard.writeText(optimizedPrompt)
      setCopied(true)
      toast({
        title: "Copied!",
        description: "Optimized prompt copied to clipboard.",
      })
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive"
      })
    }
  }

  const handleClear = () => {
    setUserInput('')
    setOptimizedPrompt('')
    setCopied(false)
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                AI Prompt Optimizer
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Transform Ideas into Perfect Prompts
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Turn your <strong>vague ideas</strong> into <strong>structured, effective prompts</strong> that get better AI results. 
              Free prompt engineering tool powered by advanced AI models.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                Free to Use
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Brain className="h-4 w-4 mr-1" />
                No Login Required
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Target className="h-4 w-4 mr-1" />
                Better AI Results
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Tool Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wand2 className="h-5 w-5 text-primary" />
                    Your Idea or Rough Prompt
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Enter your idea, question, or rough prompt here. For example: 'I want to write a blog post about AI' or 'Help me analyze customer feedback data'"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    className="min-h-[200px] resize-none"
                  />
                  
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Prompt Type</label>
                      <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {PROMPT_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex gap-3 justify-center">
                    <Button
                      onClick={handleOptimize}
                      disabled={isLoading || !userInput.trim()}
                      className="px-8"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Optimizing...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Optimize Prompt
                        </>
                      )}
                    </Button>
                    <Button variant="outline" onClick={handleClear} className="px-8">
                      Clear
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Output Section */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-primary" />
                    Optimized Prompt
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {optimizedPrompt ? (
                    <div className="space-y-4">
                      <div className="bg-muted/50 p-4 rounded-lg min-h-[200px] whitespace-pre-wrap text-sm leading-relaxed">
                        {optimizedPrompt}
                      </div>
                      <Button onClick={handleCopy} variant="outline" className="w-full">
                        {copied ? (
                          <>
                            <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Optimized Prompt
                          </>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="bg-muted/30 p-8 rounded-lg text-center min-h-[200px] flex items-center justify-center">
                      <div className="text-muted-foreground">
                        <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Your optimized prompt will appear here</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              How the AI Prompt Optimizer Works
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our AI analyzes your input and transforms it into a structured, effective prompt using proven prompt engineering techniques.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Analyze Intent</h3>
              <p className="text-muted-foreground">
                AI understands your goal and identifies the key elements needed for an effective prompt
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wand2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Structure & Format</h3>
              <p className="text-muted-foreground">
                Applies proven prompt engineering patterns to create clear, actionable instructions
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Optimize Results</h3>
              <p className="text-muted-foreground">
                Delivers a refined prompt that gets better, more consistent results from any AI model
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Before & After Examples
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              See how our AI prompt optimizer transforms vague ideas into clear, effective prompts
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Example 1 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Creative Writing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Write a story about AI"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Write a 500-word science fiction short story about an AI that discovers emotions. Include: 1) A clear protagonist (the AI), 2) A specific setting (research lab in 2030), 3) A conflict (AI struggles with newfound emotions), 4) A resolution. Use descriptive language and maintain a hopeful tone throughout."
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Example 2 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Business Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Analyze my business data"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Act as a business analyst. Analyze the provided sales data and create a comprehensive report including: 1) Key performance metrics and trends, 2) Top-performing products/services, 3) Seasonal patterns, 4) Areas of concern or opportunity, 5) 3 specific actionable recommendations. Present findings in a structured format with clear headings and bullet points."
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Example 3 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Code Generation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Create a login function"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Create a secure user login function in JavaScript using Node.js and Express. Requirements: 1) Accept email and password parameters, 2) Validate input format, 3) Hash password comparison using bcrypt, 4) Return JWT token on success, 5) Include proper error handling for invalid credentials, 6) Add rate limiting protection. Include comments explaining each step."
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Example 4 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Educational Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Explain machine learning"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Create a beginner-friendly explanation of machine learning for high school students. Include: 1) Simple definition with everyday analogies, 2) 3 real-world examples they can relate to, 3) Basic types (supervised, unsupervised, reinforcement), 4) How it differs from traditional programming, 5) Career opportunities in the field. Use conversational tone and avoid technical jargon."
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Example 5 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Marketing Copy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Write an ad for my app"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Write a compelling Facebook ad copy for a productivity app targeting busy professionals aged 25-40. Include: 1) Attention-grabbing headline addressing time management pain points, 2) 3 key benefits with specific outcomes, 3) Social proof element, 4) Clear call-to-action for free trial, 5) Urgency element. Keep under 150 words with persuasive, professional tone."
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Example 6 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-primary">Research & Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-red-600 mb-2">❌ Before (Vague)</h4>
                  <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded text-sm">
                    "Research climate change"
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-green-600 mb-2">✅ After (Optimized)</h4>
                  <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded text-sm">
                    "Conduct a comprehensive research analysis on climate change impacts on agriculture in Southeast Asia. Focus on: 1) Temperature and precipitation changes over the last 20 years, 2) Effects on major crops (rice, palm oil, rubber), 3) Economic impact on farming communities, 4) Current adaptation strategies, 5) Future projections and recommendations. Use peer-reviewed sources and present findings in structured report format with data visualizations."
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Why Use Our AI Prompt Optimizer?
          </h2>
          <p className="text-xl mb-12 opacity-90 max-w-2xl mx-auto">
            Get better results from Claude, Gemini, GPT, O3, DeepSeek, Grok, DALL-E, Midjourney, Stable Diffusion, Sora, Runway, ElevenLabs, and other cutting-edge AI models with professionally optimized prompts
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Better AI Responses</h3>
              <p className="opacity-90">
                Get more accurate, relevant, and useful responses from any AI model
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Save Time & Effort</h3>
              <p className="opacity-90">
                No need to learn complex prompt engineering - let AI do the work for you
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Consistent Results</h3>
              <p className="opacity-90">
                Get reliable, repeatable results every time you use AI tools
              </p>
            </div>
          </div>


        </div>
      </section>
    </>
  )
}
