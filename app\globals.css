/* HumanWhisper 全局样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入自定义Markdown样式 */
@import '../styles/markdown.css';

/* CSS变量定义 - 支持明暗主题 */
@layer base {
  :root {
    /* 基础颜色 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    /* 卡片颜色 */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    /* 弹出层颜色 */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    /* 主色调 */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    
    /* 次要色调 */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    
    /* 静音色调 */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    /* 强调色调 */
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* 危险色调 */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* 边框和输入框 */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    /* 圆角半径 */
    --radius: 0.5rem;
  }
 
  .dark {
    /* 暗色主题颜色 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

/* 基础样式重置 */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 聊天消息样式 */
  .message-user {
    @apply bg-primary text-primary-foreground ml-auto max-w-[80%] rounded-lg px-4 py-2;
  }
  
  .message-assistant {
    @apply bg-muted text-muted-foreground mr-auto max-w-[80%] rounded-lg px-4 py-2;
  }
  
  /* 代码块样式 */
  .prose pre {
    @apply bg-muted border border-border rounded-lg p-4 overflow-x-auto;
  }
  
  .prose code {
    @apply bg-muted px-1.5 py-0.5 rounded text-sm;
  }
  
  /* 加载动画 */
  .loading-dots {
    @apply flex space-x-1;
  }
  
  .loading-dots div {
    @apply w-2 h-2 bg-muted-foreground rounded-full animate-pulse;
  }
  
  .loading-dots div:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .loading-dots div:nth-child(3) {
    animation-delay: 0.4s;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, 
      hsl(var(--whisper-500)) 0%, 
      hsl(var(--human-500)) 100%);
  }
  
  /* 玻璃效果 */
  .glass-effect {
    @apply backdrop-blur-sm bg-background/80 border border-border/50;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本选择样式 */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  html {
    font-size: 16px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
}
