'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, FileText, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Link, AlertTriangle, BookOpen, Scale, Eye } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 政策文档类型选项
const POLICY_TYPES = [
  { id: 'privacy_policy', name: 'Privacy Policy', description: 'Website or app privacy policies' },
  { id: 'terms_of_service', name: 'Terms of Service', description: 'User agreements and terms' },
  { id: 'subscription_agreement', name: 'Subscription Agreement', description: 'Paid service agreements' },
  { id: 'cookie_policy', name: '<PERSON>ie Policy', description: 'Website cookie usage policies' },
  { id: 'data_processing', name: 'Data Processing Agreement', description: 'GDPR and data handling terms' },
  { id: 'user_agreement', name: 'User Agreement', description: 'Platform usage agreements' },
  { id: 'license_agreement', name: 'License Agreement', description: 'Software licensing terms' },
  { id: 'rental_contract', name: 'Rental Contract', description: 'Housing or equipment rental' },
  { id: 'custom', name: 'Custom Document', description: 'Other legal or policy documents' }
]

// 输入方式选项
const INPUT_METHODS = [
  { id: 'url', name: 'Website URL', description: 'Automatically fetch from webpage' },
  { id: 'text', name: 'Copy & Paste', description: 'Manually paste document text' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Strong reasoning, legal text analysis'
  },
  {
    id: 'Qwen/Qwen3-8B',
    name: 'Qwen3',
    description: 'Long context, complex documents'
  }
]

export function PolicyTLDRClient() {
  // 表单状态
  const [policyType, setPolicyType] = useState('')
  const [customType, setCustomType] = useState('')
  const [inputMethod, setInputMethod] = useState('url')
  const [policyUrl, setPolicyUrl] = useState('')
  const [policyText, setPolicyText] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B')
  
  // 处理状态
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStep, setCurrentStep] = useState<'input' | 'fetching' | 'analyzing' | 'complete'>('input')
  const [fetchedContent, setFetchedContent] = useState('')
  const [tldrSummary, setTldrSummary] = useState('')
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 处理政策分析
  const handleAnalyze = async () => {
    if (!policyType || (inputMethod === 'url' && !policyUrl.trim()) || (inputMethod === 'text' && !policyText.trim())) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsProcessing(true)
    setError('')
    setCurrentStep('input')
    
    try {
      let contentToAnalyze = ''
      
      // 如果是URL输入，先爬取内容
      if (inputMethod === 'url') {
        setCurrentStep('fetching')
        
        const fetchResponse = await fetch('/api/policy-tldr/fetch', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            url: policyUrl.trim()
          }),
        })

        if (!fetchResponse.ok) {
          const errorData = await fetchResponse.json()
          if (fetchResponse.status === 403) {
            throw new Error('This website has anti-scraping protection. Please copy and paste the content manually.')
          }
          throw new Error(errorData.error || 'Failed to fetch content from URL')
        }

        const fetchData = await fetchResponse.json()
        contentToAnalyze = fetchData.content
        setFetchedContent(contentToAnalyze)
      } else {
        contentToAnalyze = policyText.trim()
      }

      // 分析政策内容
      setCurrentStep('analyzing')
      const analyzeResponse = await fetch('/api/policy-tldr/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          policyType: policyType === 'custom' ? customType : policyType,
          content: contentToAnalyze,
          model: selectedModel
        }),
      })

      if (!analyzeResponse.ok) {
        throw new Error('Failed to analyze policy content')
      }

      const analyzeData = await analyzeResponse.json()
      setTldrSummary(analyzeData.summary)
      setCurrentStep('complete')

      toast({
        title: "Analysis Complete!",
        description: "Policy summary has been generated.",
      })

    } catch (error: any) {
      setError(error.message || 'Failed to analyze policy. Please try again.')
      toast({
        title: "Analysis Failed",
        description: error.message || "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: "Summary copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
                AI Policy TL;DR
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Simplify Legal Documents
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Transform <strong>complex privacy policies and terms</strong> into simple summaries. 
              Get key points, rights, and risks explained clearly.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Analysis
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Privacy Focused
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Web Scraping
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Analyzer Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      Policy Analysis Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Policy Type */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Document Type *</label>
                      <Select value={policyType} onValueChange={setPolicyType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose document type" />
                        </SelectTrigger>
                        <SelectContent>
                          {POLICY_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Type Input */}
                    {policyType === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Document Type *</label>
                        <input
                          type="text"
                          placeholder="Describe the document type"
                          value={customType}
                          onChange={(e) => setCustomType(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Input Method */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Input Method *</label>
                      <Select value={inputMethod} onValueChange={setInputMethod}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {INPUT_METHODS.map((method) => (
                            <SelectItem key={method.id} value={method.id}>
                              <div>
                                <div className="font-medium">{method.name}</div>
                                <div className="text-xs text-muted-foreground">{method.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* URL Input */}
                    {inputMethod === 'url' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Policy URL *</label>
                        <div className="flex gap-2">
                          <div className="flex-1 relative">
                            <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <input
                              type="url"
                              placeholder="https://example.com/privacy-policy"
                              value={policyUrl}
                              onChange={(e) => setPolicyUrl(e.target.value)}
                              className="w-full pl-10 pr-3 py-2 border border-input rounded-md"
                            />
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          We'll automatically fetch the content from this URL
                        </p>
                      </div>
                    )}

                    {/* Text Input */}
                    {inputMethod === 'text' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Policy Content *</label>
                        <Textarea
                          placeholder="Paste the privacy policy or terms of service text here..."
                          value={policyText}
                          onChange={(e) => setPolicyText(e.target.value)}
                          className="min-h-[200px] resize-none"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Copy and paste the full document text
                        </p>
                      </div>
                    )}

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Analyze Button */}
                    <Button 
                      onClick={handleAnalyze} 
                      disabled={isProcessing}
                      className="w-full"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          {currentStep === 'fetching' && 'Fetching Content...'}
                          {currentStep === 'analyzing' && 'Analyzing Policy...'}
                        </>
                      ) : (
                        <>
                          <FileText className="h-4 w-4 mr-2" />
                          Analyze Policy
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                {/* Processing Steps */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Analysis Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className={`flex items-center gap-3 ${currentStep === 'input' ? 'text-primary' : currentStep === 'fetching' || currentStep === 'analyzing' || currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'input' ? 'bg-primary text-primary-foreground' : currentStep === 'fetching' || currentStep === 'analyzing' || currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                          1
                        </div>
                        <span>Setup Analysis</span>
                      </div>
                      {inputMethod === 'url' && (
                        <div className={`flex items-center gap-3 ${currentStep === 'fetching' ? 'text-primary' : currentStep === 'analyzing' || currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                          <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'fetching' ? 'bg-primary text-primary-foreground' : currentStep === 'analyzing' || currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                            2
                          </div>
                          <span>Fetch Content</span>
                        </div>
                      )}
                      <div className={`flex items-center gap-3 ${currentStep === 'analyzing' ? 'text-primary' : currentStep === 'complete' ? 'text-green-600' : 'text-muted-foreground'}`}>
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep === 'analyzing' ? 'bg-primary text-primary-foreground' : currentStep === 'complete' ? 'bg-green-600 text-white' : 'bg-muted text-muted-foreground'}`}>
                          {inputMethod === 'url' ? '3' : '2'}
                        </div>
                        <span>Generate Summary</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                {(fetchedContent || tldrSummary) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="flex items-center gap-2">
                          <Scale className="h-5 w-5 text-primary" />
                          Analysis Results
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {fetchedContent && inputMethod === 'url' && (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">Fetched Content</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(fetchedContent)}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-muted/50 p-3 rounded-lg max-h-32 overflow-y-auto">
                            <p className="text-sm text-muted-foreground">
                              {fetchedContent.substring(0, 300)}...
                            </p>
                          </div>
                        </div>
                      )}
                      
                      {tldrSummary && (
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">TL;DR Summary</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(tldrSummary)}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                            <div className="prose prose-sm max-w-none">
                              <pre className="whitespace-pre-wrap text-sm font-sans">
                                {tldrSummary}
                              </pre>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Error Display */}
                {error && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-start gap-3 text-red-500 text-sm p-3 bg-red-50 rounded-md">
                        <AlertTriangle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium">Analysis Failed</p>
                          <p>{error}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Empty State */}
                {!fetchedContent && !tldrSummary && !error && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center py-12 text-muted-foreground">
                        <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your policy summary will appear here</p>
                        <p className="text-sm">Fill in the form and click analyze</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Use Our AI Policy Analyzer?
            </h2>
            <p className="text-xl text-muted-foreground">
              Save time and understand your rights with intelligent legal document analysis
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  Get comprehensive policy summaries in seconds, not hours of reading
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Eye className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Plain English</h3>
                <p className="text-sm text-muted-foreground">
                  Complex legal jargon translated into clear, understandable language
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Risk Identification</h3>
                <p className="text-sm text-muted-foreground">
                  Highlights potential risks and concerning clauses you should know about
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Scale className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Rights & Obligations</h3>
                <p className="text-sm text-muted-foreground">
                  Clearly explains your rights as a user and what you're agreeing to
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Web Scraping</h3>
                <p className="text-sm text-muted-foreground">
                  Automatically fetch policies from URLs or paste content manually
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Document Types</h3>
                <p className="text-sm text-muted-foreground">
                  Supports privacy policies, terms of service, contracts, and more
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Simplify complex legal documents in 3 easy steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-red-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Input Document</h3>
              <p className="text-sm text-muted-foreground">
                Paste a URL to automatically fetch content, or copy and paste the document text directly
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">AI Analysis</h3>
              <p className="text-sm text-muted-foreground">
                Our AI analyzes the document and extracts key points, rights, obligations, and risks
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Get Summary</h3>
              <p className="text-sm text-muted-foreground">
                Receive a clear, structured summary highlighting what matters most to you
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Any Legal Document
            </h2>
            <p className="text-xl text-muted-foreground">
              Analyze various types of policies and agreements with ease
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="font-semibold mb-2">Privacy Policies</h3>
                <p className="text-sm text-muted-foreground">
                  Understand how websites and apps collect, use, and protect your data
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📋</span>
                </div>
                <h3 className="font-semibold mb-2">Terms of Service</h3>
                <p className="text-sm text-muted-foreground">
                  Know your rights and obligations when using online services and platforms
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💳</span>
                </div>
                <h3 className="font-semibold mb-2">Subscription Agreements</h3>
                <p className="text-sm text-muted-foreground">
                  Understand billing, cancellation policies, and subscription terms
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏠</span>
                </div>
                <h3 className="font-semibold mb-2">Rental Contracts</h3>
                <p className="text-sm text-muted-foreground">
                  Analyze housing contracts, lease agreements, and rental terms
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our AI policy analyzer
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How accurate is the AI analysis?",
                  answer: "Our AI is trained on legal documents and provides highly accurate summaries. However, for critical legal decisions, we recommend consulting with a qualified attorney."
                },
                {
                  question: "What happens if a website blocks scraping?",
                  answer: "If a website has anti-scraping protection, you'll receive a notification. Simply copy and paste the document text manually using the 'Copy & Paste' option."
                },
                {
                  question: "What types of documents can I analyze?",
                  answer: "We support privacy policies, terms of service, subscription agreements, cookie policies, rental contracts, license agreements, and other legal documents."
                },
                {
                  question: "Is my document data secure?",
                  answer: "Yes, we prioritize your privacy. Documents are processed securely and are not stored on our servers after analysis is complete."
                },
                {
                  question: "How long does the analysis take?",
                  answer: "Most analyses complete within 10-30 seconds, depending on document length and complexity. Web scraping may add a few extra seconds."
                },
                {
                  question: "Can I analyze documents in other languages?",
                  answer: "Currently, our AI is optimized for English documents. We're working on adding support for other languages in future updates."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
