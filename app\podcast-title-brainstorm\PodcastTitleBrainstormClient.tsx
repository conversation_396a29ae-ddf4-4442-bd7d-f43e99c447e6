'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Mic, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Target, Users, TrendingUp, Search } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 播客节目类型选项
const PODCAST_TYPES = [
  { id: 'tech', name: 'Technology', description: 'Tech news, reviews, programming, AI, startups' },
  { id: 'business', name: 'Business & Entrepreneurship', description: 'Startup stories, business strategy, leadership' },
  { id: 'storytelling', name: 'Storytelling', description: 'Personal stories, narratives, true crime, fiction' },
  { id: 'self_improvement', name: 'Self-Improvement', description: 'Personal development, productivity, wellness' },
  { id: 'education', name: 'Educational', description: 'Learning, tutorials, academic topics, how-to' },
  { id: 'entertainment', name: 'Entertainment', description: 'Comedy, pop culture, celebrity interviews' },
  { id: 'news', name: 'News & Politics', description: 'Current events, political analysis, journalism' },
  { id: 'health', name: 'Health & Wellness', description: 'Fitness, nutrition, mental health, medical topics' },
  { id: 'finance', name: 'Finance & Investing', description: 'Personal finance, investing, economics, crypto' },
  { id: 'custom', name: 'Custom Genre', description: 'Define your own podcast category' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, content marketing, SEO strategy'
  },
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Logical reasoning, title optimization strategy'
  }
]

interface GeneratedTitle {
  title: string
  explanation: string
  seoKeywords: string[]
  clickFactors: string[]
}

export function PodcastTitleBrainstormClient() {
  // 表单状态
  const [podcastType, setPodcastType] = useState('')
  const [customType, setCustomType] = useState('')
  const [contentSummary, setContentSummary] = useState('')
  const [targetAudience, setTargetAudience] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedTitles, setGeneratedTitles] = useState<GeneratedTitle[]>([])
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成播客标题
  const handleGenerate = async () => {
    if (!podcastType || !contentSummary.trim() || !targetAudience.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/podcast-title-brainstorm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          podcastType: podcastType === 'custom' ? customType : podcastType,
          contentSummary: contentSummary.trim(),
          targetAudience: targetAudience.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate podcast titles')
      }

      const data = await response.json()
      setGeneratedTitles(data.titles)

      toast({
        title: "Podcast Titles Generated!",
        description: `Generated ${data.titles.length} engaging episode titles with SEO optimization.`,
      })

    } catch (error) {
      setError('Failed to generate podcast titles. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制标题到剪贴板
  const copyTitle = async (title: string) => {
    try {
      await navigator.clipboard.writeText(title)
      toast({
        title: "Copied!",
        description: "Podcast title copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950 dark:to-blue-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                AI Podcast Title Brainstorm
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Create Catchy Episode Titles
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>engaging podcast episode titles with SEO keywords</strong>. 
              Create clickable titles that boost discovery and attract your target audience.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                8 Title Variations
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                SEO Optimized
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Click-Worthy
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mic className="h-5 w-5 text-primary" />
                      Podcast Title Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Podcast Type */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Podcast Genre *</label>
                      <Select value={podcastType} onValueChange={setPodcastType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your podcast genre" />
                        </SelectTrigger>
                        <SelectContent>
                          {PODCAST_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Type Input */}
                    {podcastType === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Genre *</label>
                        <input
                          type="text"
                          placeholder="Describe your podcast genre"
                          value={customType}
                          onChange={(e) => setCustomType(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Content Summary */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Episode Content Summary *</label>
                      <Textarea
                        placeholder="Describe your episode content in 3 key sentences. What are the main topics, insights, or stories covered?"
                        value={contentSummary}
                        onChange={(e) => setContentSummary(e.target.value)}
                        className="min-h-[120px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Include the main topics, key insights, and unique angles of your episode
                      </p>
                    </div>

                    {/* Target Audience */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Target Audience Keywords *</label>
                      <input
                        type="text"
                        placeholder="e.g., entrepreneurs, developers, parents, students, professionals"
                        value={targetAudience}
                        onChange={(e) => setTargetAudience(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Keywords that describe your ideal listeners for SEO optimization
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Titles...
                        </>
                      ) : (
                        <>
                          <Mic className="h-4 w-4 mr-2" />
                          Generate Podcast Titles
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Generated Episode Titles
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedTitles.length > 0 ? (
                      <div className="space-y-4">
                        {generatedTitles.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-lg mb-2 leading-tight">
                                  {item.title}
                                </h4>
                                <p className="text-sm text-muted-foreground mb-3">{item.explanation}</p>
                                
                                {/* SEO Keywords */}
                                <div className="mb-2">
                                  <span className="text-xs font-medium text-green-700 dark:text-green-400">SEO Keywords:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {item.seoKeywords.map((keyword, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                        {keyword}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>

                                {/* Click Factors */}
                                <div>
                                  <span className="text-xs font-medium text-blue-700 dark:text-blue-400">Click Factors:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {item.clickFactors.map((factor, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                        {factor}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyTitle(item.title)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Mic className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your podcast titles will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Great Podcast Titles Matter
            </h2>
            <p className="text-xl text-muted-foreground">
              Your episode title is the first impression that determines whether someone clicks play or scrolls past
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Search className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">SEO Discovery</h3>
                <p className="text-sm text-muted-foreground">
                  Optimized titles help your podcast appear in search results and recommendations
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Click Psychology</h3>
                <p className="text-sm text-muted-foreground">
                  Strategic use of curiosity, urgency, and benefits to maximize click-through rates
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Audience Targeting</h3>
                <p className="text-sm text-muted-foreground">
                  Titles crafted to resonate with your specific listener demographics
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Growth Optimization</h3>
                <p className="text-sm text-muted-foreground">
                  Data-driven title strategies that increase downloads and subscriber growth
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">8 Variations</h3>
                <p className="text-sm text-muted-foreground">
                  Multiple title options to test and find what works best for your audience
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Genre-Specific</h3>
                <p className="text-sm text-muted-foreground">
                  Titles tailored to your podcast genre and industry best practices
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Generate compelling podcast titles in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Genre</h3>
              <p className="text-sm text-muted-foreground">
                Select your podcast category from tech, business, storytelling, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Describe Content</h3>
              <p className="text-sm text-muted-foreground">
                Summarize your episode's key topics, insights, and unique angles
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Define Audience</h3>
              <p className="text-sm text-muted-foreground">
                Specify your target listeners with relevant keywords for SEO optimization
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Get Titles</h3>
              <p className="text-sm text-muted-foreground">
                Receive 8 optimized titles with SEO keywords and click psychology analysis
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Title Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Title Examples by Genre
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different podcast genres create compelling episode titles
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💻</span>
                  Technology Podcast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"Why 90% of AI Startups Fail (And 3 That Didn't)"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">AI startups</Badge>
                      <Badge variant="outline" className="text-xs">entrepreneurship</Badge>
                      <Badge variant="outline" className="text-xs">failure analysis</Badge>
                    </div>
                  </div>
                  <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"The $2B Code Review That Changed Everything"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">code review</Badge>
                      <Badge variant="outline" className="text-xs">software engineering</Badge>
                      <Badge variant="outline" className="text-xs">tech stories</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💼</span>
                  Business Podcast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-purple-50 dark:bg-purple-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"From Broke to $10M: The Pivot That Saved Everything"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">business pivot</Badge>
                      <Badge variant="outline" className="text-xs">success story</Badge>
                      <Badge variant="outline" className="text-xs">entrepreneurship</Badge>
                    </div>
                  </div>
                  <div className="bg-orange-50 dark:bg-orange-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"The Leadership Mistake 80% of CEOs Make"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">leadership</Badge>
                      <Badge variant="outline" className="text-xs">CEO advice</Badge>
                      <Badge variant="outline" className="text-xs">management</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">📚</span>
                  Self-Improvement Podcast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-pink-50 dark:bg-pink-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"The 5AM Club: Why Successful People Wake Up Early"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">morning routine</Badge>
                      <Badge variant="outline" className="text-xs">productivity</Badge>
                      <Badge variant="outline" className="text-xs">success habits</Badge>
                    </div>
                  </div>
                  <div className="bg-teal-50 dark:bg-teal-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"How to Overcome Imposter Syndrome in 30 Days"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">imposter syndrome</Badge>
                      <Badge variant="outline" className="text-xs">confidence</Badge>
                      <Badge variant="outline" className="text-xs">personal growth</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🎭</span>
                  Storytelling Podcast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"The Night I Almost Died (And What It Taught Me)"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">personal story</Badge>
                      <Badge variant="outline" className="text-xs">life lessons</Badge>
                      <Badge variant="outline" className="text-xs">survival</Badge>
                    </div>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-950 p-3 rounded-lg">
                    <p className="font-medium text-sm">"The Secret My Grandmother Never Told Anyone"</p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      <Badge variant="outline" className="text-xs">family secrets</Badge>
                      <Badge variant="outline" className="text-xs">mystery</Badge>
                      <Badge variant="outline" className="text-xs">true story</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about creating compelling podcast titles
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "What makes a podcast title effective?",
                  answer: "Effective podcast titles combine curiosity, clear benefits, and SEO keywords. They should be 40-60 characters, include emotional triggers, and clearly indicate what listeners will gain from the episode."
                },
                {
                  question: "How do SEO keywords help my podcast?",
                  answer: "SEO keywords help your podcast appear in search results on platforms like Apple Podcasts, Spotify, and Google. Including relevant keywords increases discoverability and attracts your target audience."
                },
                {
                  question: "Should I use the exact generated titles?",
                  answer: "The generated titles are optimized starting points. Feel free to customize them to match your brand voice, combine elements from different suggestions, or use them as inspiration for your own variations."
                },
                {
                  question: "How do I test which titles work best?",
                  answer: "A/B test different titles on social media posts, use analytics to track click-through rates, and monitor which episodes get more downloads. Audience feedback can also guide your title strategy."
                },
                {
                  question: "Can I use these titles for other content?",
                  answer: "Absolutely! These titles work great for blog posts, YouTube videos, social media content, and email newsletters. The psychological principles apply across all content marketing."
                },
                {
                  question: "What if my podcast covers multiple topics?",
                  answer: "For multi-topic episodes, focus on the most compelling or unique angle. You can also create titles that hint at variety, like 'Tech News, AI Breakthroughs, and the Future of Work' or use numbered formats."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
