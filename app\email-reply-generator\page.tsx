import { PageLayout } from '@/components/layout/PageLayout'
import { EmailReplyGeneratorClient } from './EmailReplyGeneratorClient'
import type { Metadata } from 'next'

// Email Reply Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Email Reply Generator - Professional Email Assistant | HumanWhisper',
  description: 'Generate professional email replies instantly. Choose tone, add context, get perfect responses. Free AI email assistant for business communication.',
  keywords: [
    'AI email reply',
    'email generator', 
    'professional email',
    'business email',
    'email assistant',
    'email writer',
    'reply generator'
  ],
  openGraph: {
    title: 'AI Email Reply Generator - Professional Email Assistant | HumanWhisper',
    description: 'Generate professional email replies instantly. Choose tone, add context, get perfect responses. Free AI email assistant for business communication.',
    url: '/email-reply-generator',
    type: 'website',
  },
  twitter: {
    title: 'AI Email Reply Generator - Professional Email Assistant | HumanWhisper',
    description: 'Generate professional email replies instantly. Choose tone, add context, get perfect responses. Free AI email assistant for business communication.',
  },
  alternates: {
    canonical: '/email-reply-generator',
  },
}

export default function EmailReplyGeneratorPage() {
  return (
    <PageLayout>
      <EmailReplyGeneratorClient />
    </PageLayout>
  )
}
