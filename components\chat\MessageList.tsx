'use client'

import React, { useRef, useEffect } from 'react'
import { MessageItem } from './MessageItem'
import { WelcomeMessage } from './WelcomeMessage'
import type { Message, AIModel } from '@/types'

interface MessageListProps {
  messages: Message[]
  streamingContent?: string
  isStreaming?: boolean
  currentModel?: AIModel
  className?: string
  onRegenerate?: (messageId: string) => void
}

export function MessageList({
  messages,
  streamingContent = '',
  isStreaming = false,
  currentModel,
  className,
  onRegenerate
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    }
  }

  // 当消息或流式内容更新时自动滚动
  useEffect(() => {
    scrollToBottom()
  }, [messages.length, streamingContent])

  // 如果没有消息，显示欢迎界面
  if (messages.length === 0 && !streamingContent) {
    return (
      <div className={`flex flex-col items-center justify-center h-full p-8 ${className}`}>
        <WelcomeScreen />
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`flex flex-col space-y-4 p-4 h-full overflow-y-auto scroll-smooth ${className}`}
    >
      {/* 渲染历史消息 */}
      {messages.map((message, index) => (
        <MessageItem
          key={message.id}
          message={message}
          isLast={index === messages.length - 1}
          onRegenerate={onRegenerate}
        />
      ))}

      {/* 渲染流式内容 */}
      {streamingContent && (
        <MessageItem
          message={{
            id: 'streaming',
            role: 'assistant',
            content: streamingContent,
            timestamp: new Date(),
            model: currentModel || 'gpt-4.1-nano'
          }}
          isStreaming={isStreaming}
          isLast={true}
        />
      )}

      {/* 滚动锚点 */}
      <div ref={messagesEndRef} className="h-1" />
    </div>
  )
}

// 欢迎屏幕组件
function WelcomeScreen() {
  return <WelcomeMessage />
}
