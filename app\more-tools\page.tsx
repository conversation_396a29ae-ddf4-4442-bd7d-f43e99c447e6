import { PageLayout } from '@/components/layout/PageLayout'
import { MoreToolsClient } from './MoreToolsClient'
import type { Metadata } from 'next'

// More Tools页面SEO优化元数据
export const metadata: Metadata = {
  title: 'More AI Tools - Complete Collection | HumanWhisper',
  description: 'Explore our complete collection of AI-powered tools for content creation, design, marketing, and productivity. Find the perfect tool for your needs.',
  keywords: [
    'AI tools',
    'content creation',
    'design tools',
    'marketing tools',
    'productivity',
    'AI generator',
    'creative tools'
  ],
  openGraph: {
    title: 'More AI Tools - Complete Collection | HumanWhisper',
    description: 'Explore our complete collection of AI-powered tools for content creation, design, marketing, and productivity. Find the perfect tool for your needs.',
    url: '/more-tools',
    type: 'website',
  },
  twitter: {
    title: 'More AI Tools - Complete Collection | HumanWhisper',
    description: 'Explore our complete collection of AI-powered tools for content creation, design, marketing, and productivity. Find the perfect tool for your needs.',
  },
  alternates: {
    canonical: '/more-tools',
  },
}

export default function MoreToolsPage() {
  return (
    <PageLayout>
      <MoreToolsClient />
    </PageLayout>
  )
}
