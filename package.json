{"name": "humanwhisper", "version": "0.1.0", "private": true, "description": "AI that explains complex topics in simple, human-friendly language", "author": "HumanWhisper Team", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "setup": "node scripts/setup.js", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@clerk/nextjs": "^6.25.4", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@tailwindcss/typography": "^0.5.13", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.408.0", "nanoid": "^5.0.7", "next": "^15.4.2", "openai": "^4.52.7", "postcss": "^8.4.40", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.7", "typescript": "^5.5.4", "zustand": "^4.5.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "eslint": "^8.57.0", "eslint-config-next": "15.4.2", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ai", "assistant", "explanation", "simple", "human-friendly", "nextjs", "typescript", "tailwindcss"]}