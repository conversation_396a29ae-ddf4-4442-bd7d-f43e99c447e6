import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>R<PERSON>, <PERSON><PERSON>les, Users, Z<PERSON>, <PERSON>, Heart } from 'lucide-react'
import { FAQ } from '@/components/ui/faq'
import { PageLayout } from '@/components/layout/PageLayout'
import type { Metadata } from 'next'

// 首页SEO优化元数据
export const metadata: Metadata = {
  title: 'Understand Anything with HumanWhisper – Simple, Human-Friendly AI',
  description: 'Struggling with complex topics? HumanWhisper explains them clearly, in everyday language. No jargon, just simple, friendly AI help you can understand.',
  keywords: [
    'simple AI',
    'plain language AI',
    'AI assistant',
    'AI explain simply',
    'human-friendly AI',
    'AI for beginners'
  ],
  openGraph: {
    title: 'Understand Anything with <PERSON>Whisper – Simple, Human-Friendly AI',
    description: 'Struggling with complex topics? HumanWhisper explains them clearly, in everyday language. No jargon, just simple, friendly AI help you can understand.',
    url: '/',
    type: 'website',
  },
  twitter: {
    title: 'Understand Anything with <PERSON><PERSON>his<PERSON> – Simple, Human-Friendly AI',
    description: 'Struggling with complex topics? HumanWhisper explains them clearly, in everyday language. No jargon, just simple, friendly AI help you can understand.',
  },
  alternates: {
    canonical: '/',
  },
}

// 首页组件
export default function HomePage() {
  return (
    <PageLayout>
      {/* 英雄区域 */}
      <section className="py-20 md:py-32 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
          <div className="container text-center">
            <div className="mx-auto max-w-4xl">
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
                <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                  The AI That Whispers Wisdom
                </span>
                <br />
                <span className="text-3xl md:text-4xl text-muted-foreground">
                  in Your Language
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
                <strong>AI that explains complex topics simply</strong> - like having a <strong>patient friend</strong>
                who speaks your language. No jargon, just <strong>clear explanations</strong> that make sense.
              </p>

              {/* AI Network Conundrum Visual - 体现AI网络复杂性与简化解释的对比 */}
              <div className="mt-12 mb-8">
                <Image
                  src="/AI_Network_Conundrum_Visual.png"
                  alt="AI Network Conundrum Visual - Illustrating the complexity of AI networks and the challenge of simplifying complex topics into clear, understandable explanations"
                  width={800}
                  height={400}
                  className="mx-auto rounded-lg shadow-lg opacity-90 hover:opacity-100 transition-opacity duration-300"
                  priority
                />
              </div>

            </div>
          </div>
        </section>

        {/* 特性介绍 */}
        <section className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Why Choose HumanWhisper for Easy Explanations?
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Unlike other AI assistants that bombard you with technical terms,
                HumanWhisper specializes in <strong>plain language explanations</strong>.
                Our <strong>human-friendly AI</strong> breaks down <strong>complex concepts</strong>
                using real-world analogies and examples you can actually relate to.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {/* 特性1 */}
              <div className="text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-whisper-100 dark:bg-whisper-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-whisper-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Clear Explanations, No Jargon</h3>
                <p className="text-muted-foreground">
                  Get answers in simple, everyday language, perfect for non-technical users.
                  Our <strong>plain language AI</strong> makes complex topics accessible to everyone.
                </p>
              </div>

              {/* 特性2 */}
              <div className="text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-human-100 dark:bg-human-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-human-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Understand with Everyday Analogies</h3>
                <p className="text-muted-foreground">
                  Complex topics become clear with examples from daily life, like explaining
                  blockchain as a shared notebook. Our <strong>easy explanation AI</strong>
                  uses relatable comparisons.
                </p>
              </div>

              {/* 特性3 */}
              <div className="text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-whisper-100 dark:bg-whisper-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-whisper-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">No Learning Required</h3>
                <p className="text-muted-foreground">
                  Start chatting immediately - no tutorials, no complex prompts to learn.
                  Just ask your question naturally and get <strong>clear explanations</strong> instantly.
                </p>
              </div>
            </div>

            {/* CTA Button */}
            <div className="text-center mt-12">
              <Link
                href="/chat"
                className="bg-primary text-primary-foreground px-8 py-4 rounded-lg text-lg font-medium hover:bg-primary/90 transition-colors inline-flex items-center gap-2 group"
              >
                Try It Free
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </section>

        {/* 使用示例 */}
        <section className="py-20 bg-muted/30">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Turn Complex Into Crystal Clear
              </h2>
              <p className="text-xl text-muted-foreground">
                See how our <strong>AI that explains like a friend</strong> transforms complex topics
                into crystal clear understanding with simple, step-by-step explanations
              </p>
            </div>

            <div className="max-w-6xl mx-auto">
              <div className="grid md:grid-cols-2 gap-6">
                {/* 示例1: 科技解释 */}
                <div className="bg-card border rounded-lg p-6 shadow-sm">
                  <div className="space-y-4">
                    <div className="flex justify-end">
                      <div className="message-user">
                        Can you explain quantum computing in simple terms?
                      </div>
                    </div>
                    <div className="flex justify-start">
                      <div className="message-assistant">
                        <p className="mb-2">
                          <strong>Simple analogy:</strong> Regular computers are like reading one book at a time. Quantum computers? They read ALL books simultaneously!
                        </p>
                        <p className="mb-2">
                          Here's the key difference: While your laptop processes information step-by-step, quantum computers use "quantum bits" that can be both 0 AND 1 at the same time.
                        </p>
                        <p className="text-sm text-muted-foreground">
                          💡 Want me to explain the practical applications?
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 示例2: 金融解释 */}
                <div className="bg-card border rounded-lg p-6 shadow-sm">
                  <div className="space-y-4">
                    <div className="flex justify-end">
                      <div className="message-user">
                        How does compound interest work?
                      </div>
                    </div>
                    <div className="flex justify-start">
                      <div className="message-assistant">
                        <p className="mb-2">
                          Picture this: You plant a magical money tree 🌱
                        </p>
                        <p className="mb-2">
                          Year 1: Your $100 grows to $110 (10% interest)<br/>
                          Year 2: That $110 becomes $121 (interest on interest!)<br/>
                          Year 3: $121 → $133.10... and it keeps growing faster!
                        </p>
                        <p className="text-sm text-muted-foreground">
                          💰 The magic? Your earnings start earning too!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 示例3: 医学解释 */}
                <div className="bg-card border rounded-lg p-6 shadow-sm">
                  <div className="space-y-4">
                    <div className="flex justify-end">
                      <div className="message-user">
                        How do vaccines work?
                      </div>
                    </div>
                    <div className="flex justify-start">
                      <div className="message-assistant">
                        <p className="mb-2">
                          <em>Let me break this down step by step:</em>
                        </p>
                        <p className="mb-2">
                          1. Your immune system = Body's defense force<br/>
                          2. Vaccine = Training manual with enemy photos<br/>
                          3. When real threat arrives → Instant recognition & defense!
                        </p>
                        <p className="text-sm text-muted-foreground">
                          🏥 It's like a fire drill, but for your immune system!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 示例4: 商业解释 */}
                <div className="bg-card border rounded-lg p-6 shadow-sm">
                  <div className="space-y-4">
                    <div className="flex justify-end">
                      <div className="message-user">
                        What is supply chain management?
                      </div>
                    </div>
                    <div className="flex justify-start">
                      <div className="message-assistant">
                        <p className="mb-2">
                          <strong>Real talk:</strong> Ever wonder how your Amazon package gets to you in 2 days?
                        </p>
                        <p className="mb-2">
                          Supply chain = The invisible network that moves stuff from "made" to "your doorstep." Think warehouses, trucks, planes, and lots of coordination. It's basically the world's most complex game of logistics Tetris! 📦
                        </p>
                        <p className="text-sm text-muted-foreground">
                          🎂 Fun fact: Your phone traveled more than most people!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ部分 */}
        <section className="py-20 bg-muted/30">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Common questions about HumanWhisper
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              <FAQ items={[
                {
                  question: "How is HumanWhisper different from other AI assistants?",
                  answer: "While most AI tools are designed for general tasks, HumanWhisper specializes specifically in explaining complex topics in simple, human-friendly language. We focus on clear explanations with everyday analogies rather than technical jargon."
                },
                {
                  question: "Do I need to learn special prompts or commands?",
                  answer: "Not at all! Just ask your question naturally, like you would ask a friend. HumanWhisper is designed to understand and respond to everyday language without requiring special formatting or prompt engineering."
                },
                {
                  question: "Can it explain topics for different age groups?",
                  answer: "Yes! HumanWhisper can adapt explanations for different understanding levels, from children to adults. You can specify your preferred complexity level in the settings or simply ask for simpler/more detailed explanations."
                },
                {
                  question: "Is my data private and secure?",
                  answer: "Absolutely. We prioritize your privacy with a privacy-first approach. Your conversations are not stored permanently, and we don't use your data to train models or share it with third parties."
                },
                {
                  question: "What topics can HumanWhisper explain?",
                  answer: "HumanWhisper can explain virtually any topic - from science and technology to finance, health, history, and more. If it's complex and you need it simplified, HumanWhisper can help!"
                }
              ]} />
            </div>
          </div>
        </section>

        {/* 示例展示 */}
        <section className="py-20 bg-background">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Perfect for Every Curious Mind
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Whether you're a student, professional, or anyone who asks "Why?" -
                our <strong>conversational AI assistant</strong> adapts to your level and learning style
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* 学习新技能 */}
              <div className="p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Learning New Skills</h3>
                <p className="text-muted-foreground mb-4">
                  "Explain machine learning basics" → Get step-by-step guidance without overwhelming details
                </p>
                <div className="text-sm text-muted-foreground">
                  ✓ Progressive complexity<br/>
                  ✓ Practical examples<br/>
                  ✓ Clear next steps
                </div>
              </div>

              {/* 日常生活疑问 */}
              <div className="p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Everyday Questions</h3>
                <p className="text-muted-foreground mb-4">
                  "Why do interest rates affect house prices?" → Get real-world connections explained simply
                </p>
                <div className="text-sm text-muted-foreground">
                  ✓ Relevant to daily life<br/>
                  ✓ No financial jargon<br/>
                  ✓ Actionable insights
                </div>
              </div>

              {/* 工作相关话题 */}
              <div className="p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Work & Career Topics</h3>
                <p className="text-muted-foreground mb-4">
                  "What is cloud computing for business?" → Get professional explanations you can share with colleagues
                </p>
                <div className="text-sm text-muted-foreground">
                  ✓ Business context<br/>
                  ✓ Professional language<br/>
                  ✓ Team-ready insights
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 行动召唤 */}
        <section className="py-20 bg-primary text-primary-foreground">
          <div className="container text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Simplify Your World with HumanWhisper
            </h2>
            <p className="text-xl mb-8 opacity-90">
              No tech skills needed. Start simplifying complex topics today with our free trial.
            </p>
            <div className="text-center">
              <Link
                href="/chat"
                className="bg-white text-primary px-8 py-4 rounded-lg text-lg font-medium hover:bg-white/90 transition-colors inline-flex items-center gap-2 group"
              >
                Get Started for Free
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
            <div className="text-center mt-6">
              <div className="text-sm opacity-75">
                ✓ No credit card required ✓ Privacy-first approach ✓ Used by 10,000+ learners
              </div>
            </div>
          </div>
        </section>
    </PageLayout>
  )
}
