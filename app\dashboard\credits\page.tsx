import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { CreditsClient } from './CreditsClient'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { PageLayout } from '@/components/layout/PageLayout'

export default async function CreditsPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  return (
    <PageLayout>
      <ErrorBoundary>
        <CreditsClient />
      </ErrorBoundary>
    </PageLayout>
  )
}
