import { TimeGroup } from '@/types'

/**
 * 获取时间分组的显示名称
 */
export const getTimeGroupLabel = (timeGroup: TimeGroup): string => {
  const labels: Record<TimeGroup, string> = {
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    lastWeek: 'Last Week',
    thisMonth: 'This Month',
    older: 'Older'
  }
  return labels[timeGroup]
}

/**
 * 获取时间分组的排序权重（用于排序显示）
 */
export const getTimeGroupWeight = (timeGroup: TimeGroup): number => {
  const weights: Record<TimeGroup, number> = {
    today: 6,
    yesterday: 5,
    thisWeek: 4,
    lastWeek: 3,
    thisMonth: 2,
    older: 1
  }
  return weights[timeGroup]
}

/**
 * 格式化相对时间显示
 */
export const formatRelativeTime = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`

  // 超过一周显示具体日期
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

/**
 * 截断文本并添加省略号
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/**
 * 清理消息内容，移除特殊标记
 */
export const cleanMessageContent = (content: string): string => {
  return content
    .replace(/\[包含 \d+ 张图片\]/g, '') // 移除中文图片标记
    .replace(/\[Contains \d+ images?\]/g, '') // 移除英文图片标记
    .replace(/\[Asked on [^\]]+\]/g, '') // 移除时间戳标记
    .trim()
}

/**
 * 检查是否为今天
 */
export const isToday = (date: Date): boolean => {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

/**
 * 检查是否为昨天
 */
export const isYesterday = (date: Date): boolean => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return date.toDateString() === yesterday.toDateString()
}

/**
 * 获取会话的显示时间
 */
export const getSessionDisplayTime = (date: Date): string => {
  if (isToday(date)) {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isYesterday(date)) {
    return 'Yesterday ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
