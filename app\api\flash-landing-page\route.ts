import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 项目类型映射
const PROJECT_TYPE_MAP: Record<string, string> = {
  'ai_tool': 'AI-powered software, automation tools, and machine learning applications',
  'game': 'video games, mobile games, gaming platforms, and interactive entertainment',
  'community': 'social platforms, forums, networking sites, and community-driven applications',
  'podcast': 'audio content platforms, podcast shows, and media broadcasting services',
  'saas': 'software as a service platforms, business tools, and productivity applications',
  'ecommerce': 'online stores, marketplaces, retail platforms, and shopping experiences',
  'education': 'learning platforms, online courses, educational tools, and training programs',
  'health': 'wellness applications, fitness platforms, health tracking, and medical tools',
  'finance': 'fintech applications, investment platforms, financial tools, and banking services'
}

// 构建Landing Page生成prompt
function buildLandingPagePrompt(
  projectType: string,
  domainName: string,
  painPoint: string,
  launchDate: string
): string {
  const typeDesc = PROJECT_TYPE_MAP[projectType] || projectType

  const prompt = `You are a conversion copywriter and landing page expert specializing in high-converting "coming soon" pages. Your task is to create compelling copy for a new domain launch that captures emails and builds anticipation.

**Project Details:**
- Domain: ${domainName}
- Project Type: ${typeDesc}
- Core Problem: ${painPoint}
- Launch Timeline: ${launchDate}

**Instructions:**
Create a complete "coming soon" landing page package that includes:

1. **Hero Section**: Compelling headline, supporting subheadline, and primary CTA
2. **Email Capture**: Title, input placeholder, button text, and privacy note
3. **Countdown Section**: Title and description for launch countdown
4. **Social Media Teasers**: Posts for Twitter, LinkedIn, and Facebook

**Copy Requirements:**
- **Headline**: 6-10 words, benefit-focused, creates curiosity
- **Subheadline**: 15-25 words, explains the value proposition clearly
- **Email Capture**: Focus on exclusive early access and benefits
- **Social Teasers**: Platform-appropriate length and tone
- **Urgency**: Create FOMO without being pushy
- **Benefit-Driven**: Focus on outcomes, not features

**Tone & Style:**
- Professional yet approachable
- Confident and exciting
- Clear and concise
- Action-oriented
- Trustworthy

**Output Format:**
Return a valid JSON object:

{
  "heroSection": {
    "headline": "Compelling 6-10 word headline",
    "subheadline": "Supporting explanation that builds on the headline",
    "cta": "Primary call-to-action button text"
  },
  "emailCapture": {
    "title": "Email signup section title",
    "placeholder": "Email input placeholder text",
    "buttonText": "Submit button text",
    "privacyNote": "Brief privacy/spam assurance"
  },
  "countdown": {
    "title": "Countdown section title",
    "description": "Description of what's launching when"
  },
  "socialTeasers": {
    "twitter": "Twitter/X post (under 280 characters)",
    "linkedin": "LinkedIn post (professional tone, under 300 words)",
    "facebook": "Facebook post (engaging, under 250 words)"
  }
}

**Key Guidelines:**
- Make the domain name (${domainName}) feel premium and memorable
- Address the pain point (${painPoint}) as the core motivation
- Use the launch date (${launchDate}) to create appropriate urgency
- Ensure all copy is ready-to-use without editing
- Focus on building an email list of qualified prospects
- Make social posts shareable and engaging

Generate the complete landing page copy package now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      projectType,
      domainName,
      painPoint,
      launchDate,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!projectType || !domainName?.trim() || !painPoint?.trim() || !launchDate?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildLandingPagePrompt(
      projectType,
      domainName.trim(),
      painPoint.trim(),
      launchDate.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let landingPage: any
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      landingPage = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!landingPage.heroSection || !landingPage.emailCapture || !landingPage.countdown || !landingPage.socialTeasers) {
        throw new Error('Invalid response structure')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，创建基本结构
      landingPage = {
        heroSection: {
          headline: `${domainName} is Coming Soon`,
          subheadline: `Revolutionary solution for ${painPoint.toLowerCase()}`,
          cta: 'Get Early Access'
        },
        emailCapture: {
          title: 'Be the First to Know',
          placeholder: 'Enter your email address',
          buttonText: 'Notify Me',
          privacyNote: 'We respect your privacy. No spam, ever.'
        },
        countdown: {
          title: `Launching ${launchDate}`,
          description: 'Get ready for something amazing'
        },
        socialTeasers: {
          twitter: `🚀 Something big is coming to ${domainName}! Can't wait to share what we've been building. ${launchDate} can't come soon enough! #ComingSoon`,
          linkedin: `Excited to announce that ${domainName} is launching ${launchDate}! We're building something special to solve ${painPoint.toLowerCase()}. Stay tuned for updates!`,
          facebook: `Big news! ${domainName} is launching ${launchDate}. We've been working hard on something that will change how you think about ${painPoint.toLowerCase()}. Can't wait to share it with you!`
        }
      }
    }

    return NextResponse.json({
      landingPage,
      metadata: {
        projectType,
        domainName,
        launchDate,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate landing page' },
      { status: 500 }
    )
  }
}
