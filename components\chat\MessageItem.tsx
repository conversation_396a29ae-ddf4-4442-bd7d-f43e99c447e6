'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, Check, User, Bo<PERSON>, Clock, Zap, AlertCircle, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { formatRelativeTime, copyToClipboard } from '@/lib/utils'
import { getModelDisplayName } from '@/lib/ai-client'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import type { Message } from '@/types'

interface MessageItemProps {
  message: Message
  isStreaming?: boolean
  isLast?: boolean
  className?: string
  onRegenerate?: ((messageId: string) => void) | undefined
}

export function MessageItem({
  message,
  isStreaming = false,
  isLast = false,
  className,
  onRegenerate
}: MessageItemProps) {
  const [copied, setCopied] = useState(false)
  const [copiedPlain, setCopiedPlain] = useState(false)
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const hasError = message.metadata?.error

  // 复制原始内容（保留Markdown格式）
  const handleCopy = async () => {
    const success = await copyToClipboard(message.content, false)
    if (success) {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  // 复制纯文本（移除Markdown格式）
  const handleCopyPlain = async () => {
    const success = await copyToClipboard(message.content, true)
    if (success) {
      setCopiedPlain(true)
      setTimeout(() => setCopiedPlain(false), 2000)
    }
  }

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} ${className}`}>
      <div className={`flex max-w-[85%] space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
        {/* 头像 */}
        <div className="flex-shrink-0">
          <div className={`flex h-8 w-8 items-center justify-center rounded-full ${
            isUser 
              ? 'bg-primary text-primary-foreground' 
              : hasError
                ? 'bg-destructive text-destructive-foreground'
                : 'bg-muted text-muted-foreground'
          }`}>
            {isUser ? (
              <User className="h-4 w-4" />
            ) : hasError ? (
              <AlertCircle className="h-4 w-4" />
            ) : (
              <Bot className="h-4 w-4" />
            )}
          </div>
        </div>

        {/* 消息内容 */}
        <div className="flex-1 space-y-2">
          {/* 消息头部信息 */}
          <div className={`flex items-center space-x-2 text-xs text-muted-foreground ${
            isUser ? 'justify-end' : 'justify-start'
          }`}>
            <span>{isUser ? 'You' : 'HumanWhisper'}</span>
            
            {/* 模型信息 */}
            {isAssistant && message.model && (
              <Badge variant="outline" className="text-xs">
                {getModelDisplayName(message.model)}
              </Badge>
            )}
            
            {/* 时间戳 */}
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{formatRelativeTime(message.timestamp)}</span>
            </div>
            
            {/* 流式指示器 */}
            {isStreaming && (
              <div className="flex items-center space-x-1 text-primary">
                <div className="animate-pulse">
                  <Zap className="h-3 w-3" />
                </div>
                <span>Generating...</span>
              </div>
            )}
          </div>

          {/* 消息卡片 */}
          <Card className={`p-4 ${
            isUser 
              ? 'bg-primary text-primary-foreground' 
              : hasError
                ? 'bg-destructive/10 border-destructive/20'
                : 'bg-muted/50'
          }`}>
            <div className="space-y-3">
              {/* 图像显示 */}
              {message.images && message.images.length > 0 && (
                <div className="grid grid-cols-2 gap-2 max-w-md">
                  {message.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`Uploaded image ${index + 1}`}
                        className="w-full h-auto rounded-lg border border-border/50 cursor-pointer hover:border-border transition-colors"
                        onClick={() => window.open(image, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* 消息内容 */}
              <div className={`prose prose-sm max-w-none ${isUser ? 'prose-invert' : 'dark:prose-invert'}`}>
                {isUser ? (
                  // 用户消息：简单文本显示
                  <p className="whitespace-pre-wrap break-words text-white">{message.content}</p>
                ) : (
                  // AI消息：Markdown渲染
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeHighlight]}
                    components={{
                      // 自定义代码块样式
                      code: ({ inline, className, children, ...props }: any) => {
                        const match = /language-(\w+)/.exec(className || '')
                        if (inline) {
                          return (
                            <code
                              className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono"
                              {...props}
                            >
                              {children}
                            </code>
                          )
                        }
                        return (
                          <pre className="bg-muted p-4 rounded-lg overflow-x-auto my-4">
                            <code className={match ? `language-${match[1]} text-sm font-mono` : 'text-sm font-mono'} {...props}>
                              {children}
                            </code>
                          </pre>
                        )
                      },
                      // 自定义链接样式
                      a: ({ children, href, ...props }: any) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                          {...props}
                        >
                          {children}
                        </a>
                      ),
                      // 自定义列表样式
                      ul: ({ children, ...props }: any) => (
                        <ul className="list-disc pl-6 space-y-2 my-4" {...props}>
                          {children}
                        </ul>
                      ),
                      li: ({ children, ...props }: any) => (
                        <li className="pl-1" {...props}>
                          {children}
                        </li>
                      ),
                      ol: ({ children, ...props }: any) => (
                        <ol className="list-decimal pl-6 space-y-2 my-4" {...props}>
                          {children}
                        </ol>
                      ),
                      // 自定义表格样式
                      table: ({ children, ...props }: any) => (
                        <div className="overflow-x-auto my-4">
                          <table className="border-collapse border border-border w-full" {...props}>
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({ children, ...props }: any) => (
                        <thead className="bg-muted" {...props}>
                          {children}
                        </thead>
                      ),
                      th: ({ children, ...props }: any) => (
                        <th className="border border-border px-4 py-2 text-left" {...props}>
                          {children}
                        </th>
                      ),
                      td: ({ children, ...props }: any) => (
                        <td className="border border-border px-4 py-2" {...props}>
                          {children}
                        </td>
                      ),
                    }}
                  >
                    {message.content}
                  </ReactMarkdown>
                )}
              </div>

              {/* 错误信息 */}
              {hasError && (
                <div className="text-sm text-destructive bg-destructive/10 p-2 rounded border border-destructive/20">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4" />
                    <span className="font-medium">Error occurred</span>
                  </div>
                  <p className="mt-1 text-xs">{message.metadata?.error}</p>
                </div>
              )}

              {/* 消息元数据 */}
              {message.metadata && !hasError && (
                <div className="flex items-center justify-between text-xs text-muted-foreground border-t pt-2">
                  <div className="flex items-center space-x-4">
                    {message.metadata.tokenCount && (
                      <span>Token: {message.metadata.tokenCount}</span>
                    )}
                    {message.metadata.processingTime && (
                      <span>Time: {message.metadata.processingTime}ms</span>
                    )}
                    {message.metadata.cost && (
                      <span>Cost: ${message.metadata.cost.toFixed(4)}</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* 操作按钮 */}
          <div className={`flex items-center space-x-2 ${
            isUser ? 'justify-end' : 'justify-start'
          }`}>
            {/* 对于AI回复，提供两种复制选项 */}
            {isAssistant ? (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPlain}
                  className="h-8 px-2 text-xs"
                  title="Copy as plain text (remove Markdown formatting)"
                >
                  {copiedPlain ? (
                    <>
                      <Check className="h-3 w-3 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <FileText className="h-3 w-3 mr-1" />
                      Plain
                    </>
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-8 px-2 text-xs"
                  title="Copy raw content (preserve Markdown formatting)"
                >
                  {copied ? (
                    <>
                      <Check className="h-3 w-3 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="h-3 w-3 mr-1" />
                      Raw
                    </>
                  )}
                </Button>
              </>
            ) : (
              /* 对于用户消息，只提供普通复制 */
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                className="h-8 px-2 text-xs"
              >
                {copied ? (
                  <>
                    <Check className="h-3 w-3 mr-1" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </>
                )}
              </Button>
            )}

            {/* 重新生成按钮（仅对AI消息显示） */}
            {isAssistant && isLast && !isStreaming && onRegenerate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRegenerate(message.id)}
                className="h-8 px-2 text-xs"
              >
                <Zap className="h-3 w-3 mr-1" />
                Regenerate
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
