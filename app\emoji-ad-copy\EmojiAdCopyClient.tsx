'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Smile, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Target, Users, MessageSquare, TrendingUp } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 社交媒体平台选项
const PLATFORMS = [
  { id: 'twitter', name: 'Twitter/X', description: 'Short, punchy tweets with trending hashtags', charLimit: 280 },
  { id: 'instagram', name: 'Instagram', description: 'Visual-focused posts with lifestyle appeal', charLimit: 2200 },
  { id: 'tiktok', name: 'Tik<PERSON><PERSON>', description: 'Trendy, youth-oriented content with viral potential', charLimit: 150 },
  { id: 'facebook', name: 'Facebook', description: 'Detailed posts for broader demographics', charLimit: 500 },
  { id: 'linkedin', name: 'LinkedIn', description: 'Professional, business-focused content', charLimit: 700 },
  { id: 'push_notification', name: 'Push Notification', description: 'Mobile app notifications', charLimit: 60 },
  { id: 'email_subject', name: 'Email Subject', description: 'Email marketing subject lines', charLimit: 50 },
  { id: 'youtube', name: 'YouTube', description: 'Video descriptions and community posts', charLimit: 1000 },
  { id: 'custom', name: 'Custom Platform', description: 'Define your own platform requirements', charLimit: null }
]

// 目标人群选项
const TARGET_AUDIENCES = [
  { id: 'gen_z', name: 'Gen Z (18-24)', description: 'Digital natives, trend-conscious, authentic' },
  { id: 'millennials', name: 'Millennials (25-40)', description: 'Tech-savvy, value-driven, career-focused' },
  { id: 'gen_x', name: 'Gen X (41-56)', description: 'Practical, family-oriented, brand loyal' },
  { id: 'professionals', name: 'Business Professionals', description: 'Career-focused, efficiency-minded' },
  { id: 'parents', name: 'Parents', description: 'Family-focused, safety-conscious, time-pressed' },
  { id: 'students', name: 'Students', description: 'Budget-conscious, learning-oriented' },
  { id: 'entrepreneurs', name: 'Entrepreneurs', description: 'Innovation-focused, growth-minded' },
  { id: 'seniors', name: 'Seniors (55+)', description: 'Quality-focused, traditional values' },
  { id: 'custom', name: 'Custom Audience', description: 'Define your own target demographic' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Social media culture, creative expression'
  },
  {
    id: 'Qwen/Qwen3-8B',
    name: 'Qwen3',
    description: 'Multilingual support, platform-specific styles'
  }
]

interface GeneratedAdCopy {
  copy: string
  explanation: string
  emojiCount: number
  characterCount: number
}

export function EmojiAdCopyClient() {
  // 表单状态
  const [platform, setPlatform] = useState('')
  const [customPlatform, setCustomPlatform] = useState('')
  const [productSelling, setProductSelling] = useState('')
  const [targetAudience, setTargetAudience] = useState('')
  const [customAudience, setCustomAudience] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedCopies, setGeneratedCopies] = useState<GeneratedAdCopy[]>([])
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 获取当前平台信息
  const getCurrentPlatform = () => {
    return PLATFORMS.find(p => p.id === platform)
  }

  // 生成广告文案
  const handleGenerate = async () => {
    if (!platform || !productSelling.trim() || !targetAudience) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/emoji-ad-copy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: platform === 'custom' ? customPlatform : platform,
          productSelling: productSelling.trim(),
          targetAudience: targetAudience === 'custom' ? customAudience : targetAudience,
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate ad copies')
      }

      const data = await response.json()
      setGeneratedCopies(data.copies)

      toast({
        title: "Ad Copies Generated!",
        description: `Generated ${data.copies.length} engaging ad copies with emojis.`,
      })

    } catch (error) {
      setError('Failed to generate ad copies. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制文案到剪贴板
  const copyCopy = async (copy: string) => {
    try {
      await navigator.clipboard.writeText(copy)
      toast({
        title: "Copied!",
        description: "Ad copy copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-950 dark:to-yellow-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent">
                AI Emoji Ad Copy
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Social Media Ads
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>engaging social media ad copy with emojis</strong>. 
              Create professional Twitter, Instagram, TikTok ads with perfect emoji placement.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Generation
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Platform Optimized
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Multi-Platform
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Smile className="h-5 w-5 text-primary" />
                      Ad Copy Configuration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Platform Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Social Media Platform *</label>
                      <Select value={platform} onValueChange={setPlatform}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your platform" />
                        </SelectTrigger>
                        <SelectContent>
                          {PLATFORMS.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {item.description}
                                  {item.charLimit && ` • ${item.charLimit} chars`}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Platform Input */}
                    {platform === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Platform *</label>
                        <input
                          type="text"
                          placeholder="Describe your platform and requirements"
                          value={customPlatform}
                          onChange={(e) => setCustomPlatform(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Product Selling Point */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Product Selling Point *</label>
                      <Textarea
                        placeholder="Describe your product's main benefit or unique selling proposition in one sentence..."
                        value={productSelling}
                        onChange={(e) => setProductSelling(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Focus on the key benefit that makes your product stand out
                      </p>
                    </div>

                    {/* Target Audience */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Target Audience *</label>
                      <Select value={targetAudience} onValueChange={setTargetAudience}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your target audience" />
                        </SelectTrigger>
                        <SelectContent>
                          {TARGET_AUDIENCES.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Audience Input */}
                    {targetAudience === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Audience *</label>
                        <input
                          type="text"
                          placeholder="Describe your target demographic"
                          value={customAudience}
                          onChange={(e) => setCustomAudience(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Character Limit Info */}
                    {getCurrentPlatform()?.charLimit && (
                      <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                        <p className="text-sm text-blue-800 dark:text-blue-200">
                          <span className="font-medium">{getCurrentPlatform()?.name}</span> character limit: {getCurrentPlatform()?.charLimit} characters
                        </p>
                      </div>
                    )}

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Ad Copies...
                        </>
                      ) : (
                        <>
                          <Smile className="h-4 w-4 mr-2" />
                          Generate Emoji Ad Copies
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Generated Ad Copies
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedCopies.length > 0 ? (
                      <div className="space-y-4">
                        {generatedCopies.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-950 dark:to-yellow-950 p-3 rounded-lg mb-3">
                                  <p className="text-sm font-medium leading-relaxed">
                                    {item.copy}
                                  </p>
                                </div>
                                <p className="text-xs text-muted-foreground mb-2">{item.explanation}</p>
                                <div className="flex gap-4 text-xs text-muted-foreground">
                                  <span>📊 {item.characterCount} characters</span>
                                  <span>😊 {item.emojiCount} emojis</span>
                                  {getCurrentPlatform()?.charLimit && (
                                    <span className={item.characterCount > getCurrentPlatform()!.charLimit! ? 'text-red-500' : 'text-green-600'}>
                                      {getCurrentPlatform()?.charLimit! - item.characterCount > 0 
                                        ? `${getCurrentPlatform()?.charLimit! - item.characterCount} chars left`
                                        : `${item.characterCount - getCurrentPlatform()!.charLimit!} chars over`
                                      }
                                    </span>
                                  )}
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyCopy(item.copy)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Smile className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your emoji ad copies will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Choose Our Emoji Ad Copy Generator?
            </h2>
            <p className="text-xl text-muted-foreground">
              Create engaging social media ads that stand out and drive conversions
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Smile className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Strategic Emoji Placement</h3>
                <p className="text-sm text-muted-foreground">
                  Perfect emoji integration that enhances emotion without looking childish
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Platform Optimized</h3>
                <p className="text-sm text-muted-foreground">
                  Tailored for each platform's unique style and character limits
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Audience-Targeted</h3>
                <p className="text-sm text-muted-foreground">
                  Copy that resonates with your specific demographic and their preferences
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Conversion-Focused</h3>
                <p className="text-sm text-muted-foreground">
                  Compelling CTAs and persuasive language that drives action
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-yellow-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Get 3 unique ad variations in seconds, ready to post immediately
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Variations</h3>
                <p className="text-sm text-muted-foreground">
                  Different approaches and angles to test what works best for your audience
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create professional emoji ad copy in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Platform</h3>
              <p className="text-sm text-muted-foreground">
                Select your target social media platform from Twitter, Instagram, TikTok, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Describe Product</h3>
              <p className="text-sm text-muted-foreground">
                Input your product's main selling point and unique value proposition
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Select Audience</h3>
              <p className="text-sm text-muted-foreground">
                Choose your target demographic to ensure the copy resonates with them
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Get Ad Copies</h3>
              <p className="text-sm text-muted-foreground">
                Receive 3 unique emoji-enhanced ad copies ready for your campaigns
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Every Platform
            </h2>
            <p className="text-xl text-muted-foreground">
              Optimized ad copy for all major social media platforms
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🐦</span>
                </div>
                <h3 className="font-semibold mb-2">Twitter/X</h3>
                <p className="text-sm text-muted-foreground">
                  Short, punchy tweets with trending hashtags and viral potential
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📸</span>
                </div>
                <h3 className="font-semibold mb-2">Instagram</h3>
                <p className="text-sm text-muted-foreground">
                  Visual-focused posts with lifestyle appeal and storytelling elements
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎵</span>
                </div>
                <h3 className="font-semibold mb-2">TikTok</h3>
                <p className="text-sm text-muted-foreground">
                  Trendy, youth-oriented content with viral potential and Gen Z appeal
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💼</span>
                </div>
                <h3 className="font-semibold mb-2">LinkedIn</h3>
                <p className="text-sm text-muted-foreground">
                  Professional, business-focused content that builds authority and trust
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our emoji ad copy generator
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How does the AI choose which emojis to use?",
                  answer: "Our AI analyzes your product, target audience, and platform to select emojis that enhance emotion and engagement without appearing unprofessional. It considers cultural appropriateness and platform norms."
                },
                {
                  question: "Can I use the generated copy for paid advertising?",
                  answer: "Absolutely! The generated copy is designed for commercial use in paid social media campaigns, organic posts, email marketing, and other advertising channels."
                },
                {
                  question: "What if my copy exceeds the character limit?",
                  answer: "Our system shows character counts and warns when limits are exceeded. You can edit the copy or regenerate with stricter length requirements for your specific platform."
                },
                {
                  question: "How do I know which variation will perform best?",
                  answer: "We provide 3 different approaches - test them through A/B testing on your platform. Each variation targets different emotional triggers and messaging angles."
                },
                {
                  question: "Can I customize the generated copy?",
                  answer: "Yes! Use our generated copy as a starting point and customize it to match your brand voice, add specific details, or adjust the tone as needed."
                },
                {
                  question: "Does the AI understand different target audiences?",
                  answer: "Yes, our AI is trained on demographic preferences and communication styles. It adjusts emoji usage, language tone, and messaging approach based on your selected target audience."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
