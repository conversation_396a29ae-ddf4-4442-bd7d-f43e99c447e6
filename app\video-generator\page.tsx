import { PageLayout } from '@/components/layout/PageLayout'
import { VideoGeneratorClient } from './VideoGeneratorClient'
import type { Metadata } from 'next'

// Video Generator页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Video Generator - Transform Images into Videos | HumanWhisper',
  description: 'Free AI video generator that transforms static images into dynamic videos. Upload your image and create stunning videos instantly.',
  keywords: [
    'AI video generator',
    'image to video',
    'video maker tool',
    'AI video creator',
    'video generation free'
  ],
  openGraph: {
    title: 'AI Video Generator - Transform Images into Videos | HumanWhisper',
    description: 'Free AI video generator that transforms static images into dynamic videos. Upload your image and create stunning videos instantly.',
    url: '/video-generator',
    type: 'website',
  },
  twitter: {
    title: 'AI Video Generator - Transform Images into Videos | HumanWhisper',
    description: 'Free AI video generator that transforms static images into dynamic videos. Upload your image and create stunning videos instantly.',
  },
  alternates: {
    canonical: '/video-generator',
  },
}

export default function VideoGeneratorPage() {
  return (
    <PageLayout>
      <VideoGeneratorClient />
    </PageLayout>
  )
}
