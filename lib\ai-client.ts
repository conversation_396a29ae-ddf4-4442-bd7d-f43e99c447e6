import OpenAI from 'openai'
import { API_CONFIG, AI_MODELS } from './constants'
import type { AIModel, APIRequest, APIResponse, StreamResponse } from '@/types'

/**
 * AIHubMix客户端配置
 * 统一的OpenAI兼容接口，支持多个AI模型
 */
export class AIHubMixClient {
  private client: OpenAI
  private apiKey: string
  private baseURL: string

  constructor() {
    this.apiKey = process.env.AIHUBMIX_API_KEY || ''
    this.baseURL = process.env.AIHUBMIX_BASE_URL || API_CONFIG.baseUrl
    
    if (!this.apiKey) {
      throw new Error('AIHUBMIX_API_KEY environment variable is not set')
    }

    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      timeout: API_CONFIG.timeout,
      maxRetries: API_CONFIG.retryAttempts,
    })
  }

  /**
   * 发送聊天完成请求
   * @param request - API请求参数
   * @returns Promise<APIResponse>
   */
  async chatCompletion(request: APIRequest): Promise<APIResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: this.getTemperatureForModel(request.model),
        max_tokens: this.getMaxTokensForSettings(
          request.settings.responseLength, 
          request.complexityScore // 传入复杂度评分
        ),
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
        stream: false,
      })

      return response as APIResponse
    } catch (error) {
      throw this.handleAPIError(error)
    }
  }

  /**
   * 发送流式聊天完成请求
   * @param request - API请求参数
   * @returns AsyncIterable<StreamResponse>
   */
  async *chatCompletionStream(request: APIRequest): AsyncIterable<StreamResponse> {
    try {
      const stream = await this.client.chat.completions.create({
        model: request.model,
        messages: request.messages,
        temperature: this.getTemperatureForModel(request.model),
        max_tokens: this.getMaxTokensForSettings(
          request.settings.responseLength,
          request.complexityScore // 传入复杂度评分
        ),
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
        stream: true,
      })

      for await (const chunk of stream) {
        yield chunk as StreamResponse
      }
    } catch (error) {
      throw this.handleAPIError(error)
    }
  }

  /**
   * 获取可用模型列表
   * @returns Promise<string[]>
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list()
      return response.data.map(model => model.id)
    } catch (error) {
      // 返回默认支持的模型
      return Object.values(AI_MODELS)
    }
  }

  /**
   * 检查API连接状态
   * @returns Promise<boolean>
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.client.models.list()
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 根据模型获取推荐的温度值
   * @param model - AI模型
   * @returns number
   */
  private getTemperatureForModel(model: AIModel): number {
    const temperatureMap: Record<AIModel, number> = {
      [AI_MODELS.GPT_4_1_NANO]: 0.7,
      [AI_MODELS.CLAUDE_3_5_HAIKU]: 0.6,
      [AI_MODELS.GEMINI_2_0_FLASH]: 0.8,
    }
    
    return temperatureMap[model] || 0.7
  }

  /**
   * 根据响应长度设置和复杂度获取最大token数
   * @param responseLength - 响应长度设置
   * @param complexityScore - 问题复杂度评分 (可选)
   * @returns number
   */
  private getMaxTokensForSettings(responseLength: string, complexityScore?: number): number {
    // 基础token分配
    const enhancedTokenMap: Record<string, number> = {
      'BRIEF': 300,
      'STANDARD': 600,
      'COMPREHENSIVE': 1200,
      'THOROUGH': 2000,
    }

    const baseTokens = enhancedTokenMap[responseLength] || 600

    // 如果没有复杂度评分，返回基础值
    if (!complexityScore) {
      return baseTokens
    }

    // 根据复杂度调整token数量 (传统方式)
    let multiplier = 1.0
    if (complexityScore <= 3) {
      multiplier = 1.0      // 简单问题
    } else if (complexityScore <= 6) {
      multiplier = 1.5      // 中等复杂度
    } else if (complexityScore <= 8) {
      multiplier = 2.0      // 高复杂度
    } else {
      multiplier = 2.5      // 专家级问题
    }

    return Math.round(baseTokens * multiplier)
  }

  /**
   * 处理API错误
   * @param error - 原始错误
   * @returns Error
   */
  private handleAPIError(error: any): Error {
    if (error?.response?.status) {
      const status = error.response.status
      const message = error.response.data?.error?.message || error.message

      switch (status) {
        case 401:
          return new Error('Invalid or expired API key')
        case 429:
          return new Error('Too many requests, please try again later')
        case 500:
          return new Error('AI service temporarily unavailable, please try again later')
        case 503:
          return new Error('AI service is under maintenance')
        default:
          return new Error(`API request failed: ${message}`)
      }
    }

    if (error?.code === 'ECONNREFUSED') {
      return new Error('Unable to connect to AI service, please check your network connection')
    }

    if (error?.code === 'ETIMEDOUT') {
      return new Error('Request timeout, please try again later')
    }

    return new Error(error?.message || 'Unknown error')
  }
}

// 单例实例
let aiClientInstance: AIHubMixClient | null = null

/**
 * 获取AI客户端实例
 * @returns AIHubMixClient
 */
export function getAIClient(): AIHubMixClient {
  if (!aiClientInstance) {
    aiClientInstance = new AIHubMixClient()
  }
  return aiClientInstance
}

/**
 * 验证模型是否受支持
 * @param model - 模型名称
 * @returns boolean
 */
export function isSupportedModel(model: string): model is AIModel {
  return Object.values(AI_MODELS).includes(model as AIModel)
}

/**
 * 获取模型的显示名称
 * @param model - AI模型
 * @returns string
 */
export function getModelDisplayName(model: AIModel): string {
  const displayNames: Record<AIModel, string> = {
    [AI_MODELS.GPT_4_1_NANO]: 'GPT',
    [AI_MODELS.CLAUDE_3_5_HAIKU]: 'Claude',
    [AI_MODELS.GEMINI_2_0_FLASH]: 'Gemini',
  }

  return displayNames[model] || model
}

/**
 * 获取模型的提供商
 * @param model - AI模型
 * @returns string
 */
export function getModelProvider(model: AIModel): string {
  const providers: Record<AIModel, string> = {
    [AI_MODELS.GPT_4_1_NANO]: 'OpenAI',
    [AI_MODELS.CLAUDE_3_5_HAIKU]: 'Anthropic',
    [AI_MODELS.GEMINI_2_0_FLASH]: 'Google',
  }
  
  return providers[model] || 'Unknown'
}
