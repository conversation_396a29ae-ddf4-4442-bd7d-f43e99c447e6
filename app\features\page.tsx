import { PageLayout } from '@/components/layout/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Brain, MessageSquare, Zap, Shield, Globe, Sparkles, ArrowRight, CheckCircle } from 'lucide-react'
import Link from 'next/link'
import type { Metadata } from 'next'
import { FAQ } from '@/components/ui/faq'

// Features页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Features for Simplifying Complex Topics - HumanWhisper',
  description: 'Explore HumanWhisper\'s powerful features like real-time simplification, personalized explanations, and human-friendly AI for easier understanding.',
  keywords: [
    'AI features',
    'simplify with AI',
    'real-time explanations',
    'custom AI tools',
    'human-friendly AI'
  ],
  openGraph: {
    title: 'AI Features for Simplifying Complex Topics - HumanWhisper',
    description: 'Explore HumanWhisper\'s powerful features like real-time simplification, personalized explanations, and human-friendly AI for easier understanding.',
    url: '/features',
    type: 'website',
  },
  twitter: {
    title: 'AI Features for Simplifying Complex Topics - HumanWhisper',
    description: 'Explore HumanWhisper\'s powerful features like real-time simplification, personalized explanations, and human-friendly AI for easier understanding.',
  },
  alternates: {
    canonical: '/features',
  },
}

// 核心特性
const coreFeatures = [
  {
    id: 'nlp',
    icon: Brain,
    title: "Advanced Natural Language Processing",
    description: "Our AI simplification tool leverages cutting-edge NLP technology to understand complex information and transform it into simple, human-friendly explanations."
  },
  {
    id: 'customizable',
    icon: MessageSquare,
    title: "Customizable Explanations",
    description: "Tailor explanations to your needs with adjustable complexity levels, examples, analogies, and language style to match your learning preferences."
  },
  {
    id: 'real-time',
    icon: Zap,
    title: "Real-Time Simplification",
    description: "Get instant simplifications with our streaming response technology, making complex topics immediately accessible without waiting."
  },
  {
    id: 'privacy',
    icon: Shield,
    title: "Privacy Protection",
    description: "Your conversations and data are securely encrypted and protected, ensuring your privacy while using our AI simplification tool."
  },
  {
    id: 'multi-language',
    icon: Globe,
    title: "Multi-Language Support",
    description: "Break language barriers with support for multiple languages, making complex information accessible regardless of your native tongue."
  },
  {
    id: 'premium',
    icon: Sparkles,
    title: "Premium Features",
    description: "Free users can experience our core features. Upgrade for unlimited usage, advanced AI models, and priority processing to solve any complex topic without limits."
  }
]

// 常见问题
const faqs = [
  {
    question: "What is AI simplification?",
    answer: "AI simplification is the process of using artificial intelligence to transform complex, technical, or jargon-heavy information into clear, accessible language that's easy for anyone to understand. HumanWhisper's AI simplification tool analyzes complex content and rewrites it using plain language, helpful analogies, and structured explanations."
  },
  {
    question: "How is HumanWhisper different from other AI tools?",
    answer: "While most AI tools are general-purpose assistants requiring prompt engineering skills, HumanWhisper is specifically designed for simplifying complex information without any special prompting techniques. Our AI is pre-optimized to explain difficult concepts in human-friendly language, uses relatable analogies, and offers customizable explanation styles to match your learning preferences."
  },
  {
    question: "Can I customize how the AI explains things to me?",
    answer: "Absolutely! HumanWhisper offers several customization options: you can adjust the explanation complexity level, request more or fewer examples, enable or disable analogies, and even specify your preferred learning style. This allows you to receive explanations tailored to your unique needs and preferences."
  },
  {
    question: "Is HumanWhisper free to use?",
    answer: "HumanWhisper offers both free and premium options. Free users receive 100 credits upon registration, which can be used with any of our AI models. Premium users enjoy 3600 monthly credits, priority access to all models, and additional features. Our credit-based system ensures everyone can experience our AI simplification tool."
  },
  {
    question: "Do you store my chat history?",
    answer: "Your chat history is only stored locally on your device and is never uploaded to our servers. You can feel completely secure knowing that your conversations remain private, and you can delete your chat history at any time with a single click. We prioritize your privacy and do not access or store your personal conversations."
  }
]

export default function FeaturesPage() {

  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                AI Simplification Tool
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Key Features
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Discover how HumanWhisper's <strong>AI simplification tool</strong> transforms complex information into
              <strong> simple, human-friendly explanations</strong> with advanced features designed for everyone.
            </p>
          </div>
        </div>
      </section>

      {/* How HumanWhisper Simplifies Complex Information */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How HumanWhisper Simplifies Complex Information
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our <strong>AI for simplifying complex topics</strong> uses cutting-edge technology to break down
              difficult concepts into clear, accessible language. Unlike other AI models that require complex prompts
              to achieve clear explanations, HumanWhisper is <strong>specifically designed for simplification</strong>,
              focusing exclusively on making complex information understandable for everyone.
            </p>
          </div>

          {/* Core Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {coreFeatures.map((feature) => (
              <Card key={feature.id} className="h-full border-2 hover:border-primary/20 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-3 bg-primary/10 rounded-lg">
                      <feature.icon className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Advanced NLP Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Advanced Natural Language Processing (NLP)
            </h3>
            <div className="prose prose-lg max-w-none">
              <p className="text-lg text-muted-foreground mb-6">
                At the heart of our <strong>AI simplification tool</strong> lies sophisticated Natural Language Processing
                technology that understands context, nuance, and complexity in human language. Our NLP engine doesn't just
                translate technical terms—it comprehends the underlying concepts and rebuilds them using accessible language.
              </p>

              <div className="grid md:grid-cols-2 gap-8 my-8">
                <div className="bg-card p-6 rounded-lg border">
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Context Understanding
                  </h4>
                  <p className="text-muted-foreground">
                    Our AI analyzes the full context of complex information, ensuring explanations maintain accuracy
                    while becoming more accessible.
                  </p>
                </div>

                <div className="bg-card p-6 rounded-lg border">
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Semantic Analysis
                  </h4>
                  <p className="text-muted-foreground">
                    Advanced semantic processing identifies key concepts and relationships, preserving meaning
                    during simplification.
                  </p>
                </div>
              </div>

              <p className="text-lg text-muted-foreground">
                This makes HumanWhisper one of the <strong>best AI tools for simplifying information</strong>,
                capable of handling everything from scientific papers to legal documents, financial reports to
                technical manuals—all while maintaining the original meaning and accuracy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Customizable Explanations */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Customizable Explanations
            </h3>
            <p className="text-lg text-muted-foreground mb-8">
              Every person learns differently, which is why our <strong>customizable AI explanations</strong>
              adapt to your unique preferences and learning style. Whether you're a visual learner who loves
              analogies or prefer step-by-step breakdowns, HumanWhisper adjusts to match your needs.
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-6 bg-card rounded-lg border">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-6 w-6 text-blue-600" />
                </div>
                <h4 className="font-semibold mb-2">Complexity Levels</h4>
                <p className="text-sm text-muted-foreground">
                  Choose from beginner, intermediate, or advanced explanation levels
                </p>
              </div>

              <div className="text-center p-6 bg-card rounded-lg border">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-6 w-6 text-green-600" />
                </div>
                <h4 className="font-semibold mb-2">Learning Styles</h4>
                <p className="text-sm text-muted-foreground">
                  Enable analogies, examples, or step-by-step breakdowns
                </p>
              </div>

              <div className="text-center p-6 bg-card rounded-lg border">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <h4 className="font-semibold mb-2">Language & Tone</h4>
                <p className="text-sm text-muted-foreground">
                  Adjust formality, cultural context, and communication style
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Real-Time Simplification */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold mb-6">
              Real-Time Simplification
            </h3>
            <p className="text-lg text-muted-foreground mb-8">
              Experience instant understanding with our <strong>real-time AI simplification</strong> technology.
              No waiting, no delays—just immediate access to clear explanations that make complex topics
              instantly accessible.
            </p>

            <div className="bg-card p-8 rounded-lg border mb-8">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-primary/10 rounded-lg">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold text-lg">Streaming Response Technology</h4>
                  <p className="text-muted-foreground">Watch explanations appear in real-time as our AI processes your request</p>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h5 className="font-medium">Speed Benefits:</h5>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Instant response initiation
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Progressive content delivery
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Reduced waiting time
                    </li>
                  </ul>
                </div>

                <div className="space-y-3">
                  <h5 className="font-medium">User Experience:</h5>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Engaging interaction flow
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Natural conversation feel
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      Immediate feedback
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Benefits of Using HumanWhisper
            </h2>
            <p className="text-lg text-muted-foreground mb-12">
              Discover why HumanWhisper is considered one of the <strong>best AI tools for simplifying information</strong>,
              trusted by students, professionals, and curious minds worldwide.
            </p>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Save Time</h3>
                <p className="text-muted-foreground">
                  Get instant understanding instead of spending hours researching complex topics
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Boost Efficiency</h3>
                <p className="text-muted-foreground">
                  Make better decisions faster with clear, actionable explanations
                </p>
              </div>

              <div className="text-center">
                <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Multi-Domain</h3>
                <p className="text-muted-foreground">
                  From science to business, get expert-level explanations in any field
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center">
              Why Choose HumanWhisper for Simplification
            </h2>
            <p className="text-lg text-muted-foreground mb-12 text-center">
              HumanWhisper saves you from learning complex prompts and techniques. While other AI tools require you to
              master prompt engineering, HumanWhisper is <strong>pre-optimized for simplification</strong>, so you can
              focus on understanding rather than crafting the perfect question.
            </p>

            <div className="overflow-x-auto">
              <table className="w-full bg-card rounded-lg border">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-semibold">Feature</th>
                    <th className="text-center p-4 font-semibold">HumanWhisper</th>
                    <th className="text-center p-4 font-semibold">Other AI Tools</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4">Direct Questions, Smart Processing</td>
                    <td className="p-4 text-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mx-auto" />
                    </td>
                    <td className="p-4 text-center text-muted-foreground">Requires Learning</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Pre-optimized for Simplification</td>
                    <td className="p-4 text-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mx-auto" />
                    </td>
                    <td className="p-4 text-center text-muted-foreground">General Purpose</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Consistent Simple Language</td>
                    <td className="p-4 text-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mx-auto" />
                    </td>
                    <td className="p-4 text-center text-muted-foreground">Variable Quality</td>
                  </tr>
                  <tr>
                    <td className="p-4">Ready to Use Immediately</td>
                    <td className="p-4 text-center">
                      <CheckCircle className="h-5 w-5 text-green-600 mx-auto" />
                    </td>
                    <td className="p-4 text-center text-muted-foreground">Setup Required</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Get Started with HumanWhisper
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Experience the power of our <strong>AI simplification tool</strong> today.
            Join thousands who are already simplifying complex information with ease.
          </p>
          <div className="flex justify-center">
            <Link
              href="/chat"
              className="bg-white text-primary px-8 py-4 rounded-lg text-lg font-medium hover:bg-white/90 transition-colors inline-flex items-center gap-2 group"
            >
              Try HumanWhisper Free
              <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
          <p className="text-sm opacity-75 mt-6">
            ✓ No credit card required ✓ Instant access ✓ Multiple AI models
          </p>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground mb-12 text-center">
              Common questions about our AI simplification features
            </p>

            <FAQ items={faqs} />
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
