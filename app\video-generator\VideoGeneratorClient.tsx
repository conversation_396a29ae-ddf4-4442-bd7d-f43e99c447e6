'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Loader2, Upload, Play, Download, Sparkles, Film, Zap, Heart, RefreshCw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import Image from 'next/image'

// 背景选项
const BACKGROUND_OPTIONS = [
  { id: 'starry-sky', name: 'Starry Sky', description: 'Beautiful night sky with stars' },
  { id: 'city-night', name: 'City Night', description: 'Urban nightscape with lights' },
  { id: 'forest', name: 'Forest', description: 'Natural forest environment' },
  { id: 'ocean', name: 'Ocean', description: 'Blue ocean waves' },
  { id: 'abstract', name: 'Abstract', description: 'Geometric abstract background' },
  { id: 'solid-color', name: 'Solid Color', description: 'Simple colored background' },
  { id: 'custom', name: 'Custom', description: 'Describe your own background' }
]

// 主体运动选项
const SUBJECT_MOTIONS = [
  { id: 'rotate-slow', name: 'Slow Rotation', description: 'Gentle rotating movement' },
  { id: 'rotate-fast', name: 'Fast Rotation', description: 'Quick spinning motion' },
  { id: 'dance', name: 'Dancing', description: 'Rhythmic dance movements' },
  { id: 'walk', name: 'Walking', description: 'Natural walking motion' },
  { id: 'fly', name: 'Flying', description: 'Floating or flying movement' },
  { id: 'talk', name: 'Talking', description: 'Speaking or conversation' },
  { id: 'custom', name: 'Custom', description: 'Describe your own motion' }
]

// 镜头运动选项
const CAMERA_MOTIONS = [
  { id: 'none', name: 'Static Camera', description: 'Fixed camera position' },
  { id: 'orbit', name: 'Orbit Around', description: 'Camera circles around subject' },
  { id: 'zoom-in', name: 'Zoom In', description: 'Camera moves closer' },
  { id: 'zoom-out', name: 'Zoom Out', description: 'Camera moves away' },
  { id: 'pan-left', name: 'Pan Left', description: 'Camera moves left' },
  { id: 'pan-right', name: 'Pan Right', description: 'Camera moves right' },
  { id: 'tilt-up', name: 'Tilt Up', description: 'Camera tilts upward' },
  { id: 'tilt-down', name: 'Tilt Down', description: 'Camera tilts downward' }
]

// 环境效果选项
const ENVIRONMENT_EFFECTS = [
  { id: 'light-change', name: 'Light Changes', description: 'Dynamic lighting effects' },
  { id: 'color-change', name: 'Color Changes', description: 'Shifting color palette' },
  { id: 'particles', name: 'Particle Effects', description: 'Floating particles or sparkles' },
  { id: 'smoke', name: 'Smoke Effect', description: 'Misty smoke atmosphere' },
  { id: 'rain', name: 'Rain Effect', description: 'Falling raindrops' },
  { id: 'snow', name: 'Snow Effect', description: 'Gentle snowfall' }
]

// 视觉风格选项
const VISUAL_STYLES = [
  { id: 'realistic', name: 'Realistic', description: 'Photorealistic style' },
  { id: 'tech', name: 'Modern Tech', description: 'Futuristic technology feel' },
  { id: 'cyberpunk', name: 'Cyberpunk', description: 'Neon cyberpunk aesthetic' },
  { id: 'watercolor', name: 'Watercolor', description: 'Artistic watercolor painting' },
  { id: 'cartoon', name: 'Cartoon', description: 'Animated cartoon style' },
  { id: 'oil-painting', name: 'Oil Painting', description: 'Classic oil painting look' }
]

// 情感氛围选项
const EMOTIONAL_TONES = [
  { id: 'none', name: 'None', description: 'No specific mood' },
  { id: 'mysterious', name: 'Mysterious', description: 'Enigmatic and intriguing' },
  { id: 'joyful', name: 'Joyful', description: 'Happy and uplifting' },
  { id: 'tense', name: 'Tense', description: 'Suspenseful and dramatic' },
  { id: 'peaceful', name: 'Peaceful', description: 'Calm and serene' },
  { id: 'epic', name: 'Epic', description: 'Grand and heroic' },
  { id: 'melancholy', name: 'Melancholy', description: 'Sad and reflective' }
]

// 任务状态类型
type TaskStatus = 'PROCESSING' | 'SUCCESS' | 'FAILED' | 'PENDING'

interface VideoResult {
  url: string
  cover_image_url?: string
}

export function VideoGeneratorClient() {
  // 状态管理
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>('')

  // Prompt构建参数
  const [subject, setSubject] = useState('')
  const [background, setBackground] = useState('starry-sky')
  const [customBackground, setCustomBackground] = useState('')
  const [subjectMotion, setSubjectMotion] = useState('rotate-slow')
  const [customSubjectMotion, setCustomSubjectMotion] = useState('')
  const [cameraMotion, setCameraMotion] = useState('orbit')
  const [environmentEffects, setEnvironmentEffects] = useState<string[]>([])
  const [visualStyle, setVisualStyle] = useState('realistic')
  const [emotionalTone, setEmotionalTone] = useState('none')

  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskStatus, setTaskStatus] = useState<TaskStatus>('PENDING')
  const [progress, setProgress] = useState(0)
  const [generatedVideo, setGeneratedVideo] = useState<VideoResult | null>(null)
  const [error, setError] = useState<string>('')
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // 构建prompt的函数
  const buildPrompt = () => {
    let prompt = ''

    // 主体 (必填)
    if (subject.trim()) {
      prompt += subject.trim()
    } else {
      return '' // 主体为空则返回空prompt
    }

    // 背景
    const backgroundText = background === 'custom' ? customBackground.trim() :
      BACKGROUND_OPTIONS.find(b => b.id === background)?.name || ''
    if (backgroundText) {
      prompt += ` in ${backgroundText.toLowerCase()}`
    }

    // 主体运动
    const motionText = subjectMotion === 'custom' ? customSubjectMotion.trim() :
      SUBJECT_MOTIONS.find(m => m.id === subjectMotion)?.name || ''
    if (motionText) {
      prompt += `, ${motionText.toLowerCase()}`
    }

    // 镜头运动
    if (cameraMotion !== 'none') {
      const cameraText = CAMERA_MOTIONS.find(c => c.id === cameraMotion)?.name || ''
      if (cameraText) {
        prompt += `, camera ${cameraText.toLowerCase()}`
      }
    }

    // 环境效果
    if (environmentEffects.length > 0) {
      const effectTexts = environmentEffects.map(effect =>
        ENVIRONMENT_EFFECTS.find(e => e.id === effect)?.name || ''
      ).filter(text => text)
      if (effectTexts.length > 0) {
        prompt += `, ${effectTexts.join(', ').toLowerCase()}`
      }
    }

    // 视觉风格和情感氛围
    const styleText = VISUAL_STYLES.find(s => s.id === visualStyle)?.name || ''
    const toneText = emotionalTone !== 'none' ?
      EMOTIONAL_TONES.find(t => t.id === emotionalTone)?.name || '' : ''

    if (styleText || toneText) {
      prompt += '. Style:'
      if (styleText) prompt += ` ${styleText.toLowerCase()}`
      if (toneText) prompt += ` ${toneText.toLowerCase()}`
    }

    prompt += '.'
    return prompt
  }

  // 处理图片上传
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an image file (JPG, PNG, GIF, etc.)",
        variant: "destructive"
      })
      return
    }

    // 验证文件大小 (10MB限制)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please upload an image smaller than 10MB",
        variant: "destructive"
      })
      return
    }

    setSelectedImage(file)
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
    
    // 清除之前的结果
    setGeneratedVideo(null)
    setError('')
  }

  // 将图片转换为base64
  const imageToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result as string
        // 移除data:image/...;base64,前缀
        const base64Data = base64.split(',')[1]
        if (base64Data) {
          resolve(base64Data)
        } else {
          reject(new Error('Failed to process image'))
        }
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  // 生成视频
  const handleGenerateVideo = async () => {
    if (!selectedImage) {
      toast({
        title: "Image Required",
        description: "Please upload an image first.",
        variant: "destructive"
      })
      return
    }

    if (!subject.trim()) {
      toast({
        title: "Subject Required",
        description: "Please describe what's in your image.",
        variant: "destructive"
      })
      return
    }

    const generatedPrompt = buildPrompt()
    if (!generatedPrompt) {
      toast({
        title: "Invalid Parameters",
        description: "Please fill in the required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    setProgress(0)

    try {
      // 转换图片为base64
      const base64Image = await imageToBase64(selectedImage)
      
      const response = await fetch('/api/video-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: base64Image,
          prompt: generatedPrompt,
          style: visualStyle
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to start video generation')
      }

      const data = await response.json()
      setTaskStatus('PROCESSING')
      
      // 开始轮询任务状态
      pollTaskStatus(data.id)
      
      toast({
        title: "Video Generation Started!",
        description: "Your video is being generated. This may take a few minutes.",
      })
    } catch (error) {
      setError('Failed to generate video. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Failed to start video generation. Please try again.",
        variant: "destructive"
      })
      setIsGenerating(false)
    }
  }

  // 轮询任务状态
  const pollTaskStatus = async (id: string) => {
    const maxAttempts = 60 // 最多轮询5分钟
    let attempts = 0

    const poll = async () => {
      try {
        const response = await fetch(`/api/video-generator/status?taskId=${id}`)
        const data = await response.json()
        
        setTaskStatus(data.task_status)
        setProgress(Math.min(90, (attempts / maxAttempts) * 90))

        if (data.task_status === 'SUCCESS') {
          setGeneratedVideo(data.video_result[0])
          setProgress(100)
          setIsGenerating(false)
          toast({
            title: "Video Generated!",
            description: "Your video has been created successfully.",
          })
          return
        }

        if (data.task_status === 'FAILED') {
          setError('Video generation failed. Please try again.')
          setIsGenerating(false)
          toast({
            title: "Generation Failed",
            description: "Video generation failed. Please try again.",
            variant: "destructive"
          })
          return
        }

        attempts++
        if (attempts < maxAttempts && data.task_status === 'PROCESSING') {
          setTimeout(poll, 5000) // 每5秒轮询一次
        } else if (attempts >= maxAttempts) {
          setError('Video generation timed out. Please try again.')
          setIsGenerating(false)
        }
      } catch (error) {
        setError('Failed to check video status.')
        setIsGenerating(false)
      }
    }

    poll()
  }

  // 下载视频
  const handleDownloadVideo = async () => {
    if (!generatedVideo?.url) return

    try {
      // 通过我们的API代理下载，避免CORS问题
      const response = await fetch('/api/video-generator/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoUrl: generatedVideo.url
        }),
      })

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `generated-video-${Date.now()}.mp4`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: "Downloaded!",
        description: "Video has been saved to your device.",
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download video. Please try again.",
        variant: "destructive"
      })
    }
  }

  // 清除所有内容
  const handleClear = () => {
    setSelectedImage(null)
    setImagePreview('')
    setSubject('')
    setBackground('starry-sky')
    setCustomBackground('')
    setSubjectMotion('rotate-slow')
    setCustomSubjectMotion('')
    setCameraMotion('orbit')
    setEnvironmentEffects([])
    setVisualStyle('realistic')
    setEmotionalTone('none')
    setGeneratedVideo(null)
    setError('')
    setTaskStatus('PENDING')
    setProgress(0)
    setIsGenerating(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                AI Video Generator
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Transform Images into Dynamic Videos
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Upload any image and watch it <strong>come to life</strong> with AI-powered motion. 
              Create stunning videos from static images in minutes.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                AI-Powered
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Film className="h-4 w-4 mr-1" />
                High Quality
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Heart className="h-4 w-4 mr-1" />
                Easy to Use
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                {/* Image Upload */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5 text-primary" />
                      Upload Your Image
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div 
                        className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        {imagePreview ? (
                          <div className="space-y-4">
                            <div className="relative w-full max-w-sm mx-auto">
                              <Image
                                src={imagePreview}
                                alt="Preview"
                                width={400}
                                height={300}
                                className="rounded-lg object-cover w-full h-auto"
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Click to change image
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                            <div>
                              <p className="text-lg font-medium">Upload an image</p>
                              <p className="text-sm text-muted-foreground">
                                JPG, PNG, GIF up to 10MB
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Video Parameters */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Film className="h-5 w-5 text-primary" />
                      Video Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Subject */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">What's in your image? *</label>
                      <Input
                        placeholder="e.g., A woman, A cat, A car, A landscape"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                      />
                    </div>

                    {/* Background */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Background Setting</label>
                      <Select value={background} onValueChange={setBackground}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {BACKGROUND_OPTIONS.map((bg) => (
                            <SelectItem key={bg.id} value={bg.id}>
                              <div>
                                <div className="font-medium">{bg.name}</div>
                                <div className="text-xs text-muted-foreground">{bg.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {background === 'custom' && (
                        <Input
                          placeholder="Describe your custom background"
                          value={customBackground}
                          onChange={(e) => setCustomBackground(e.target.value)}
                          className="mt-2"
                        />
                      )}
                    </div>

                    {/* Subject Motion */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">How should it move?</label>
                      <Select value={subjectMotion} onValueChange={setSubjectMotion}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {SUBJECT_MOTIONS.map((motion) => (
                            <SelectItem key={motion.id} value={motion.id}>
                              <div>
                                <div className="font-medium">{motion.name}</div>
                                <div className="text-xs text-muted-foreground">{motion.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {subjectMotion === 'custom' && (
                        <Input
                          placeholder="Describe the custom motion"
                          value={customSubjectMotion}
                          onChange={(e) => setCustomSubjectMotion(e.target.value)}
                          className="mt-2"
                        />
                      )}
                    </div>

                    {/* Camera Motion */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Camera Movement</label>
                      <Select value={cameraMotion} onValueChange={setCameraMotion}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CAMERA_MOTIONS.map((camera) => (
                            <SelectItem key={camera.id} value={camera.id}>
                              <div>
                                <div className="font-medium">{camera.name}</div>
                                <div className="text-xs text-muted-foreground">{camera.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Prompt Preview */}
                    {subject.trim() && (
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium mb-2">Generated Prompt Preview:</h4>
                        <p className="text-sm text-muted-foreground italic">
                          {buildPrompt() || 'Please fill in the required fields...'}
                        </p>
                      </div>
                    )}

                    <div className="flex gap-3 justify-center">
                      <Button
                        onClick={handleGenerateVideo}
                        disabled={isGenerating || !selectedImage || !subject.trim()}
                        className="px-8"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Generate Video
                          </>
                        )}
                      </Button>
                      <Button variant="outline" onClick={handleClear} className="px-8">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Clear
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                {/* Generation Progress */}
                {isGenerating && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Loader2 className="h-5 w-5 text-primary animate-spin" />
                        Generating Video
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Progress value={progress} className="w-full" />
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">
                            Status: {taskStatus === 'PROCESSING' ? 'Processing your video...' : 'Starting generation...'}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            This usually takes 2-5 minutes
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Error Display */}
                {error && (
                  <Card className="border-destructive">
                    <CardContent className="p-6">
                      <div className="text-center text-destructive">
                        <p className="font-medium">Generation Failed</p>
                        <p className="text-sm mt-1">{error}</p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Generated Video */}
                {generatedVideo && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Play className="h-5 w-5 text-primary" />
                        Your Generated Video
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="bg-muted/30 rounded-lg overflow-hidden">
                          <video 
                            controls 
                            className="w-full h-auto"
                            poster={generatedVideo.cover_image_url}
                          >
                            <source src={generatedVideo.url} type="video/mp4" />
                            Your browser does not support the video tag.
                          </video>
                        </div>
                        <Button 
                          onClick={handleDownloadVideo}
                          className="w-full"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download Video
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Placeholder */}
                {!isGenerating && !generatedVideo && !error && (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <div className="text-muted-foreground">
                        <Film className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Your generated video will appear here</p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              How AI Video Generation Works
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Transform static images into dynamic videos with advanced AI technology in just three simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Upload className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Upload Your Image</h3>
              <p className="text-muted-foreground">
                Upload any image - photos, artwork, or graphics. Our AI works with all image formats.
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Film className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Describe the Motion</h3>
              <p className="text-muted-foreground">
                Tell our AI how you want the image to move. Be specific about the motion and effects you envision.
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <Play className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Get Your Video</h3>
              <p className="text-muted-foreground">
                Watch as AI brings your image to life with realistic motion and download the result instantly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose Our AI Video Generator?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Professional video creation made simple, fast, and completely free
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Completely Free</h3>
              <p className="text-muted-foreground text-sm">
                Generate unlimited videos without any cost or subscription fees
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Lightning Fast</h3>
              <p className="text-muted-foreground text-sm">
                Generate professional videos in just 2-5 minutes with AI acceleration
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Film className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">High Quality Output</h3>
              <p className="text-muted-foreground text-sm">
                Professional-grade videos suitable for social media, marketing, and presentations
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No Skills Required</h3>
              <p className="text-muted-foreground text-sm">
                Anyone can create stunning videos without video editing experience
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Any Image Format</h3>
              <p className="text-muted-foreground text-sm">
                Works with JPG, PNG, GIF and other popular image formats
              </p>
            </div>

            <div className="text-center p-6">
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Download className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Instant Download</h3>
              <p className="text-muted-foreground text-sm">
                Download your generated videos immediately in high quality MP4 format
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Perfect for Every Creative Need
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              From social media content to professional presentations, bring your images to life
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6">
              <div className="h-12 w-12 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-6 w-6 text-pink-600" />
              </div>
              <h3 className="font-semibold mb-2">Social Media</h3>
              <p className="text-sm text-muted-foreground">
                Create engaging posts for Instagram, TikTok, and Facebook
              </p>
            </Card>

            <Card className="text-center p-6">
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Film className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">Marketing</h3>
              <p className="text-sm text-muted-foreground">
                Enhance product photos and promotional materials
              </p>
            </Card>

            <Card className="text-center p-6">
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Play className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Presentations</h3>
              <p className="text-sm text-muted-foreground">
                Make slides more dynamic and engaging
              </p>
            </Card>

            <Card className="text-center p-6">
              <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold mb-2">Personal</h3>
              <p className="text-sm text-muted-foreground">
                Bring memories and artwork to life
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* AI Model Prompt Examples Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                AI Video Model Prompt Formulas
              </h2>
              <p className="text-xl text-muted-foreground">
                Learn from professional prompt structures used by top AI video generation models
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* Sora (OpenAI) Example */}
              <Card className="overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                    Sora Formula (OpenAI)
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">Industry-leading video generation with cinematic quality</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Prompt Formula:</h4>
                    <code className="text-sm text-green-700 dark:text-green-300">
                      "A [shot type] of [subject] [action] in [environment]. [Camera movement]. [Lighting/atmosphere]. [Style/quality modifiers]."
                    </code>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Example Prompt:</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      "A close-up shot of a woman walking through a bustling Tokyo street at night. Camera slowly pulls back. Neon lights reflecting on wet pavement. Cinematic, 35mm film, shallow depth of field."
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Best for:</strong> Cinematic storytelling, complex scenes, photorealistic results
                  </div>
                </CardContent>
              </Card>

              {/* Runway Gen-3 Example */}
              <Card className="overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                    Runway Gen-3 Formula
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">Professional video creation with precise control</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Prompt Formula:</h4>
                    <code className="text-sm text-blue-700 dark:text-blue-300">
                      "[Subject] [detailed action] [environment description]. [Motion specifics]. [Visual style]. [Technical parameters]."
                    </code>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Example Prompt:</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      "Golden retriever running through autumn forest, leaves falling around. Smooth tracking shot following the dog. Warm golden hour lighting. 4K, stable motion, natural color grading."
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Best for:</strong> Professional content creation, marketing videos, smooth motion
                  </div>
                </CardContent>
              </Card>

              {/* Pika Labs Example */}
              <Card className="overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="h-3 w-3 bg-purple-500 rounded-full"></div>
                    Pika Labs Formula
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">Creative video generation with artistic flair</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-2">Prompt Formula:</h4>
                    <code className="text-sm text-purple-700 dark:text-purple-300">
                      "[Scene description] [motion type] [artistic style] [mood/atmosphere] [technical specs]"
                    </code>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Example Prompt:</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      "Watercolor painting of cherry blossoms swaying in breeze, petals floating through air, dreamy pastel colors, soft ethereal mood, artistic animation style"
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Best for:</strong> Artistic videos, creative content, stylized animations
                  </div>
                </CardContent>
              </Card>

              {/* CogVideoX (Current Model) Example */}
              <Card className="overflow-hidden border-2 border-primary">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="h-3 w-3 bg-primary rounded-full"></div>
                    CogVideoX (Our Model)
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">Fast, high-quality video generation optimized for image-to-video</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-primary/10 p-4 rounded-lg">
                    <h4 className="font-medium text-primary mb-2">Prompt Formula:</h4>
                    <code className="text-sm">
                      "[Subject] in [background], [motion description], camera [movement], [environmental effects]. Style: [style] [mood]."
                    </code>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Example Prompt:</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      "Woman in starry sky, slow rotation, camera orbiting around her, light and colors changing with her movement. Style: modern tech mysterious."
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <strong>Best for:</strong> Image-to-video conversion, balanced quality and speed, natural language
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-12 text-center">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-8 rounded-lg max-w-5xl mx-auto">
                <h3 className="text-xl font-semibold mb-6 text-primary">
                  🎯 Universal Video Prompt Best Practices
                </h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm">
                  <div className="text-left">
                    <h4 className="font-semibold text-blue-600 mb-2">📝 Structure</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• Start with main subject</li>
                      <li>• Add specific actions</li>
                      <li>• Include environment</li>
                      <li>• Specify camera work</li>
                    </ul>
                  </div>
                  <div className="text-left">
                    <h4 className="font-semibold text-green-600 mb-2">🎬 Motion</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• Use action verbs</li>
                      <li>• Specify direction</li>
                      <li>• Mention speed/intensity</li>
                      <li>• Add physics details</li>
                    </ul>
                  </div>
                  <div className="text-left">
                    <h4 className="font-semibold text-purple-600 mb-2">🎨 Style</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• Define visual style</li>
                      <li>• Set mood/atmosphere</li>
                      <li>• Specify lighting</li>
                      <li>• Add quality terms</li>
                    </ul>
                  </div>
                  <div className="text-left">
                    <h4 className="font-semibold text-orange-600 mb-2">⚡ Technical</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• Keep prompts focused</li>
                      <li>• Use clear language</li>
                      <li>• Avoid contradictions</li>
                      <li>• Test variations</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
