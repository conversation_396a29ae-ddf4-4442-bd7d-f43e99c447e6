import { PageLayout } from '@/components/layout/PageLayout'
import { EmojiAdCopyClient } from './EmojiAdCopyClient'
import type { Metadata } from 'next'

// Emoji Ad Copy页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Emoji Ad Copy Generator - Social Media Ads | HumanWhisper',
  description: 'Generate engaging social media ad copy with emojis. Create professional Twitter, Instagram, TikTok ads with perfect emoji placement and CTAs.',
  keywords: [
    'emoji ad copy',
    'social media ads',
    'ad copy generator',
    'emoji marketing',
    'social media copy',
    'ad copywriting',
    'emoji ads'
  ],
  openGraph: {
    title: 'AI Emoji Ad Copy Generator - Social Media Ads | HumanWhisper',
    description: 'Generate engaging social media ad copy with emojis. Create professional Twitter, Instagram, TikTok ads with perfect emoji placement and CTAs.',
    url: '/emoji-ad-copy',
    type: 'website',
  },
  twitter: {
    title: 'AI Emoji Ad Copy Generator - Social Media Ads | HumanWhisper',
    description: 'Generate engaging social media ad copy with emojis. Create professional Twitter, Instagram, TikTok ads with perfect emoji placement and CTAs.',
  },
  alternates: {
    canonical: '/emoji-ad-copy',
  },
}

export default function EmojiAdCopyPage() {
  return (
    <PageLayout>
      <EmojiAdCopyClient />
    </PageLayout>
  )
}
