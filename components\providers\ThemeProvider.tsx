'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useSettingsStore } from '@/store/settings-store'
import { THEMES } from '@/lib/constants'
import type { Theme } from '@/types'

// 主题上下文类型
interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  resolvedTheme: 'light' | 'dark'
  systemTheme: 'light' | 'dark'
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 主题提供者组件
interface ThemeProviderProps {
  children: React.ReactNode
  storageKey?: string
  attribute?: string
  disableTransitionOnChange?: boolean
}

export function ThemeProvider({
  children,
  attribute = 'class',
  disableTransitionOnChange = false
}: ThemeProviderProps) {
  const { theme, setTheme: setStoreTheme } = useSettingsStore()
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light')
  const [mounted, setMounted] = useState(false)

  // 获取系统主题
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light'
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  // 计算解析后的主题
  const resolvedTheme = theme === THEMES.SYSTEM ? systemTheme : theme as 'light' | 'dark'

  // 应用主题到DOM
  const applyTheme = (newTheme: 'light' | 'dark') => {
    if (typeof window === 'undefined') return

    const root = document.documentElement

    if (disableTransitionOnChange) {
      const css = document.createElement('style')
      css.appendChild(
        document.createTextNode(
          `*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`
        )
      )
      document.head.appendChild(css)

      return () => {
        // Force repaint
        ;(() => window.getComputedStyle(document.body))()

        // Wait for next tick before removing
        setTimeout(() => {
          document.head.removeChild(css)
        }, 1)
      }
    }

    if (attribute === 'class') {
      root.classList.remove('light', 'dark')
      root.classList.add(newTheme)
    } else {
      root.setAttribute(attribute, newTheme)
    }

    return undefined
  }

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    setStoreTheme(newTheme)
  }

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = () => {
      const newSystemTheme = getSystemTheme()
      setSystemTheme(newSystemTheme)
    }

    // 初始化系统主题
    setSystemTheme(getSystemTheme())

    // 监听变化
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // 应用主题变化
  useEffect(() => {
    if (!mounted) return
    
    const cleanup = applyTheme(resolvedTheme)
    return cleanup
  }, [resolvedTheme, mounted])

  // 组件挂载后设置mounted状态
  useEffect(() => {
    setMounted(true)
  }, [])

  // 初始主题应用
  useEffect(() => {
    if (mounted) {
      applyTheme(resolvedTheme)
    }
  }, [mounted])

  const value: ThemeContextType = {
    theme,
    setTheme,
    resolvedTheme,
    systemTheme
  }

  // 防止hydration不匹配，在mounted之前不渲染主题相关内容
  if (!mounted) {
    return (
      <ThemeContext.Provider value={value}>
        <div suppressHydrationWarning>
          {children}
        </div>
      </ThemeContext.Provider>
    )
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

// 使用主题的Hook
export function useTheme() {
  const context = useContext(ThemeContext)
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  
  return context
}

// 主题切换组件
export function ThemeToggle({ className }: { className?: string }) {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    if (theme === THEMES.SYSTEM) {
      setTheme(resolvedTheme === 'dark' ? THEMES.LIGHT : THEMES.DARK)
    } else if (theme === THEMES.LIGHT) {
      setTheme(THEMES.DARK)
    } else {
      setTheme(THEMES.LIGHT)
    }
  }

  // 防止hydration不匹配
  if (!mounted) {
    return (
      <button
        className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${className}`}
        aria-label="Switch Theme"
        suppressHydrationWarning
      >
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      </button>
    )
  }

  return (
    <button
      onClick={toggleTheme}
      className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-10 w-10 ${className}`}
      aria-label="Switch Theme"
    >
      {resolvedTheme === 'dark' ? (
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      ) : (
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
      )}
    </button>
  )
}
