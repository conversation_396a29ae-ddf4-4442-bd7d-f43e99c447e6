import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { DashboardClient } from './DashboardClient'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { PageLayout } from '@/components/layout/PageLayout'
import type { Metadata } from 'next'

// Dashboard页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Your AI Dashboard for Simplification - HumanWhisper',
  description: 'Access your HumanWhisper dashboard to simplify texts, get tailored AI explanations, and manage your settings with ease.',
  keywords: [
    'AI dashboard',
    'simplify text online',
    'AI settings',
    'HumanWhisper control panel',
    'personalized AI'
  ],
  openGraph: {
    title: 'Your AI Dashboard for Simplification - HumanWhisper',
    description: 'Access your HumanWhisper dashboard to simplify texts, get tailored AI explanations, and manage your settings with ease.',
    url: '/dashboard',
    type: 'website',
  },
  twitter: {
    title: 'Your AI Dashboard for Simplification - HumanWhisper',
    description: 'Access your HumanWhisper dashboard to simplify texts, get tailored AI explanations, and manage your settings with ease.',
  },
  alternates: {
    canonical: '/dashboard',
  },
}

export default async function DashboardPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  return (
    <PageLayout>
      <ErrorBoundary>
        <DashboardClient />
      </ErrorBoundary>
    </PageLayout>
  )
}
