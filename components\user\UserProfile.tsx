'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'
import { Coins, Crown, User } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

interface UserStats {
  plan: 'free' | 'premium'
  credits: number
  usedCredits: number
  totalCreditsEarned: number
  subscriptionPeriodEnd?: string
}

export function UserProfile() {
  const { user } = useUser()
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch('/api/user/stats')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        }
      } catch (error) {
        console.error('Failed to fetch user stats:', error)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      fetchUserStats()
    }
  }, [user])

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="p-6 text-center text-muted-foreground">
          Unable to load user information
        </CardContent>
      </Card>
    )
  }

  const remainingCredits = stats.credits - stats.usedCredits
  const isPremium = stats.plan === 'premium'

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </CardTitle>
          <Badge
            variant={isPremium ? "default" : "secondary"}
            className={isPremium ? "bg-gradient-to-r from-purple-500 to-blue-500" : ""}
          >
            {isPremium ? (
              <div className="flex items-center gap-1">
                <Crown className="h-3 w-3" />
                Premium
              </div>
            ) : (
              "Free User"
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* User Basic Information */}
        <div className="space-y-2">
          <p className="text-sm font-medium">{user?.fullName || user?.firstName || "User"}</p>
          <p className="text-xs text-muted-foreground">{user?.primaryEmailAddress?.emailAddress}</p>
        </div>

        <Separator />

        {/* Credits Information */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Coins className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Available Credits</span>
            </div>
            <span className="text-lg font-bold text-primary">
              {remainingCredits}
            </span>
          </div>

          <div className="grid grid-cols-2 gap-4 text-xs">
            <div className="text-center p-2 bg-muted rounded">
              <p className="text-muted-foreground">Total Earned</p>
              <p className="font-medium">{stats.totalCreditsEarned}</p>
            </div>
            <div className="text-center p-2 bg-muted rounded">
              <p className="text-muted-foreground">Used</p>
              <p className="font-medium">{stats.usedCredits}</p>
            </div>
          </div>
        </div>



        {/* Premium Information */}
        {isPremium && stats.subscriptionPeriodEnd && (
          <>
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Crown className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Premium Member</span>
              </div>
              <p className="text-xs text-muted-foreground">
                Expires: {new Date(stats.subscriptionPeriodEnd).toLocaleDateString()}
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
