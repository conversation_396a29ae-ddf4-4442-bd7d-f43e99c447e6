'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Mail, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, BookOpen, Users, Target, Clock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 邮件场景选项
const EMAIL_SCENARIOS = [
  { id: 'job_application', name: 'Job Application Reply', description: 'Responding to job opportunities' },
  { id: 'complaint_response', name: 'Complaint Response', description: 'Addressing customer complaints' },
  { id: 'collaboration', name: 'Collaboration Proposal', description: 'Partnership and collaboration' },
  { id: 'invoice_reminder', name: 'Invoice Reminder', description: 'Payment and billing follow-up' },
  { id: 'event_invitation', name: 'Event Invitation', description: 'Meeting and event responses' },
  { id: 'leave_request', name: 'Leave Request', description: 'Time off and absence requests' },
  { id: 'thank_you', name: 'Thank You Note', description: 'Appreciation and gratitude' },
  { id: 'apology', name: 'Apology Letter', description: 'Sincere apologies and corrections' },
  { id: 'follow_up', name: 'Follow-up Email', description: 'Project and meeting follow-ups' },
  { id: 'custom', name: 'Custom Scenario', description: 'Define your own scenario' }
]

// 语气风格选项
const TONE_STYLES = [
  { id: 'formal', name: 'Formal', description: 'Professional and official tone' },
  { id: 'friendly', name: 'Friendly', description: 'Warm and approachable tone' },
  { id: 'humorous', name: 'Humorous', description: 'Light-hearted with appropriate humor' },
  { id: 'firm_decline', name: 'Firm Decline', description: 'Polite but decisive rejection' },
  { id: 'enthusiastic', name: 'Enthusiastic', description: 'Positive and energetic tone' },
  { id: 'diplomatic', name: 'Diplomatic', description: 'Tactful and careful wording' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM',
    description: 'Professional tone, business communication'
  },
  {
    id: 'Qwen/Qwen3-8B',
    name: 'Qwen3',
    description: 'Complex logic, international business'
  }
]

export function EmailReplyGeneratorClient() {
  // 表单状态
  const [scenario, setScenario] = useState('')
  const [customScenario, setCustomScenario] = useState('')
  const [toneStyle, setToneStyle] = useState('')
  const [originalEmail, setOriginalEmail] = useState('')
  const [keyPoints, setKeyPoints] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedReply, setGeneratedReply] = useState('')
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成邮件回复
  const handleGenerate = async () => {
    if (!scenario || !toneStyle || !originalEmail.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/email-reply-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scenario: scenario === 'custom' ? customScenario : scenario,
          toneStyle,
          originalEmail: originalEmail.trim(),
          keyPoints: keyPoints.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate email reply')
      }

      const data = await response.json()
      setGeneratedReply(data.emailReply)

      toast({
        title: "Email Reply Generated!",
        description: "Your professional email reply is ready.",
      })

    } catch (error) {
      console.error('Generation error:', error)
      setError('Failed to generate email reply. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedReply)
      toast({
        title: "Copied!",
        description: "Email reply copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                AI Email Reply Generator
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Professional Email Assistant
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>professional email replies</strong> instantly. Choose your tone, 
              add context, and get perfect responses for any business situation.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Generation
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Professional Quality
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Multiple Languages
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5 text-primary" />
                      Email Context
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Email Scenario */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Email Scenario *</label>
                      <Select value={scenario} onValueChange={setScenario}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose email scenario" />
                        </SelectTrigger>
                        <SelectContent>
                          {EMAIL_SCENARIOS.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Scenario Input */}
                    {scenario === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Scenario *</label>
                        <input
                          type="text"
                          placeholder="Describe your email scenario"
                          value={customScenario}
                          onChange={(e) => setCustomScenario(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Tone Style */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Tone Style *</label>
                      <Select value={toneStyle} onValueChange={setToneStyle}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose tone style" />
                        </SelectTrigger>
                        <SelectContent>
                          {TONE_STYLES.map((tone) => (
                            <SelectItem key={tone.id} value={tone.id}>
                              <div>
                                <div className="font-medium">{tone.name}</div>
                                <div className="text-xs text-muted-foreground">{tone.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Original Email */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Original Email *</label>
                      <Textarea
                        placeholder="Paste the email you received here..."
                        value={originalEmail}
                        onChange={(e) => setOriginalEmail(e.target.value)}
                        className="min-h-[120px] resize-none"
                      />
                    </div>

                    {/* Key Points */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Additional Context</label>
                      <Textarea
                        placeholder="Add key points, policies, or specific information to include in your reply..."
                        value={keyPoints}
                        onChange={(e) => setKeyPoints(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Reply...
                        </>
                      ) : (
                        <>
                          <Mail className="h-4 w-4 mr-2" />
                          Generate Email Reply
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-primary" />
                        Generated Email Reply
                      </span>
                      {generatedReply && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copyToClipboard}
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedReply ? (
                      <div className="bg-muted/50 p-4 rounded-lg">
                        <pre className="whitespace-pre-wrap text-sm font-mono">
                          {generatedReply}
                        </pre>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your generated email reply will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Choose Our AI Email Assistant?
            </h2>
            <p className="text-xl text-muted-foreground">
              Professional email communication made simple with AI-powered assistance
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Generate professional email replies in seconds, not minutes
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Professional Quality</h3>
                <p className="text-sm text-muted-foreground">
                  AI-crafted responses that maintain professional standards
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Languages</h3>
                <p className="text-sm text-muted-foreground">
                  Support for 100+ languages for international communication
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Context-Aware</h3>
                <p className="text-sm text-muted-foreground">
                  Understands email context and generates appropriate responses
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Tone Customization</h3>
                <p className="text-sm text-muted-foreground">
                  Choose from multiple tone styles to match your communication needs
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">No Learning Curve</h3>
                <p className="text-sm text-muted-foreground">
                  Simple form-based interface - no prompt engineering required
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Generate professional email replies in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Scenario</h3>
              <p className="text-sm text-muted-foreground">
                Select the type of email you're responding to from our predefined scenarios
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Set Tone</h3>
              <p className="text-sm text-muted-foreground">
                Pick the appropriate tone style - formal, friendly, diplomatic, or others
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Add Context</h3>
              <p className="text-sm text-muted-foreground">
                Paste the original email and add any specific points you want to address
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Generate & Copy</h3>
              <p className="text-sm text-muted-foreground">
                Get your professional email reply instantly and copy it to use
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Email Writing Guide Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Professional Email Writing Guide
              </h2>
              <p className="text-xl text-muted-foreground">
                Master the art of professional email communication with our comprehensive guide
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              {/* Email Structure */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    Email Structure Formula
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-blue-600">1</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Subject Line</h4>
                        <p className="text-xs text-muted-foreground">Clear, specific, action-oriented</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-green-600">2</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Greeting</h4>
                        <p className="text-xs text-muted-foreground">Professional salutation</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-purple-600">3</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Purpose Statement</h4>
                        <p className="text-xs text-muted-foreground">Why you're writing</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-orange-600">4</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Main Content</h4>
                        <p className="text-xs text-muted-foreground">Details, context, information</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-red-600">5</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Call to Action</h4>
                        <p className="text-xs text-muted-foreground">What you need from them</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="h-6 w-6 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-bold text-indigo-600">6</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">Professional Closing</h4>
                        <p className="text-xs text-muted-foreground">Sign-off and signature</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Subject Line Examples */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-primary" />
                    Subject Line Examples
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-sm text-green-600 mb-1">✅ Good Examples</h4>
                      <div className="space-y-1 text-xs">
                        <p className="bg-green-50 dark:bg-green-950 p-2 rounded">"Meeting Request: Q4 Budget Review - March 15"</p>
                        <p className="bg-green-50 dark:bg-green-950 p-2 rounded">"Action Required: Contract Approval by Friday"</p>
                        <p className="bg-green-50 dark:bg-green-950 p-2 rounded">"Follow-up: Marketing Campaign Proposal"</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-red-600 mb-1">❌ Poor Examples</h4>
                      <div className="space-y-1 text-xs">
                        <p className="bg-red-50 dark:bg-red-950 p-2 rounded">"Hi"</p>
                        <p className="bg-red-50 dark:bg-red-950 p-2 rounded">"Quick question"</p>
                        <p className="bg-red-50 dark:bg-red-950 p-2 rounded">"Important!!!"</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Email Templates */}
            <div className="grid md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-primary" />
                    Business Request
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                    <p><strong>Subject:</strong> Request for [Specific Item/Action]</p>
                    <p><strong>Greeting:</strong> Dear [Name]/Hello [Name],</p>
                    <p><strong>Purpose:</strong> I am writing to request...</p>
                    <p><strong>Details:</strong> [Context and specifics]</p>
                    <p><strong>Action:</strong> Could you please...</p>
                    <p><strong>Closing:</strong> Thank you for your time.</p>
                    <p><strong>Sign-off:</strong> Best regards, [Your name]</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-primary" />
                    Meeting Invitation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                    <p><strong>Subject:</strong> Meeting Invitation: [Topic] - [Date]</p>
                    <p><strong>Greeting:</strong> Hi [Name],</p>
                    <p><strong>Purpose:</strong> I would like to schedule a meeting...</p>
                    <p><strong>Details:</strong> Date, time, location/link</p>
                    <p><strong>Agenda:</strong> We will discuss...</p>
                    <p><strong>Action:</strong> Please confirm your availability</p>
                    <p><strong>Sign-off:</strong> Looking forward to our discussion</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Follow-up Email
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted/50 p-3 rounded-lg text-xs space-y-2">
                    <p><strong>Subject:</strong> Follow-up: [Previous Topic]</p>
                    <p><strong>Greeting:</strong> Hello [Name],</p>
                    <p><strong>Reference:</strong> Following our [meeting/conversation]...</p>
                    <p><strong>Summary:</strong> Key points discussed</p>
                    <p><strong>Next Steps:</strong> Action items and deadlines</p>
                    <p><strong>Action:</strong> Please let me know if...</p>
                    <p><strong>Sign-off:</strong> Best regards, [Your name]</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Professional Tips */}
            <div className="mt-12">
              <h3 className="text-2xl font-bold text-center mb-8">Professional Email Tips</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Be Specific</h4>
                  <p className="text-sm text-muted-foreground">Clear subject lines and specific requests get better responses</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Clock className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Be Concise</h4>
                  <p className="text-sm text-muted-foreground">Keep emails brief and to the point for busy professionals</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Know Your Audience</h4>
                  <p className="text-sm text-muted-foreground">Adjust tone and formality based on your relationship</p>
                </div>
                <div className="text-center">
                  <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-6 w-6 text-orange-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Proofread</h4>
                  <p className="text-sm text-muted-foreground">Always review for grammar, spelling, and clarity</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our AI email reply generator
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "Is the AI email generator really free?",
                  answer: "Yes, our AI email reply generator is completely free to use. No registration, no hidden fees, no limits on usage."
                },
                {
                  question: "What types of emails can I reply to?",
                  answer: "Our tool supports various email scenarios including job applications, complaints, collaborations, invoices, events, leave requests, thank you notes, apologies, and more."
                },
                {
                  question: "Can I customize the tone of the reply?",
                  answer: "Absolutely! Choose from formal, friendly, humorous, diplomatic, enthusiastic, or firm decline tones to match your communication style."
                },
                {
                  question: "Is my email data secure and private?",
                  answer: "We prioritize your privacy. Email content is processed only for generation purposes and is not stored or shared with third parties."
                },
                {
                  question: "What languages are supported?",
                  answer: "Our AI models support multiple languages, making it perfect for international business communication and multilingual email exchanges."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
