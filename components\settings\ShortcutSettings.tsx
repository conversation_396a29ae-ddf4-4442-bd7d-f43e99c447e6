'use client'

import React from 'react'
import { Keyboard, Command } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useSettingsStore } from '@/store/settings-store'

export function ShortcutSettings() {
  const { shortcuts } = useSettingsStore()

  const shortcutGroups = [
    {
      title: 'Chat Operations',
      shortcuts: [
        { key: 'send', label: 'Send Message', description: 'Send the current input message' },
        { key: 'newLine', label: 'New Line', description: 'Insert a new line in the message' },
        { key: 'focusInput', label: 'Focus Input', description: 'Quickly focus on the message input box' },
      ]
    },
    {
      title: 'Interface Control',
      shortcuts: [
        { key: 'newChat', label: 'New Chat', description: 'Start a new conversation' },
        { key: 'clearChat', label: 'Clear Chat', description: 'Clear current conversation content' },
        { key: 'toggleSidebar', label: 'Toggle Sidebar', description: 'Show or hide conversation history' },
        { key: 'toggleSettings', label: 'Open Settings', description: 'Open the settings panel' },
      ]
    },
    {
      title: 'Data Operations',
      shortcuts: [
        { key: 'exportChat', label: 'Export Chat', description: 'Export current conversation as file' },
      ]
    }
  ]

  return (
    <div className="space-y-6">
      {/* Keyboard Shortcuts */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Keyboard className="h-5 w-5 text-blue-500" />
            <CardTitle>Keyboard Shortcuts</CardTitle>
          </div>
          <CardDescription>
            Use shortcuts to improve operation efficiency
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {shortcutGroups.map((group, index) => (
              <div key={index}>
                <h3 className="text-sm font-medium mb-3">{group.title}</h3>
                <div className="space-y-2">
                  {group.shortcuts.map((shortcut) => (
                    <div key={shortcut.key} className="flex items-center justify-between py-2">
                      <div className="space-y-1">
                        <div className="text-sm font-medium">{shortcut.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {shortcut.description}
                        </div>
                      </div>
                      <Badge variant="outline" className="font-mono">
                        {shortcuts[shortcut.key] || 'N/A'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Shortcuts (Coming Soon) */}
      <Card className="opacity-60">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Command className="h-5 w-5 text-purple-500" />
            <CardTitle>Custom Shortcuts</CardTitle>
          </div>
          <CardDescription>
            Customize your shortcut combinations (coming soon)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Custom shortcut functionality is under development, stay tuned
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
