'use client'

import { PageLayout } from '@/components/layout/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Check, Sparkles, Zap, Shield, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'
import { useAuth } from '@clerk/nextjs'

// Pricing页面组件

const plans = [
  {
    id: 'free',
    name: 'Free',
    price: '$0',
    period: 'forever',
    description: 'Perfect for trying out HumanWhisper',
    credits: '100 credits',
    features: [
      'Access to all AI models',
      'Basic simplification features',
      'Chat history (local storage)',
      'No credit card required'
    ],
    buttonText: 'Get Started Free',
    buttonVariant: 'outline' as const,
    href: '/chat',
    popular: false
  },
  {
    id: 'premium-monthly',
    name: 'Premium Monthly',
    price: '$29.99',
    period: 'per month',
    description: 'Generous limits designed to meet professional needs',
    credits: '3,600 credits/month',
      features: [
        'High-speed access to all AI models',
        'Advanced customization options',
        'Extended chat history',
        'Early access to new features',
        'Export conversations',
        'Custom explanation styles'
      ],
      buttonText: 'Upgrade to Premium',
      buttonVariant: 'default' as const,
      href: '/api/payments/create-checkout',
      popular: true
    }
  ]

export default function PricingPage() {
  const { isSignedIn } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  // 处理升级按钮点击
  const handleUpgrade = async () => {
    if (!isSignedIn) {
      // 如果未登录，跳转到登录页面
      window.location.href = '/sign-in?redirect_url=/pricing'
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/payments/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success && data.checkout_url) {
        // 跳转到支付页面
        window.location.href = data.checkout_url
      } else {
        throw new Error(data.error || 'Failed to create checkout session')
      }
    } catch (error) {
      // 使用简单的alert替代toast
      alert('Failed to start payment process. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Flexible AI Pricing Plans
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Choose the perfect plan for your simplification needs. Start free, upgrade when you're ready.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative h-full ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary text-primary-foreground px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      Most Popular
                    </div>
                  </div>
                )}
                
                <CardHeader className="text-center pb-8">
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">{plan.price}</span>
                    <span className="text-muted-foreground ml-2">/{plan.period}</span>
                  </div>
                  <p className="text-muted-foreground mt-2">{plan.description}</p>
                  <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-center gap-2">
                      <Zap className="h-5 w-5 text-primary" />
                      <span className="font-semibold">{plan.credits}</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <Check className="h-5 w-5 text-green-600 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <div className="pt-6">
                    {plan.name === 'Free' ? (
                      <Link href="/chat">
                        <Button
                          variant={plan.buttonVariant}
                          size="lg"
                          className="w-full"
                        >
                          {plan.buttonText}
                        </Button>
                      </Link>
                    ) : (
                      <Button
                        variant={plan.buttonVariant}
                        size="lg"
                        className="w-full"
                        onClick={handleUpgrade}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          plan.buttonText
                        )}
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Security Notice */}
      <section className="py-12 bg-muted/30">
        <div className="container text-center">
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <Shield className="h-5 w-5" />
            <span>🔒 Secure checkout protected by Creem</span>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold mb-2">What are credits and how do they work?</h3>
                <p className="text-muted-foreground">
                  Credits are used to access our AI models. Different models consume different amounts of credits per request. 
                  Free users get 100 credits to start, while Premium members get 3,600 credits monthly.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Can I cancel my Premium subscription anytime?</h3>
                <p className="text-muted-foreground">
                  Yes, you can cancel your Premium subscription at any time. You'll continue to have Premium access until the end of your current billing period.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Do unused credits roll over to the next month?</h3>
                <p className="text-muted-foreground">
                  Premium credits reset each month and don't roll over. This ensures you always have fresh credits and encourages regular use of the service.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">Is my payment information secure?</h3>
                <p className="text-muted-foreground">
                  Absolutely. All payments are processed securely through Creem, our trusted payment partner. 
                  We never store your payment information on our servers.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Simplify Complex Topics?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Start with our free plan and experience the power of AI simplification. 
            Upgrade to Premium when you need more credits and advanced features.
          </p>
          <Link href="/chat">
            <Button size="lg" variant="secondary">
              Start Free Today
            </Button>
          </Link>
        </div>
      </section>
    </PageLayout>
  )
}
