import type { ChatSettings, Message } from '@/types'
import { buildCompleteSystemPrompt } from './base-prompt'
import { addTimestampToUserMessage, isTimeRelatedQuestion } from '@/lib/time-utils'

// import { getLanguageAdaptation, buildLanguageEnhancement } from './language-adapter'
// import { detectLanguage } from '../ai-utils'

/**
 * 动态提示词构建器
 * 根据用户设置、对话历史和上下文智能构建最优提示词
 */

// 特殊场景的提示词模板
export const SCENARIO_PROMPTS = {
  // 首次对话
  FIRST_CONVERSATION: `
This is the user's first interaction with HumanWhisper. Make them feel welcome and demonstrate your unique ability to explain things simply. Set a warm, encouraging tone that makes them want to continue the conversation.`,

  // 复杂技术话题
  TECHNICAL_TOPIC: `
This appears to be a technical topic. Remember to:
- Start with the big picture before diving into details
- Use non-technical analogies to explain technical concepts
- Break down complex processes into simple steps
- Explain why this matters in everyday terms`,

  // 学术概念
  ACADEMIC_CONCEPT: `
This seems to be an academic or theoretical concept. Focus on:
- Making abstract ideas concrete with real-world examples
- Connecting theory to practical applications
- Using storytelling to make it memorable
- Avoiding academic jargon and formal language`,

  // 情感或敏感话题
  SENSITIVE_TOPIC: `
This topic may be sensitive or emotional. Approach with:
- Extra empathy and understanding
- Gentle, non-judgmental language
- Acknowledgment of different perspectives
- Focus on helpful, constructive information`,

  // 创意或艺术话题
  CREATIVE_TOPIC: `
This is a creative or artistic topic. Emphasize:
- The beauty and wonder of the subject
- Personal expression and interpretation
- Inspiring examples and stories
- The emotional and human aspects`,
}

// 对话上下文分析
export interface ConversationContext {
  isFirstMessage: boolean
  previousTopics: string[]
  userExpertiseLevel: 'beginner' | 'intermediate' | 'advanced'
  conversationTone: 'formal' | 'casual' | 'friendly'
  commonMisunderstandings: string[]
}

/**
 * 分析用户问题类型
 * @param question - 用户问题
 * @returns string
 */
export function analyzeQuestionType(question: string): string {
  const lowerQuestion = question.toLowerCase()
  
  // 技术相关关键词
  const techKeywords = [
    'algorithm', 'programming', 'code', 'software', 'computer', 'data',
    'api', 'database', 'server', 'network', 'security', 'encryption',
    '算法', '编程', '代码', '软件', '计算机', '数据', '网络', '安全'
  ]
  
  // 学术相关关键词
  const academicKeywords = [
    'theory', 'research', 'study', 'analysis', 'hypothesis', 'methodology',
    'philosophy', 'psychology', 'sociology', 'economics', 'physics', 'chemistry',
    '理论', '研究', '分析', '假设', '方法论', '哲学', '心理学', '经济学', '物理', '化学'
  ]
  
  // 创意相关关键词
  const creativeKeywords = [
    'art', 'music', 'design', 'creative', 'painting', 'drawing', 'writing',
    'poetry', 'literature', 'photography', 'film', 'theater',
    '艺术', '音乐', '设计', '创意', '绘画', '写作', '诗歌', '文学', '摄影', '电影'
  ]
  
  // 敏感话题关键词
  const sensitiveKeywords = [
    'death', 'depression', 'anxiety', 'trauma', 'abuse', 'addiction',
    'politics', 'religion', 'race', 'gender', 'sexuality',
    '死亡', '抑郁', '焦虑', '创伤', '政治', '宗教', '性别'
  ]
  
  if (techKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    return 'TECHNICAL_TOPIC'
  }
  
  if (academicKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    return 'ACADEMIC_CONCEPT'
  }
  
  if (creativeKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    return 'CREATIVE_TOPIC'
  }
  
  if (sensitiveKeywords.some(keyword => lowerQuestion.includes(keyword))) {
    return 'SENSITIVE_TOPIC'
  }
  
  return 'GENERAL'
}

/**
 * 分析对话上下文
 * @param messages - 对话历史
 * @returns ConversationContext
 */
export function analyzeConversationContext(messages: Message[]): ConversationContext {
  const userMessages = messages.filter(m => m.role === 'user')
  // const assistantMessages = messages.filter(m => m.role === 'assistant')
  
  // 判断是否是首次对话
  const isFirstMessage = userMessages.length <= 1
  
  // 提取之前讨论的话题
  const previousTopics = userMessages.slice(0, -1).map(m => 
    m.content.slice(0, 50) + (m.content.length > 50 ? '...' : '')
  )
  
  // 简单的专业水平评估
  let userExpertiseLevel: 'beginner' | 'intermediate' | 'advanced' = 'beginner'
  if (userMessages.length > 3) {
    const avgLength = userMessages.reduce((sum, m) => sum + m.content.length, 0) / userMessages.length
    const hasTechnicalTerms = userMessages.some(m => 
      /\b(algorithm|methodology|paradigm|implementation|architecture)\b/i.test(m.content)
    )
    
    if (avgLength > 100 && hasTechnicalTerms) {
      userExpertiseLevel = 'advanced'
    } else if (avgLength > 50 || userMessages.length > 5) {
      userExpertiseLevel = 'intermediate'
    }
  }
  
  // 对话语调分析
  let conversationTone: 'formal' | 'casual' | 'friendly' = 'friendly'
  const recentMessages = userMessages.slice(-3)
  const hasFormality = recentMessages.some(m => 
    /\b(please|could you|would you|thank you)\b/i.test(m.content)
  )
  const hasCasualness = recentMessages.some(m => 
    /\b(hey|hi|cool|awesome|yeah)\b/i.test(m.content)
  )
  
  if (hasFormality && !hasCasualness) {
    conversationTone = 'formal'
  } else if (hasCasualness) {
    conversationTone = 'casual'
  }
  
  return {
    isFirstMessage,
    previousTopics,
    userExpertiseLevel,
    conversationTone,
    commonMisunderstandings: [], // 可以根据历史对话分析
  }
}

/**
 * 构建上下文感知的提示词
 * @param userQuestion - 用户问题
 * @param settings - 聊天设置
 * @param messages - 对话历史
 * @param complexityAnalysis - 复杂度分析结果 (可选)
 * @returns string
 */
export function buildContextAwarePrompt(
  userQuestion: string,
  settings: ChatSettings,
  messages: Message[] = []
): string {
  // 基础系统提示词
  let systemPrompt = buildCompleteSystemPrompt(userQuestion, settings)
  
  // 分析对话上下文
  const context = analyzeConversationContext(messages)
  const questionType = analyzeQuestionType(userQuestion)
  


  
  // 添加首次对话特殊处理
  if (context.isFirstMessage) {
    systemPrompt += '\n\n' + SCENARIO_PROMPTS.FIRST_CONVERSATION
  }
  
  // 添加问题类型特定指导
  if (questionType !== 'GENERAL' && SCENARIO_PROMPTS[questionType as keyof typeof SCENARIO_PROMPTS]) {
    systemPrompt += '\n\n' + SCENARIO_PROMPTS[questionType as keyof typeof SCENARIO_PROMPTS]
  }
  
  // 根据用户专业水平调整
  if (context.userExpertiseLevel === 'advanced') {
    systemPrompt += `\n\n### Advanced User Adaptation:
The user appears to have some expertise in this area. You can:
- Use slightly more sophisticated vocabulary when appropriate
- Acknowledge their existing knowledge
- Focus on nuances and deeper insights
- Still maintain the simple, friendly explanation style`
  } else if (context.userExpertiseLevel === 'beginner') {
    systemPrompt += `\n\n### Beginner-Friendly Approach:
This user appears to be new to the topic. Extra emphasis on:
- Starting with absolute basics
- Defining any terms you use
- Using very simple analogies
- Encouraging questions and curiosity`
  }
  
  // 添加语言和文化适配
  if (settings.language !== 'en') {
    // const languageEnhancement = buildLanguageEnhancement(settings.language, settings.useAnalogies)
    // systemPrompt += '\n\n' + languageEnhancement
    systemPrompt += '\n\n请用' + settings.language + '语言回答，并结合相应的文化背景。'
  }
  
  // 添加对话连续性
  if (context.previousTopics.length > 0) {
    systemPrompt += `\n\n### Conversation Context:
Previous topics discussed: ${context.previousTopics.join(', ')}
Build on this context when relevant, but focus on the current question.`
  }
  
  return systemPrompt
}

/**
 * 构建优化的消息序列
 * @param userQuestion - 用户问题
 * @param settings - 聊天设置
 * @param messages - 对话历史
 * @param images - 图像数据 (可选)
 * @param complexityAnalysis - 复杂度分析结果 (可选)
 * @returns Array<{role: string, content: string}>
 */
export function buildOptimizedMessageSequence(
  userQuestion: string,
  settings: ChatSettings,
  messages: Message[] = [],
  images?: string[]
) {
  const systemPrompt = buildContextAwarePrompt(userQuestion, settings, messages)

  // 检查是否是时间相关问题，决定是否添加时间戳
  const needsTimestamp = isTimeRelatedQuestion(userQuestion)

  // 构建消息序列
  const messageSequence: Array<{role: 'system' | 'user' | 'assistant', content: any}> = [
    {
      role: 'system' as const,
      content: systemPrompt
    }
  ]
  
  // 添加相关的历史消息（保持上下文但避免过长）
  const relevantHistory = messages.slice(-6) // 最近6条消息
  relevantHistory.forEach(message => {
    if (message.role !== 'system') {
      messageSequence.push({
        role: message.role,
        content: message.content
      })
    }
  })
  
  // 添加当前用户问题（如果不在历史中）
  const lastMessage = relevantHistory[relevantHistory.length - 1]
  if (!lastMessage || lastMessage.content !== userQuestion) {
    // 为时间相关问题添加时间戳，其他问题保持原样
    const finalUserMessage = needsTimestamp
      ? addTimestampToUserMessage(userQuestion)
      : userQuestion

    // 构建用户消息，支持图像
    if (images && images.length > 0) {
      // 如果有图像，构建多模态消息
      const content: any[] = [
        { type: 'text', text: finalUserMessage }
      ]

      // 添加图像内容
      images.forEach(imageBase64 => {
        content.push({
          type: 'image_url',
          image_url: { url: imageBase64 }
        })
      })

      messageSequence.push({
        role: 'user' as const,
        content: content
      })
    } else {
      // 纯文本消息
      messageSequence.push({
        role: 'user' as const,
        content: finalUserMessage
      })
    }
  }
  
  return messageSequence
}

/**
 * 验证提示词质量
 * @param prompt - 提示词内容
 * @returns object
 */
export function validatePromptQuality(prompt: string) {
  const issues: string[] = []
  const suggestions: string[] = []

  // 检查长度
  if (prompt.length > 8000) {
    issues.push('Prompt too long, may affect performance')
    suggestions.push('Consider simplifying or breaking into segments')
  }

  if (prompt.length < 500) {
    issues.push('Prompt too short, may lack necessary guidance')
    suggestions.push('Add more specific guidelines')
  }

  // 检查关键元素
  const hasIdentity = /HumanWhisper/i.test(prompt)
  const hasExamples = /example|analogy/i.test(prompt)
  const hasStructure = /structure|format/i.test(prompt)

  if (!hasIdentity) {
    issues.push('Missing identity definition')
    suggestions.push('Add HumanWhisper identity description')
  }

  if (!hasExamples) {
    suggestions.push('Consider adding examples or analogy guidance')
  }

  if (!hasStructure) {
    suggestions.push('Consider adding response structure guidance')
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions,
    score: Math.max(0, 100 - issues.length * 20 - suggestions.length * 5)
  }
}
