import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { <PERSON><PERSON>rovider } from '@clerk/nextjs'
import { Toaster } from '@/components/ui/toaster'
import { ThemeProvider } from '@/components/providers/ThemeProvider'

// 字体配置
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

// 网站元数据
export const metadata: Metadata = {
  title: {
    default: 'HumanWhisper - AI That Speaks Your Language | Complex Topics Made Simple',
    template: '%s'
  },
  description: 'Get clear, simple explanations for anything complex with HumanWhisper AI. Like having a patient friend who speaks your language - no jargon, just understanding. Try free now!',
  keywords: [
    'AI that explains simply',
    'plain language AI assistant',
    'AI simplify complex topics',
    'easy to understand AI',
    'simple AI explanations',
    'AI breakdown complex concepts',
    'human-friendly AI chat',
    'AI explain like I\'m 5',
    'complex topics made simple',
    'easy AI conversation',
    'beginner friendly AI',
    'AI for non-technical users'
  ],
  authors: [{ name: 'HumanWhisper Team' }],
  creator: 'HumanWhisper',
  publisher: 'HumanWhisper',
  
  // Open Graph 元数据
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://humanwhisper.com',
    siteName: 'HumanWhisper',
    title: 'HumanWhisper - AI That Speaks Your Language | Complex Topics Made Simple',
    description: 'Get clear, simple explanations for anything complex with HumanWhisper AI. Like having a patient friend who speaks your language - no jargon, just understanding.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'HumanWhisper - AI that speaks your language',
      },
    ],
  },
  
  // Twitter 卡片
  twitter: {
    card: 'summary_large_image',
    title: 'HumanWhisper - AI that speaks your language',
    description: 'AI that explains complex topics in simple, human-friendly language.',
    images: ['/og-image.png'],
    creator: '@humanwhisper',
  },
  
  // 图标配置
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  
  // 清单文件
  manifest: '/site.webmanifest',
  
  // 其他元数据
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://humanwhisper.com'),
  alternates: {
    canonical: '/',
  },
  
  // 机器人指令
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  

}

// 视口配置
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#0ea5e9' },
    { media: '(prefers-color-scheme: dark)', color: '#0284c7' },
  ],
}

// 根布局组件
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="en" className={inter.variable} suppressHydrationWarning>
        <head>
        {/* 预连接到外部资源 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-118DQZNFG0"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-118DQZNFG0');
            `,
          }}
        />

        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "HumanWhisper",
              "description": "AI that explains complex topics in simple, human-friendly language",
              "applicationCategory": "AI Assistant",
              "operatingSystem": "Web Browser",
              "url": process.env.NEXT_PUBLIC_APP_URL || "https://humanwhisper.com",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "creator": {
                "@type": "Organization",
                "name": "HumanWhisper Team"
              },
              "featureList": [
                "Plain language explanations",
                "Complex topics made simple",
                "Human-friendly AI chat",
                "No jargon responses",
                "Patient AI assistant"
              ]
            })
          }}
        />
      </head>
      <body className={`${inter.className} antialiased min-h-screen bg-background font-sans`}>
        {/* Skip to main content link (Accessibility feature) */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50"
        >
          Skip to main content
        </a>
        
        {/* 主要内容区域 */}
        <ThemeProvider>
          <div id="main-content" className="relative flex min-h-screen flex-col">
            {children}
          </div>
        </ThemeProvider>

        {/* Toast通知 */}
        <Toaster />
        </body>
      </html>
    </ClerkProvider>
  )
}
