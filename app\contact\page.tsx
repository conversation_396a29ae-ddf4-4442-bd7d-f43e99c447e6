import { PageLayout } from '@/components/layout/PageLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Mail, MessageSquare, Clock, HelpCircle, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import type { Metadata } from 'next'

// Contact Us页面SEO优化元数据
export const metadata: Metadata = {
  title: 'Contact Our AI Support Team - HumanWhisper',
  description: 'Have a question, suggestion, or need help? Reach out to HumanWhisper\'s support team anytime—we\'re here to assist.',
  keywords: [
    'contact HumanWhisper',
    'AI support',
    'customer service',
    'AI help',
    'reach HumanWhisper'
  ],
  openGraph: {
    title: 'Contact Our AI Support Team - HumanWhisper',
    description: 'Have a question, suggestion, or need help? Reach out to HumanWhisper\'s support team anytime—we\'re here to assist.',
    url: '/contact',
    type: 'website',
  },
  twitter: {
    title: 'Contact Our AI Support Team - HumanWhisper',
    description: 'Have a question, suggestion, or need help? Reach out to HumanWhisper\'s support team anytime—we\'re here to assist.',
  },
  alternates: {
    canonical: '/contact',
  },
}

export default function ContactPage() {
  return (
    <PageLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-whisper-50 to-human-50 dark:from-whisper-950 dark:to-human-950">
        <div className="container text-center">
          <div className="mx-auto max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-whisper-600 to-human-600 bg-clip-text text-transparent">
                Contact Our AI Support Team
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Have a question, suggestion, or need help? <strong>Reach out to HumanWhisper</strong> 
              anytime—our dedicated <strong>AI support</strong> team is here to assist you with 
              exceptional <strong>customer service</strong>.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Get in Touch
              </h2>
              <p className="text-lg text-muted-foreground">
                We're here to help with any questions about HumanWhisper's AI simplification features, 
                account issues, or general inquiries. Don't hesitate to <strong>contact HumanWhisper</strong> 
                for personalized <strong>AI help</strong>.
              </p>
            </div>

            {/* Main Contact Card */}
            <Card className="mb-8 border-2 border-primary/20">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-2xl">Official Email Support</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="bg-muted/50 p-6 rounded-lg mb-6">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-2xl font-semibold text-primary hover:underline"
                  >
                    <EMAIL>
                  </a>
                </div>
                <p className="text-muted-foreground">
                  Send us your questions, feedback, or technical issues. Our support team
                  monitors this inbox regularly and provides comprehensive assistance.
                </p>
              </CardContent>
            </Card>

            {/* Response Time & FAQ */}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <Clock className="h-6 w-6 text-green-600" />
                    <CardTitle className="text-lg">Response Time</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    We typically respond to all inquiries within <strong>24 hours</strong>. 
                    For urgent technical issues, we prioritize faster response times to ensure 
                    your experience with our AI support remains smooth.
                  </p>
                  <div className="text-sm text-muted-foreground">
                    <div className="flex items-center gap-2 mb-1">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <span>General inquiries: 24 hours</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <span>Technical issues: Priority handling</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <HelpCircle className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-lg">Quick Answers</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Before reaching out, check our features page for detailed information about
                    HumanWhisper's capabilities and common questions.
                  </p>
                  <Link href="/features">
                    <Button variant="outline" className="w-full">
                      <HelpCircle className="h-4 w-4 mr-2" />
                      Browse Features
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>

            {/* What to Include */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="text-xl">What to Include in Your Message</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  To help us provide the best <strong>AI help</strong> possible, please include:
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Your account email (if applicable)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Detailed description of the issue</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Steps you've already tried</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Browser and device information</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Screenshots (if relevant)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                      <span className="text-sm">Any error messages received</span>
                    </div>
                  </div>
                </div>

                {/* Email Template Example */}
                <div className="mt-6 p-4 bg-muted/50 rounded-lg border-l-4 border-primary">
                  <h4 className="font-medium mb-3 text-sm">📧 Email Template Example</h4>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p><strong>Subject:</strong> [Issue Type] - Brief description</p>
                    <div className="bg-background p-3 rounded border text-xs font-mono">
                      <p><strong>Account Email:</strong> <EMAIL></p>
                      <p><strong>Issue Description:</strong> I'm experiencing [specific problem] when trying to [action].</p>
                      <p><strong>Steps Tried:</strong> I have already tried [list steps].</p>
                      <p><strong>Browser/Device:</strong> Chrome 120 on Windows 11 / Safari on iPhone 15</p>
                      <p><strong>Error Message:</strong> "[exact error message if any]"</p>
                      <p><strong>Screenshots:</strong> Attached [if applicable]</p>
                    </div>
                    <p className="text-xs italic">This template helps us quickly understand and resolve your issue.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Alternative Support */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-6">
              Other Ways to Get Help
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardContent className="pt-6 text-center">
                  <MessageSquare className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">Try Our AI Chat</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Sometimes the best way to understand HumanWhisper is to experience it firsthand.
                  </p>
                  <Link href="/chat">
                    <Button variant="outline">
                      Start Chat
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6 text-center">
                  <HelpCircle className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">Browse Documentation</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Learn more about features, pricing, and how to get the most out of HumanWhisper.
                  </p>
                  <Link href="/features">
                    <Button variant="outline">
                      View Features
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            We're Here to Help
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Your success with HumanWhisper is our priority. Don't hesitate to reach out—
            we're committed to providing excellent <strong>customer service</strong> and support.
          </p>
          <a href="mailto:<EMAIL>">
            <Button size="lg" variant="secondary">
              <Mail className="h-5 w-5 mr-2" />
              Contact Support Now
            </Button>
          </a>
        </div>
      </section>
    </PageLayout>
  )
}
