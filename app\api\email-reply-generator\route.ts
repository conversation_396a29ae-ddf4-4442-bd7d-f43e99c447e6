import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 场景映射
const SCENARIO_MAP: Record<string, string> = {
  'job_application': 'job application or career opportunity',
  'complaint_response': 'customer complaint or service issue',
  'collaboration': 'business collaboration or partnership proposal',
  'invoice_reminder': 'invoice, payment, or billing matter',
  'event_invitation': 'meeting, event, or appointment invitation',
  'leave_request': 'leave request or time-off application',
  'thank_you': 'appreciation, gratitude, or thank you note',
  'apology': 'apology or correction of mistake',
  'follow_up': 'project follow-up or meeting recap'
}

// 语气映射
const TONE_MAP: Record<string, string> = {
  'formal': 'formal and professional',
  'friendly': 'warm, friendly, and approachable',
  'humorous': 'light-hearted with appropriate humor',
  'firm_decline': 'polite but firm in declining',
  'enthusiastic': 'positive, energetic, and enthusiastic',
  'diplomatic': 'tactful, diplomatic, and carefully worded'
}

// 构建邮件回复prompt
function buildEmailReplyPrompt(
  scenario: string,
  toneStyle: string,
  originalEmail: string,
  keyPoints?: string
): string {
  const scenarioDesc = SCENARIO_MAP[scenario] || scenario
  const toneDesc = TONE_MAP[toneStyle] || toneStyle

  let prompt = `You are a professional email writing assistant. Your task is to generate a well-crafted email reply.

**Context:**
- Email scenario: ${scenarioDesc}
- Tone style: ${toneDesc}
- Language: English (professional business English)

**Original email to reply to:**
"""
${originalEmail}
"""

**Additional context and key points to include:**
${keyPoints ? `"""
${keyPoints}
"""` : 'None provided'}

**Instructions:**
1. Write a professional email reply that addresses the original email appropriately
2. Use a ${toneDesc} tone throughout the response
3. Include a proper subject line (if needed)
4. Structure the email with appropriate greeting, body, and closing
5. If additional context was provided, incorporate those key points naturally
6. Keep the response concise but complete
7. Ensure the reply is contextually appropriate for the scenario: ${scenarioDesc}

**Output format:**
Subject: [Appropriate subject line]

[Email body with proper greeting, main content, and professional closing]

Generate the email reply now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      scenario,
      toneStyle,
      originalEmail,
      keyPoints,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!scenario || !toneStyle || !originalEmail?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildEmailReplyPrompt(
      scenario,
      toneStyle,
      originalEmail.trim(),
      keyPoints?.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'Qwen/Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的邮件回复
    const emailReply = response.choices[0]?.message?.content?.trim()

    if (!emailReply) {
      throw new Error('No email reply generated')
    }

    return NextResponse.json({
      emailReply,
      metadata: {
        scenario,
        toneStyle,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Email content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate email reply' },
      { status: 500 }
    )
  }
}
