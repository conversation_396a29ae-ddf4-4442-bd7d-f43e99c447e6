import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 关系阶段映射
const RELATIONSHIP_STAGE_MAP: Record<string, string> = {
  'crush': 'secret crush with hidden feelings and nervous energy, first confession',
  'flirting': 'flirting stage with playful banter, testing waters, building romantic tension',
  'dating': 'early dating phase, new relationship, getting to know each other deeply',
  'long_distance': 'long distance relationship, miles apart, missing each other, staying connected',
  'anniversary': 'anniversary celebration, reflecting on journey together, milestone moments',
  'apology': 'making up after a fight, seeking forgiveness, rebuilding trust and connection',
  'proposal': 'proposal preparation, ready for next level, marriage thoughts, future planning',
  'long_term': 'long-term love, years together, deep connection, comfortable intimacy',
  'rekindling': 'rekindling romance, reigniting spark, bringing back excitement'
}

// 构建情书生成prompt
function buildLoveLetterPrompt(
  relationshipStage: string,
  quirkyHabit: string,
  sharedMemory: string
): string {
  const stageDesc = RELATIONSHIP_STAGE_MAP[relationshipStage] || relationshipStage

  const prompt = `You are a creative writer specializing in authentic, anti-cliche love letters that avoid generic romantic tropes. Your task is to create a personalized, heartfelt confession that feels genuine and unique.

**Relationship Context:**
- Stage: ${stageDesc}
- Their Quirky Habit: ${quirkyHabit}
- Shared Memory: ${sharedMemory}

**Instructions:**
Write a 100-word English love letter that:

1. **Avoids Cliches**: No "roses are red," "you complete me," "love at first sight," or generic romantic phrases
2. **Personal & Specific**: References the quirky habit and shared memory naturally
3. **Anti-Routine Punchline**: Ends with an unexpected, clever, or humorous twist that's memorable
4. **Authentic Voice**: Sounds like a real person, not a greeting card
5. **Emotional Impact**: Creates genuine connection through specificity and vulnerability
6. **Handwriting Ready**: Flows well when written by hand, appropriate line breaks

**Tone Guidelines:**
- Match the relationship stage (nervous for crush, comfortable for long-term, etc.)
- Be vulnerable but not overly dramatic
- Include subtle humor or wit
- Make it conversational and natural
- Focus on small, meaningful details

**Anti-Cliche Strategies:**
- Use unexpected metaphors (not flowers/stars/oceans)
- Reference modern life, shared interests, inside jokes
- Be self-aware about romance without being cynical
- Include imperfections and real moments
- Make the punchline surprising but fitting

**Output Format:**
Return a valid JSON object:

{
  "letter": "The main 80-90 word love letter content",
  "punchline": "The final 10-20 word anti-routine punchline",
  "wordCount": 100,
  "tone": "Brief description of the emotional tone used",
  "explanation": "Brief explanation of the anti-cliche strategy and why this approach works"
}

**Important Guidelines:**
- Keep total word count around 100 words
- Make the punchline feel natural, not forced
- Ensure the letter flows from general to specific
- Reference both the quirky habit and shared memory meaningfully
- Make it feel like something a real person would write
- Avoid overly poetic or flowery language

Generate the anti-cliche love letter now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      relationshipStage,
      quirkyHabit,
      sharedMemory,
      model = 'THUDM/glm-4-9b-chat'
    } = body

    // 输入验证
    if (!relationshipStage || !quirkyHabit?.trim() || !sharedMemory?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildLoveLetterPrompt(
      relationshipStage,
      quirkyHabit.trim(),
      sharedMemory.trim()
    )

    // 模型参数配置
    const modelParams = {
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['THUDM/glm-4-9b-chat']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let loveLetter: {letter: string, punchline: string, wordCount: number, tone: string, explanation: string}
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      loveLetter = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!loveLetter.letter || !loveLetter.punchline || !loveLetter.tone || !loveLetter.explanation) {
        throw new Error('Invalid response structure')
      }
      
      // 确保字数统计准确
      const totalWords = (loveLetter.letter + ' ' + loveLetter.punchline).split(/\s+/).length
      loveLetter.wordCount = totalWords
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取内容
      const lines = generatedContent.split('\n').filter(line => line.trim())
      
      let letterText = ''
      let punchlineText = ''
      
      // 尝试找到情书内容
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]?.trim()
        if (line && line.length > 20 && !line.includes('{') && !line.includes('}')) {
          if (!letterText) {
            letterText = line
          } else if (!punchlineText && line.length < letterText.length) {
            punchlineText = line
          }
        }
      }
      
      if (!letterText) {
        // 创建基本情书结构
        letterText = `I've been thinking about ${quirkyHabit.toLowerCase()} and how it makes me smile. Remember ${sharedMemory.toLowerCase()}? That moment showed me something special about you.`
        punchlineText = "I guess what I'm trying to say is... you've got me completely figured out, and I'm okay with that."
      }
      
      const totalWords = (letterText + ' ' + (punchlineText || '')).split(/\s+/).length
      
      loveLetter = {
        letter: letterText,
        punchline: punchlineText || "And somehow, that's exactly what I needed to hear.",
        wordCount: totalWords,
        tone: 'Warm and authentic with gentle humor',
        explanation: 'Uses personal details to create genuine connection while avoiding romantic cliches'
      }
    }

    return NextResponse.json({
      loveLetter,
      metadata: {
        relationshipStage,
        wordCount: loveLetter.wordCount,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate love letter' },
      { status: 500 }
    )
  }
}
