'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Calendar, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Target, Trophy, Users, Gift } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 受众类型选项
const AUDIENCE_TYPES = [
  { id: 'fitness', name: 'Fitness Enthusiasts', description: 'Workout routines, nutrition, physical health goals' },
  { id: 'writing', name: 'Writers & Authors', description: 'Creative writing, journaling, storytelling skills' },
  { id: 'finance', name: 'Financial Wellness', description: 'Budgeting, saving, investment, money management' },
  { id: 'photography', name: 'Photography', description: 'Photo skills, composition, editing, visual storytelling' },
  { id: 'mindfulness', name: 'Mindfulness & Meditation', description: 'Mental health, stress relief, self-awareness' },
  { id: 'productivity', name: 'Productivity', description: 'Time management, organization, efficiency habits' },
  { id: 'creativity', name: 'Creative Arts', description: 'Drawing, painting, crafts, artistic expression' },
  { id: 'learning', name: 'Learning & Skills', description: 'New skills, languages, professional development' },
  { id: 'social', name: 'Social Connection', description: 'Networking, relationships, communication skills' },
  { id: 'wellness', name: 'General Wellness', description: 'Holistic health, lifestyle improvement, self-care' },
  { id: 'custom', name: 'Custom Audience', description: 'Define your own target community' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, complex functions, challenge design'
  },
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Logical reasoning, systematic planning, behavior analysis'
  }
]

interface GeneratedChallenge {
  challengeTitle: string
  dailyTasks: string[]
  checkInPrompts: string[]
  rewardSystem: {
    weekly: string[]
    milestones: string[]
    completion: string
  }
  engagementTips: string[]
}

export function ThirtyDayChallengeClient() {
  // 表单状态
  const [audienceType, setAudienceType] = useState('')
  const [customAudience, setCustomAudience] = useState('')
  const [painPoint, setPainPoint] = useState('')
  const [completionFormat, setCompletionFormat] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedChallenge, setGeneratedChallenge] = useState<GeneratedChallenge | null>(null)
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成30天挑战
  const handleGenerate = async () => {
    if (!audienceType || !painPoint.trim() || !completionFormat.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/30-day-challenge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audienceType: audienceType === 'custom' ? customAudience : audienceType,
          painPoint: painPoint.trim(),
          completionFormat: completionFormat.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate 30-day challenge')
      }

      const data = await response.json()
      setGeneratedChallenge(data.challenge)

      toast({
        title: "30-Day Challenge Generated!",
        description: "Your community engagement challenge is ready to launch.",
      })

    } catch (error) {
      setError('Failed to generate 30-day challenge. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制内容到剪贴板
  const copyToClipboard = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard.`,
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  // 复制完整挑战
  const copyFullChallenge = async () => {
    if (!generatedChallenge) return
    
    const fullChallenge = `${generatedChallenge.challengeTitle}

DAILY TASKS:
${generatedChallenge.dailyTasks.map((task, index) => `Day ${index + 1}: ${task}`).join('\n')}

CHECK-IN PROMPTS:
${generatedChallenge.checkInPrompts.map((prompt, index) => `${index + 1}. ${prompt}`).join('\n')}

REWARD SYSTEM:
Weekly Rewards:
${generatedChallenge.rewardSystem.weekly.map((reward, index) => `Week ${index + 1}: ${reward}`).join('\n')}

Milestones:
${generatedChallenge.rewardSystem.milestones.map((milestone, index) => `${index + 1}. ${milestone}`).join('\n')}

Completion Reward: ${generatedChallenge.rewardSystem.completion}

ENGAGEMENT TIPS:
${generatedChallenge.engagementTips.map((tip, index) => `${index + 1}. ${tip}`).join('\n')}`

    await copyToClipboard(fullChallenge, 'Full 30-Day Challenge')
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                AI 30-Day Challenge
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Community Engagement Ideas
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>engaging 30-day challenges for communities</strong>. 
              Create daily tasks, check-in prompts, and reward systems to boost member retention.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                30 Daily Tasks
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Retention Focused
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Community Ready
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-primary" />
                      Challenge Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Audience Type */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Target Audience *</label>
                      <Select value={audienceType} onValueChange={setAudienceType}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your community audience" />
                        </SelectTrigger>
                        <SelectContent>
                          {AUDIENCE_TYPES.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div>
                                <div className="font-medium">{type.name}</div>
                                <div className="text-xs text-muted-foreground">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Audience Input */}
                    {audienceType === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Audience *</label>
                        <input
                          type="text"
                          placeholder="Describe your target community"
                          value={customAudience}
                          onChange={(e) => setCustomAudience(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Pain Point */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Community Pain Point *</label>
                      <Textarea
                        placeholder="Describe the main challenge or problem your community faces..."
                        value={painPoint}
                        onChange={(e) => setPainPoint(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        The specific issue this 30-day challenge will address
                      </p>
                    </div>

                    {/* Completion Format */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Check-in Format *</label>
                      <input
                        type="text"
                        placeholder="e.g., photo upload, word count, expense tracking, time logged"
                        value={completionFormat}
                        onChange={(e) => setCompletionFormat(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        How members will prove they completed daily tasks
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Challenge...
                        </>
                      ) : (
                        <>
                          <Calendar className="h-4 w-4 mr-2" />
                          Generate 30-Day Challenge
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Your 30-Day Challenge
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedChallenge ? (
                      <div className="space-y-6">
                        {/* Challenge Title */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Target className="h-4 w-4" />
                              Challenge Title
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedChallenge.challengeTitle, 'Challenge Title')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                            <h3 className="text-lg font-bold text-green-800 dark:text-green-200">
                              {generatedChallenge.challengeTitle}
                            </h3>
                          </div>
                        </div>

                        {/* Daily Tasks Preview */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              Daily Tasks (First 7 Days)
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                generatedChallenge.dailyTasks.map((task, index) => `Day ${index + 1}: ${task}`).join('\n'),
                                'All Daily Tasks'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy All
                            </Button>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-2 max-h-64 overflow-y-auto">
                            {generatedChallenge.dailyTasks.slice(0, 7).map((task, index) => (
                              <div key={index} className="flex items-start gap-2">
                                <span className="text-xs font-medium text-blue-600 mt-0.5 min-w-[40px]">Day {index + 1}:</span>
                                <p className="text-sm leading-relaxed">{task}</p>
                              </div>
                            ))}
                            {generatedChallenge.dailyTasks.length > 7 && (
                              <p className="text-xs text-muted-foreground italic">
                                ... and {generatedChallenge.dailyTasks.length - 7} more days
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Check-in Prompts */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              Check-in Prompts
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                generatedChallenge.checkInPrompts.join('\n'),
                                'Check-in Prompts'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg space-y-2">
                            {generatedChallenge.checkInPrompts.map((prompt, index) => (
                              <div key={index} className="flex items-start gap-2">
                                <span className="text-xs font-medium text-purple-600 mt-0.5">{index + 1}.</span>
                                <p className="text-sm leading-relaxed">{prompt}</p>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Reward System */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm flex items-center gap-1">
                              <Gift className="h-4 w-4" />
                              Reward System
                            </h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(
                                `Weekly Rewards:\n${generatedChallenge.rewardSystem.weekly.join('\n')}\n\nMilestones:\n${generatedChallenge.rewardSystem.milestones.join('\n')}\n\nCompletion: ${generatedChallenge.rewardSystem.completion}`,
                                'Reward System'
                              )}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg space-y-3">
                            <div>
                              <span className="text-xs font-medium text-orange-600">Weekly Rewards:</span>
                              <div className="mt-1 space-y-1">
                                {generatedChallenge.rewardSystem.weekly.map((reward, index) => (
                                  <p key={index} className="text-sm">Week {index + 1}: {reward}</p>
                                ))}
                              </div>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-orange-600">Milestones:</span>
                              <div className="mt-1 space-y-1">
                                {generatedChallenge.rewardSystem.milestones.map((milestone, index) => (
                                  <p key={index} className="text-sm">{milestone}</p>
                                ))}
                              </div>
                            </div>
                            <div>
                              <span className="text-xs font-medium text-orange-600">Completion Reward:</span>
                              <p className="text-sm mt-1">{generatedChallenge.rewardSystem.completion}</p>
                            </div>
                          </div>
                        </div>

                        {/* Copy Full Challenge */}
                        <div className="flex justify-center pt-4 border-t">
                          <Button onClick={copyFullChallenge} variant="default" size="lg">
                            <Copy className="h-4 w-4 mr-2" />
                            Copy Complete 30-Day Challenge
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your 30-day challenge will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why 30-Day Challenges Work for Communities
            </h2>
            <p className="text-xl text-muted-foreground">
              Transform member engagement and build lasting habits with structured challenges
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Member Retention</h3>
                <p className="text-sm text-muted-foreground">
                  Combat the 3-day dropout problem with progressive engagement and community support
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Habit Formation</h3>
                <p className="text-sm text-muted-foreground">
                  30 days is the perfect timeframe to establish new habits and see real progress
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Trophy className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Gamification</h3>
                <p className="text-sm text-muted-foreground">
                  Rewards, milestones, and achievements keep members motivated throughout the journey
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Structured Progress</h3>
                <p className="text-sm text-muted-foreground">
                  Daily tasks with progressive difficulty ensure steady advancement toward goals
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Gift className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Community Building</h3>
                <p className="text-sm text-muted-foreground">
                  Shared challenges create bonds, accountability, and mutual support among members
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Measurable Results</h3>
                <p className="text-sm text-muted-foreground">
                  Clear check-in formats provide trackable progress and tangible achievements
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create your community challenge in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Audience</h3>
              <p className="text-sm text-muted-foreground">
                Select your community type from fitness, writing, finance, and more
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Define Pain Point</h3>
              <p className="text-sm text-muted-foreground">
                Identify the specific challenge your community wants to overcome
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Set Check-in Format</h3>
              <p className="text-sm text-muted-foreground">
                Specify how members will prove completion: photos, word counts, time logs
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Launch Challenge</h3>
              <p className="text-sm text-muted-foreground">
                Get 30 daily tasks, check-in prompts, and a complete reward system
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Challenge Examples by Community Type
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different communities create engaging 30-day challenges
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💪</span>
                  Fitness Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-red-50 dark:bg-red-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-red-600">Challenge:</span>
                    <p className="text-sm font-semibold">"30-Day Morning Movement Challenge"</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-red-600">Pain Point:</span>
                    <p className="text-sm">Lack of consistent exercise routine</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-red-600">Check-in:</span>
                    <p className="text-sm">Photo of workout completion + time logged</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-red-600">Sample Tasks:</span>
                    <p className="text-sm">Day 1: 5-minute walk • Day 15: 20-minute HIIT • Day 30: Personal fitness goal achievement</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">✍️</span>
                  Writing Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-blue-600">Challenge:</span>
                    <p className="text-sm font-semibold">"30-Day Creative Writing Sprint"</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-600">Pain Point:</span>
                    <p className="text-sm">Writer's block and inconsistent writing habits</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-600">Check-in:</span>
                    <p className="text-sm">Daily word count + excerpt sharing</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-600">Sample Tasks:</span>
                    <p className="text-sm">Day 1: 100 words • Day 15: Character development exercise • Day 30: Complete short story</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">💰</span>
                  Finance Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-green-600">Challenge:</span>
                    <p className="text-sm font-semibold">"30-Day Money Mindfulness Challenge"</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Pain Point:</span>
                    <p className="text-sm">Lack of spending awareness and budget tracking</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Check-in:</span>
                    <p className="text-sm">Daily expense tracking screenshot</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Sample Tasks:</span>
                    <p className="text-sm">Day 1: Track all expenses • Day 15: No-spend day • Day 30: Monthly budget review</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">📸</span>
                  Photography Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg space-y-3">
                  <div>
                    <span className="text-xs font-medium text-purple-600">Challenge:</span>
                    <p className="text-sm font-semibold">"30-Day Visual Storytelling Challenge"</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-purple-600">Pain Point:</span>
                    <p className="text-sm">Lack of creative inspiration and technical skills</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-purple-600">Check-in:</span>
                    <p className="text-sm">Daily photo upload with technique explanation</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-purple-600">Sample Tasks:</span>
                    <p className="text-sm">Day 1: Golden hour portrait • Day 15: Street photography • Day 30: Photo series completion</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about creating successful 30-day challenges
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How do 30-day challenges improve member retention?",
                  answer: "30-day challenges create structured engagement that combats the common 3-day dropout problem. Progressive daily tasks, community interaction, and reward systems keep members motivated and connected to your community throughout the entire month."
                },
                {
                  question: "What makes a good check-in format?",
                  answer: "Effective check-in formats are specific, measurable, and easy to complete. Examples include photo uploads, word counts, time tracking, or expense logging. The key is making progress visible and shareable with the community."
                },
                {
                  question: "How should I structure the reward system?",
                  answer: "Use a mix of weekly rewards (recognition, badges), milestone rewards (Day 7, 14, 21 achievements), and a meaningful completion reward. Balance intrinsic motivation (personal satisfaction) with extrinsic rewards (tangible benefits)."
                },
                {
                  question: "Can I modify the generated challenge?",
                  answer: "Absolutely! The generated challenge is a comprehensive starting point. Feel free to adjust daily tasks, modify rewards, or adapt the format to better fit your community's specific needs and culture."
                },
                {
                  question: "How do I prevent member dropoff during the challenge?",
                  answer: "Use the provided engagement tips: create accountability partnerships, share success stories, host weekly check-ins, respond to struggles with support, and maintain active community moderation throughout the 30 days."
                },
                {
                  question: "What happens after the 30 days are complete?",
                  answer: "Successful challenges often lead to habit formation that continues beyond 30 days. Consider creating follow-up challenges, advanced programs, or transitioning successful participants into community leadership roles."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
