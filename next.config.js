/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性功能
  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', 'humanwhisper.com']
    }
  },

  // 外部包配置
  serverExternalPackages: ['openai'],

  // 图片优化配置
  images: {
    domains: [
      'lh3.googleusercontent.com', // Google用户头像域名
      'avatars.githubusercontent.com', // GitHub用户头像域名
    ],
    formats: ['image/webp', 'image/avif'], // 支持的图片格式
  },

  // 自定义环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY, // 自定义环境变量
  },

  // URL重定向规则
  async redirects() {
    return [
      {
        source: '/home', // 源路径
        destination: '/', // 目标路径
        permanent: true, // 永久重定向
      },
    ]
  },

  // HTTP响应头配置
  async headers() {
    return [
      {
        source: '/api/:path*', // API路由匹配
        headers: [
          {
            key: 'Access-Control-Allow-Origin', // 允许跨域来源
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods', // 允许的HTTP方法
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers', // 允许的请求头
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },

  // Webpack自定义配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 配置模块解析回退选项
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false, // 禁用文件系统模块
    }

    return config
  },

  // 构建输出配置
  output: 'standalone', // 独立输出模式

  // 启用压缩
  compress: true,

  // 隐藏X-Powered-By头
  poweredByHeader: false,

  // 启用React严格模式
  reactStrictMode: true,

  // SWC压缩在Next.js 15中默认启用，无需配置
}

module.exports = nextConfig
