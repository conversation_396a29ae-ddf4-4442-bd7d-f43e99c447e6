import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 业务场景映射
const SCENARIO_MAP: Record<string, string> = {
  'saas': 'Software as a Service platform',
  'ecommerce': 'E-commerce store or online retail',
  'hardware': 'Hardware product or physical device',
  'event': 'Event registration or conference',
  'education': 'Educational service or online course',
  'healthcare': 'Healthcare service or medical platform',
  'finance': 'Financial service or fintech product',
  'consulting': 'Consulting service or professional service'
}

// 构建FAQ生成prompt
function buildFAQPrompt(
  scenario: string,
  productInfo: string,
  userQuestions: string,
  faqCount: number
): string {
  const scenarioDesc = SCENARIO_MAP[scenario] || scenario

  const prompt = `You are a professional FAQ content creator. Your task is to generate comprehensive and helpful FAQ sections for businesses.

**Business Context:**
- Business type: ${scenarioDesc}
- Number of FAQs needed: ${faqCount}

**Product/Service Information:**
"""
${productInfo}
"""

**Recent User Questions (if provided):**
${userQuestions ? `"""
${userQuestions}
"""` : 'No specific user questions provided - generate common questions for this business type.'}

**Instructions:**
1. Generate ${faqCount} frequently asked questions and comprehensive answers
2. Cover the most important aspects customers would ask about
3. Include questions about pricing, features, support, getting started, and common concerns
4. If user questions are provided, incorporate similar themes and topics
5. Make answers clear, helpful, and professional
6. Use a friendly but professional tone
7. Ensure answers are specific to the provided product/service information

**Required Output Format:**
Return a valid JSON array with exactly ${faqCount} objects, each containing "question" and "answer" fields:

[
  {
    "question": "What is [product/service] and how does it work?",
    "answer": "Detailed explanation of the product/service..."
  },
  {
    "question": "How much does it cost?",
    "answer": "Clear pricing information..."
  }
]

**Important Guidelines:**
- Questions should be natural and conversational
- Answers should be comprehensive but concise (2-4 sentences)
- Cover different aspects: features, pricing, support, getting started, troubleshooting
- Make it specific to the business type and product information provided
- Ensure JSON format is valid and properly escaped

Generate the FAQ JSON now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      scenario,
      productInfo,
      userQuestions,
      faqCount = 10,
      model = 'Qwen/Qwen2.5-7B-Instruct'
    } = body

    // 输入验证
    if (!scenario || !productInfo?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 验证FAQ数量
    const count = parseInt(faqCount)
    if (count < 5 || count > 20) {
      return NextResponse.json(
        { error: 'FAQ count must be between 5 and 20' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildFAQPrompt(
      scenario,
      productInfo.trim(),
      userQuestions?.trim() || '',
      count
    )

    // 模型参数配置
    const modelParams = {
      'Qwen/Qwen2.5-7B-Instruct': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      },
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['Qwen/Qwen2.5-7B-Instruct']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let faqs: Array<{question: string, answer: string}>
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      faqs = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!Array.isArray(faqs)) {
        throw new Error('Response is not an array')
      }
      
      // 验证每个FAQ项目
      faqs = faqs.filter(faq => 
        faq && 
        typeof faq.question === 'string' && 
        typeof faq.answer === 'string' &&
        faq.question.trim() && 
        faq.answer.trim()
      )
      
      if (faqs.length === 0) {
        throw new Error('No valid FAQ items found')
      }
      
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取FAQ
      const lines = generatedContent.split('\n').filter(line => line.trim())
      faqs = []
      
      for (let i = 0; i < lines.length - 1; i++) {
        const line = lines[i]?.trim()
        const nextLine = lines[i + 1]?.trim()

        if (line && line.includes('?') && nextLine && !nextLine.includes('?')) {
          faqs.push({
            question: line.replace(/^\d+\.\s*/, '').replace(/^Q:\s*/, ''),
            answer: nextLine.replace(/^A:\s*/, '')
          })
        }
      }
      
      if (faqs.length === 0) {
        throw new Error('Failed to parse FAQ content')
      }
    }

    return NextResponse.json({
      faqs: faqs.slice(0, count), // 确保不超过请求的数量
      metadata: {
        scenario,
        faqCount: faqs.length,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate FAQs' },
      { status: 500 }
    )
  }
}
