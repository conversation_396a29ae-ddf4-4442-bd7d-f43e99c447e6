/**
 * AI API 工具函数
 * 用于管理多个 API keys 的随机选择和负载均衡
 */

/**
 * 获取随机的 API key
 * 从环境变量中的 API keys 池中随机选择一个
 * @returns string - 随机选择的 API key
 */
export function getRandomSiliconFlowApiKey(): string {
  // 获取 API keys 池
  const apiKeysString = process.env.SILICONFLOW_API_KEYS

  if (!apiKeysString) {
    throw new Error('API configuration error')
  }

  // 解析 keys 数组
  const apiKeys = apiKeysString.split(',').map((key: string) => key.trim()).filter((key: string) => key.length > 0)

  if (apiKeys.length === 0) {
    throw new Error('API configuration error')
  }

  // 随机选择一个 key
  const randomIndex = Math.floor(Math.random() * apiKeys.length)
  return apiKeys[randomIndex]!
}

/**
 * 获取 API 的基础 URL
 * @returns string - API 基础 URL
 */
export function getSiliconFlowBaseUrl(): string {
  return process.env.SILICONFLOW_BASE_URL || 'https://api.siliconflow.cn/v1'
}

/**
 * 创建 API 请求的通用 headers
 * 自动使用随机选择的 API key
 * @returns Record<string, string> - 请求头对象
 */
export function createSiliconFlowHeaders(): Record<string, string> {
  const apiKey = getRandomSiliconFlowApiKey()

  return {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
  }
}
