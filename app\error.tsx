'use client'


import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { PageLayout } from '@/components/layout/PageLayout'
import { AlertTriangle, RefreshCw, Home, MessageCircle, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function Error({
  reset,
}: {
  reset: () => void
}) {

  return (
    <PageLayout>
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center px-4 py-16">
        <div className="max-w-lg mx-auto text-center">
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-destructive" />
              </div>
              <CardTitle className="text-2xl mb-2">Page Loading Error</CardTitle>
              <p className="text-muted-foreground">
                Sorry, there was a problem loading this page
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-sm text-muted-foreground">
                <p>Possible causes:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Network connection issues</li>
                  <li>Server temporarily unavailable</li>
                  <li>Page data loading failed</li>
                </ul>
              </div>

              <div className="space-y-3">
                <Button onClick={reset} className="w-full gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Reload Page
                </Button>

                <div className="grid grid-cols-2 gap-3">
                  <Button asChild variant="outline" className="gap-2">
                    <Link href="/">
                      <Home className="h-4 w-4" />
                      Back to Home
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="gap-2">
                    <Link href="/chat">
                      <MessageCircle className="h-4 w-4" />
                      Start Chat
                    </Link>
                  </Button>
                </div>

                <Button asChild variant="ghost" className="w-full gap-2 text-muted-foreground hover:text-foreground">
                  <Link href="/">
                    <ArrowLeft className="h-4 w-4" />
                    Go Back
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  )
}
