import React, { useState } from 'react'
import { Message<PERSON><PERSON>re, Trash2, MoreHorizontal, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useChatHistoryStore } from '@/store/chat-history-store'
import { getTimeGroupLabel, getTimeGroupWeight, getSessionDisplayTime, truncateText } from '@/lib/chat-history-utils'
import { TimeGroup } from '@/types'
import { cn } from '@/lib/utils'

interface ChatHistoryListProps {
  onSessionSelect: (sessionId: string) => void
  className?: string
}

export function ChatHistoryList({ onSessionSelect, className }: ChatHistoryListProps) {
  const {
    sessions,
    currentSessionId,
    getGroupedSessions,
    deleteSession,
    deleteSessionsByTimeGroup,
    clearAllSessions
  } = useChatHistoryStore()

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<{
    type: 'session' | 'group' | 'all'
    id?: string
    group?: TimeGroup
  } | null>(null)

  const groupedSessions = getGroupedSessions()
  const sortedGroups = Object.keys(groupedSessions)
    .map(key => key as TimeGroup)
    .sort((a, b) => getTimeGroupWeight(b) - getTimeGroupWeight(a))

  // 处理会话点击
  const handleSessionClick = (sessionId: string) => {
    onSessionSelect(sessionId)
  }

  // 处理删除确认
  const handleDeleteConfirm = () => {
    if (!deleteTarget) return

    switch (deleteTarget.type) {
      case 'session':
        if (deleteTarget.id) {
          deleteSession(deleteTarget.id)
        }
        break
      case 'group':
        if (deleteTarget.group) {
          deleteSessionsByTimeGroup(deleteTarget.group)
        }
        break
      case 'all':
        clearAllSessions()
        break
    }

    setDeleteDialogOpen(false)
    setDeleteTarget(null)
  }

  // 打开删除对话框
  const openDeleteDialog = (type: 'session' | 'group' | 'all', id?: string, group?: TimeGroup) => {
    setDeleteTarget({
      type,
      ...(id && { id }),
      ...(group && { group })
    })
    setDeleteDialogOpen(true)
  }

  // 获取删除对话框内容
  const getDeleteDialogContent = () => {
    if (!deleteTarget) return { title: '', description: '' }

    switch (deleteTarget.type) {
      case 'session':
        return {
          title: 'Delete Conversation',
          description: 'Are you sure you want to delete this conversation? This action cannot be undone.'
        }
      case 'group':
        const groupLabel = deleteTarget.group ? getTimeGroupLabel(deleteTarget.group) : ''
        return {
          title: `Delete ${groupLabel} Conversations`,
          description: `Are you sure you want to delete all conversations from ${groupLabel}? This action cannot be undone.`
        }
      case 'all':
        return {
          title: 'Clear All Conversations',
          description: 'Are you sure you want to delete all conversation history? This action cannot be undone.'
        }
      default:
        return { title: '', description: '' }
    }
  }

  const dialogContent = getDeleteDialogContent()

  if (sessions.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-8 text-center", className)}>
        <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
        <p className="text-sm text-muted-foreground">No conversations yet</p>
        <p className="text-xs text-muted-foreground mt-1">Start a new chat to see it here</p>
      </div>
    )
  }

  return (
    <>
      <div className={cn("flex flex-col h-full", className)}>
        {/* 头部操作 */}
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="text-sm font-medium">Chat History</h3>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => openDeleteDialog('all')}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All Chats
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 对话列表 */}
        <ScrollArea className="flex-1">
          <div className="p-2 space-y-1">
            {sortedGroups.map(group => {
              const groupSessions = groupedSessions[group]
              if (!groupSessions || groupSessions.length === 0) return null

              return (
                <div key={group} className="space-y-1">
                  {/* 时间分组标题 */}
                  <div className="flex items-center justify-between px-2 py-1">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs font-medium text-muted-foreground">
                        {getTimeGroupLabel(group)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        ({groupSessions.length})
                      </span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-5 w-5">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openDeleteDialog('group', undefined, group)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete {getTimeGroupLabel(group)} Chats
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* 会话列表 */}
                  {groupSessions.map(session => (
                    <div
                      key={session.id}
                      className={cn(
                        "group flex items-start gap-2 p-2 rounded-lg cursor-pointer transition-colors",
                        "hover:bg-accent/50",
                        currentSessionId === session.id && "bg-accent"
                      )}
                      onClick={() => handleSessionClick(session.id)}
                    >
                      <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                      
                      <div className="flex-1 min-w-0 max-w-[200px]">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium truncate max-w-[180px]">
                            {truncateText(session.title, 25)}
                          </h4>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openDeleteDialog('session', session.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Chat
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        
                        {session.previewText && (
                          <p className="text-xs text-muted-foreground truncate mt-1 max-w-full">
                            {truncateText(session.previewText, 45)}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-muted-foreground">
                            {getSessionDisplayTime(session.updatedAt)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {session.messageCount} messages
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )
            })}
          </div>
        </ScrollArea>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogContent.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {dialogContent.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
