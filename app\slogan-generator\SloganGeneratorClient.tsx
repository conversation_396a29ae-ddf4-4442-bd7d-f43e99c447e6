'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, Lightbulb, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Target, Users, MessageSquare, Search } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 行业选项
const INDUSTRIES = [
  { id: 'saas', name: 'SaaS & Tech', description: 'Software and technology products' },
  { id: 'consumer', name: 'Consumer Products', description: 'Physical products and retail' },
  { id: 'web3', name: 'Web3 & Crypto', description: 'Blockchain and cryptocurrency' },
  { id: 'nonprofit', name: 'Non-profit', description: 'Charitable and social causes' },
  { id: 'healthcare', name: 'Healthcare', description: 'Medical and wellness services' },
  { id: 'education', name: 'Education', description: 'Learning and training platforms' },
  { id: 'finance', name: 'Finance', description: 'Financial services and fintech' },
  { id: 'ecommerce', name: 'E-commerce', description: 'Online retail and marketplaces' },
  { id: 'food', name: 'Food & Beverage', description: 'Restaurants and food products' },
  { id: 'custom', name: 'Custom Industry', description: 'Define your own industry' }
]

// 调性选项
const TONES = [
  { id: 'minimal', name: 'Minimal', description: 'Clean, simple, and elegant' },
  { id: 'humorous', name: 'Humorous', description: 'Fun, witty, and playful' },
  { id: 'premium', name: 'Premium', description: 'Luxury, sophisticated, high-end' },
  { id: 'rebellious', name: 'Rebellious', description: 'Bold, disruptive, edgy' },
  { id: 'friendly', name: 'Friendly', description: 'Warm, approachable, casual' },
  { id: 'professional', name: 'Professional', description: 'Serious, trustworthy, corporate' },
  { id: 'innovative', name: 'Innovative', description: 'Cutting-edge, forward-thinking' },
  { id: 'inspiring', name: 'Inspiring', description: 'Motivational, uplifting, empowering' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, multiple language styles'
  },
  {
    id: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
    name: 'DeepSeek',
    description: 'Strong reasoning, marketing strategy analysis'
  }
]

interface GeneratedSlogan {
  slogan: string
  explanation: string
  domainSuggestion: string
  domainAvailable?: boolean | null
  checkingDomain?: boolean
}

export function SloganGeneratorClient() {
  // 表单状态
  const [industry, setIndustry] = useState('')
  const [customIndustry, setCustomIndustry] = useState('')
  const [tone, setTone] = useState('')
  const [productDescription, setProductDescription] = useState('')
  const [targetAudience, setTargetAudience] = useState('')
  const [keywords, setKeywords] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedSlogans, setGeneratedSlogans] = useState<GeneratedSlogan[]>([])
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成口号
  const handleGenerate = async () => {
    if (!industry || !tone || !productDescription.trim() || !targetAudience.trim() || !keywords.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/slogan-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          industry: industry === 'custom' ? customIndustry : industry,
          tone,
          productDescription: productDescription.trim(),
          targetAudience: targetAudience.trim(),
          keywords: keywords.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate slogans')
      }

      const data = await response.json()
      setGeneratedSlogans(data.slogans.map((slogan: any) => ({
        ...slogan,
        domainAvailable: null,
        checkingDomain: false
      })))

      toast({
        title: "Slogans Generated!",
        description: `Generated ${data.slogans.length} creative slogans for your brand.`,
      })

    } catch (error) {
      setError('Failed to generate slogans. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 检查单个域名
  const checkSingleDomain = async (index: number) => {
    const slogan = generatedSlogans[index]
    if (!slogan || !slogan.domainSuggestion) return

    // 更新状态为检查中
    const updatedSlogans = [...generatedSlogans]
    updatedSlogans[index] = { ...slogan, checkingDomain: true }
    setGeneratedSlogans(updatedSlogans)

    try {
      const response = await fetch('/api/slogan-generator/check-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: slogan.domainSuggestion
        }),
      })

      const data = await response.json()

      // 更新域名可用性状态
      updatedSlogans[index] = {
        slogan: slogan.slogan,
        explanation: slogan.explanation,
        domainSuggestion: slogan.domainSuggestion,
        domainAvailable: data.available,
        checkingDomain: false
      }
      setGeneratedSlogans(updatedSlogans)

    } catch (error) {
      // 检查失败，重置状态
      updatedSlogans[index] = {
        slogan: slogan.slogan,
        explanation: slogan.explanation,
        domainSuggestion: slogan.domainSuggestion,
        domainAvailable: slogan.domainAvailable || null,
        checkingDomain: false
      }
      setGeneratedSlogans(updatedSlogans)
      
      toast({
        title: "Domain Check Failed",
        description: "Unable to check domain availability.",
        variant: "destructive"
      })
    }
  }

  // 批量检查所有域名
  const checkAllDomains = async () => {
    const domains = generatedSlogans
      .filter(slogan => slogan.domainSuggestion)
      .map(slogan => slogan.domainSuggestion)

    if (domains.length === 0) return

    // 设置所有域名为检查中状态
    const updatedSlogans = generatedSlogans.map(slogan => ({
      ...slogan,
      checkingDomain: slogan.domainSuggestion ? true : false
    }))
    setGeneratedSlogans(updatedSlogans)

    try {
      const response = await fetch('/api/slogan-generator/check-domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domains
        }),
      })

      const data = await response.json()
      
      // 更新所有域名的可用性状态
      const finalSlogans = generatedSlogans.map(slogan => {
        if (slogan.domainSuggestion) {
          const domainResult = data.results.find((r: any) => r.domain === slogan.domainSuggestion)
          return {
            ...slogan,
            domainAvailable: domainResult?.available || false,
            checkingDomain: false
          }
        }
        return slogan
      })
      setGeneratedSlogans(finalSlogans)

      toast({
        title: "Domain Check Complete",
        description: `Checked ${domains.length} domains for availability.`,
      })

    } catch (error) {
      // 批量检查失败，重置所有状态
      const resetSlogans = generatedSlogans.map(slogan => ({
        ...slogan,
        checkingDomain: false
      }))
      setGeneratedSlogans(resetSlogans)
      
      toast({
        title: "Batch Check Failed",
        description: "Unable to check domain availability.",
        variant: "destructive"
      })
    }
  }

  // 复制口号到剪贴板
  const copySlogan = async (slogan: string) => {
    try {
      await navigator.clipboard.writeText(slogan)
      toast({
        title: "Copied!",
        description: "Slogan copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950 dark:to-orange-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
                AI Brand Slogan Storm
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Create Catchy Slogans
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>powerful brand slogans and taglines</strong> instantly. 
              Choose your industry and tone, get creative English slogans with domain checks.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Generation
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Domain Check
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                Multiple Tones
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-primary" />
                      Slogan Configuration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Industry Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Industry *</label>
                      <Select value={industry} onValueChange={setIndustry}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your industry" />
                        </SelectTrigger>
                        <SelectContent>
                          {INDUSTRIES.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Industry Input */}
                    {industry === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Industry *</label>
                        <input
                          type="text"
                          placeholder="Describe your industry"
                          value={customIndustry}
                          onChange={(e) => setCustomIndustry(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Tone Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Brand Tone *</label>
                      <Select value={tone} onValueChange={setTone}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose brand tone" />
                        </SelectTrigger>
                        <SelectContent>
                          {TONES.map((item) => (
                            <SelectItem key={item.id} value={item.id}>
                              <div>
                                <div className="font-medium">{item.name}</div>
                                <div className="text-xs text-muted-foreground">{item.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Product Description */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Product Description *</label>
                      <Textarea
                        placeholder="Describe your product or service in one sentence..."
                        value={productDescription}
                        onChange={(e) => setProductDescription(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    {/* Target Audience */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Target Audience *</label>
                      <input
                        type="text"
                        placeholder="e.g., Small business owners, Gen Z consumers, Tech professionals"
                        value={targetAudience}
                        onChange={(e) => setTargetAudience(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                    </div>

                    {/* Keywords */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Key Words *</label>
                      <input
                        type="text"
                        placeholder="e.g., fast, reliable, innovative (separate with commas)"
                        value={keywords}
                        onChange={(e) => setKeywords(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter 3-5 keywords that represent your brand values
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Slogans...
                        </>
                      ) : (
                        <>
                          <Lightbulb className="h-4 w-4 mr-2" />
                          Generate Brand Slogans
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-primary" />
                        Generated Slogans
                      </span>
                      {generatedSlogans.length > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={checkAllDomains}
                          disabled={generatedSlogans.some(s => s.checkingDomain)}
                        >
                          <Search className="h-4 w-4 mr-1" />
                          Check All Domains
                        </Button>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedSlogans.length > 0 ? (
                      <div className="space-y-4">
                        {generatedSlogans.map((item, index) => (
                          <div key={index} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-lg mb-1">{item.slogan}</h4>
                                <p className="text-sm text-muted-foreground mb-2">{item.explanation}</p>
                                {item.domainSuggestion && (
                                  <div className="flex items-center gap-2 text-sm">
                                    <span className="text-muted-foreground">Domain:</span>
                                    <code className="bg-muted px-2 py-1 rounded text-xs">
                                      {item.domainSuggestion}
                                    </code>
                                    {item.domainAvailable === true && (
                                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 hover:bg-gray-100 hover:text-gray-700 transition-colors cursor-default">
                                        Available
                                      </Badge>
                                    )}
                                    {item.domainAvailable === false && (
                                      <Badge variant="secondary" className="text-xs bg-red-100 text-red-800 hover:bg-gray-100 hover:text-gray-700 transition-colors cursor-default">
                                        Taken
                                      </Badge>
                                    )}
                                  </div>
                                )}
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => copySlogan(item.slogan)}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                                {item.domainSuggestion && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => checkSingleDomain(index)}
                                    disabled={item.checkingDomain}
                                  >
                                    {item.checkingDomain ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <Search className="h-4 w-4" />
                                    )}
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Lightbulb className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your brand slogans will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Choose Our AI Slogan Generator?
            </h2>
            <p className="text-xl text-muted-foreground">
              Create memorable brand slogans that resonate with your audience and drive business growth
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-yellow-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Generate 10 unique, creative slogans in seconds with AI-powered creativity
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Industry-Specific</h3>
                <p className="text-sm text-muted-foreground">
                  Tailored slogans for your specific industry and target audience
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Tones</h3>
                <p className="text-sm text-muted-foreground">
                  Choose from 8 different brand tones to match your company personality
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Search className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Domain Check</h3>
                <p className="text-sm text-muted-foreground">
                  Instantly check domain availability for your slogans and brand names
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Audience-Focused</h3>
                <p className="text-sm text-muted-foreground">
                  Slogans crafted to appeal specifically to your target demographic
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Ready to Use</h3>
                <p className="text-sm text-muted-foreground">
                  Professional-quality slogans ready for marketing campaigns and branding
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create powerful brand slogans in 4 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-yellow-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Industry & Tone</h3>
              <p className="text-sm text-muted-foreground">
                Select your business industry and desired brand tone from our comprehensive options
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Describe Your Brand</h3>
              <p className="text-sm text-muted-foreground">
                Provide your product description, target audience, and key brand values
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Generate Slogans</h3>
              <p className="text-sm text-muted-foreground">
                Our AI creates 10 unique, memorable slogans tailored to your specifications
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">4</span>
              </div>
              <h3 className="font-semibold mb-2">Check Domains</h3>
              <p className="text-sm text-muted-foreground">
                Verify domain availability and choose the perfect slogan for your brand
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Perfect for Every Business
            </h2>
            <p className="text-xl text-muted-foreground">
              Create compelling slogans for any industry or business type
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💻</span>
                </div>
                <h3 className="font-semibold mb-2">SaaS & Tech</h3>
                <p className="text-sm text-muted-foreground">
                  Innovation-focused slogans for software and technology companies
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🛍️</span>
                </div>
                <h3 className="font-semibold mb-2">Consumer Products</h3>
                <p className="text-sm text-muted-foreground">
                  Catchy, memorable slogans for retail and consumer brands
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="font-semibold mb-2">Startups</h3>
                <p className="text-sm text-muted-foreground">
                  Bold, disruptive slogans for innovative startup companies
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6 text-center">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">❤️</span>
                </div>
                <h3 className="font-semibold mb-2">Non-Profit</h3>
                <p className="text-sm text-muted-foreground">
                  Inspiring, mission-driven slogans for charitable organizations
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about our AI slogan generator
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "How does the AI slogan generator work?",
                  answer: "Our AI analyzes your industry, brand tone, product description, and target audience to create unique, memorable slogans that resonate with your specific market and brand identity."
                },
                {
                  question: "Can I use the generated slogans commercially?",
                  answer: "Yes, all generated slogans are original creations that you can use for your business. However, we recommend checking for trademark conflicts before finalizing your choice."
                },
                {
                  question: "How accurate is the domain availability check?",
                  answer: "Our domain checker uses DNS queries to verify availability. While highly accurate, we recommend double-checking with a domain registrar before making final decisions."
                },
                {
                  question: "What makes a good brand slogan?",
                  answer: "Great slogans are memorable, concise (3-7 words), reflect your brand values, appeal to your target audience, and differentiate you from competitors. Our AI considers all these factors."
                },
                {
                  question: "Can I generate slogans for multiple brands?",
                  answer: "Absolutely! You can generate slogans for as many brands or products as needed. Each generation creates 10 unique options tailored to your specific inputs."
                },
                {
                  question: "What if I don't like any of the generated slogans?",
                  answer: "Try adjusting your inputs - change the tone, refine your product description, or modify your keywords. Different inputs often yield completely different creative directions."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
