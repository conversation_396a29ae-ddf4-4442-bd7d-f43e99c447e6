'use client'

import React from 'react'
import { X, Settings, Globe, MessageSquare, Target, BookOpen } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { 
  RESPONSE_LENGTH_CONFIG, 
  TARGET_AGES, 
  SUPPORTED_LANGUAGES 
} from '@/lib/constants'
import type { ChatSettings as ChatSettingsType, Language, ResponseLength } from '@/types'

interface ChatSettingsProps {
  settings: ChatSettingsType
  onSettingsChange: (settings: Partial<ChatSettingsType>) => void
  onClose: () => void
  className?: string
}

export function ChatSettings({
  settings,
  onSettingsChange,
  onClose,
  className
}: ChatSettingsProps) {
  // 确保settings有有效的responseLength值
  const safeSettings = {
    ...settings,
    responseLength: RESPONSE_LENGTH_CONFIG[settings.responseLength] ? settings.responseLength : 'STANDARD'
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Chat Settings</h2>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* 语言设置 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <Globe className="h-4 w-4 text-blue-500" />
              <CardTitle className="text-base">Language Preference</CardTitle>
            </div>
            <CardDescription>
              Choose the language and cultural background for responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={safeSettings.language}
              onValueChange={(value: Language) =>
                onSettingsChange({ language: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_LANGUAGES.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    <div className="flex items-center space-x-2">
                      <span>{lang.nativeName}</span>
                      <span className="text-muted-foreground">({lang.name})</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* 回答长度 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-green-500" />
              <CardTitle className="text-base">Response Length</CardTitle>
            </div>
            <CardDescription>
              Control the level of detail in responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={safeSettings.responseLength}
              onValueChange={(value: ResponseLength) =>
                onSettingsChange({ responseLength: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(RESPONSE_LENGTH_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center space-x-2">
                      <span>{config?.icon || '📝'}</span>
                      <div>
                        <div className="font-medium">{config?.label || key}</div>
                        <div className="text-xs text-muted-foreground">
                          {config?.description || 'Response configuration'}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* 目标年龄 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-purple-500" />
              <CardTitle className="text-base">Understanding Level</CardTitle>
            </div>
            <CardDescription>
              Adjust the complexity of explanations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={safeSettings.targetAge.toString()}
              onValueChange={(value) =>
                onSettingsChange({ targetAge: parseInt(value) })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TARGET_AGES.map((age) => (
                  <SelectItem key={age.value} value={age.value.toString()}>
                    <div>
                      <div className="font-medium">{age.label}</div>
                      <div className="text-xs text-muted-foreground">
                        {age.description}
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* 增强功能 */}
        <Card className="md:col-span-2 lg:col-span-3">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-4 w-4 text-orange-500" />
              <CardTitle className="text-base">Enhanced Features</CardTitle>
            </div>
            <CardDescription>
              Make responses more vivid and interesting
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* 包含例子 */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Include Examples</span>
                    <Badge variant="secondary" className="text-xs">Recommended</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Use specific examples to illustrate concepts
                  </p>
                </div>
                <Switch
                  checked={safeSettings.includeExamples}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ includeExamples: checked })
                  }
                />
              </div>

              {/* 使用类比 */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Use Analogies</span>
                    <Badge variant="secondary" className="text-xs">Recommended</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Use everyday things as metaphors
                  </p>
                </div>
                <Switch
                  checked={safeSettings.useAnalogies}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ useAnalogies: checked })
                  }
                />
              </div>

              {/* 显示思考过程 */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Show Thinking</span>
                    <Badge variant="outline" className="text-xs">Experimental</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Show AI's thinking process
                  </p>
                </div>
                <Switch
                  checked={safeSettings.showThinking}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showThinking: checked })
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 预设配置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Quick Configuration</CardTitle>
          <CardDescription>
            Apply common setting combinations with one click
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
            <Button
              variant="outline"
              className="justify-start"
              onClick={() => onSettingsChange({
                responseLength: 'BRIEF',
                targetAge: 8,
                includeExamples: true,
                useAnalogies: true,
                showThinking: false
              })}
            >
              <span className="mr-2">🧒</span>
              Kids Mode
            </Button>

            <Button
              variant="outline"
              className="justify-start"
              onClick={() => onSettingsChange({
                responseLength: 'STANDARD',
                targetAge: 16,
                includeExamples: true,
                useAnalogies: true,
                showThinking: false
              })}
            >
              <span className="mr-2">🎓</span>
              Student Mode
            </Button>

            <Button
              variant="outline"
              className="justify-start"
              onClick={() => onSettingsChange({
                responseLength: 'COMPREHENSIVE',
                targetAge: 18,
                includeExamples: true,
                useAnalogies: false,
                showThinking: true
              })}
            >
              <span className="mr-2">💼</span>
              Professional Mode
            </Button>

            <Button
              variant="outline"
              className="justify-start"
              onClick={() => onSettingsChange({
                responseLength: 'BRIEF',
                targetAge: 18,
                includeExamples: false,
                useAnalogies: false,
                showThinking: false
              })}
            >
              <span className="mr-2">⚡</span>
              Quick Mode
            </Button>

            <Button
              variant="outline"
              className="justify-start"
              onClick={() => onSettingsChange({
                responseLength: 'THOROUGH',
                targetAge: 18,
                includeExamples: true,
                useAnalogies: true,
                showThinking: true
              })}
            >
              <span className="mr-2">🔬</span>
              Expert Mode
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 当前配置摘要 */}
      <div className="bg-muted/50 rounded-lg p-4">
        <h3 className="font-medium mb-2">Current Configuration</h3>
        <div className="flex flex-wrap gap-2 text-sm">
          <Badge variant="outline">
            {SUPPORTED_LANGUAGES.find(l => l.code === safeSettings.language)?.nativeName}
          </Badge>
          <Badge variant="outline">
            {RESPONSE_LENGTH_CONFIG[safeSettings.responseLength]?.label || safeSettings.responseLength}
          </Badge>
          <Badge variant="outline">
            {TARGET_AGES.find(a => a.value === safeSettings.targetAge)?.label}
          </Badge>
          {safeSettings.includeExamples && (
            <Badge variant="secondary">Include Examples</Badge>
          )}
          {safeSettings.useAnalogies && (
            <Badge variant="secondary">Use Analogies</Badge>
          )}
          {safeSettings.showThinking && (
            <Badge variant="secondary">Show Thinking</Badge>
          )}
        </div>
      </div>
    </div>
  )
}
