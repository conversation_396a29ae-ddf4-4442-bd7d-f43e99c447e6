import { PageLayout } from '@/components/layout/PageLayout'
import { PodcastTitleBrainstormClient } from './PodcastTitleBrainstormClient'
import type { Metadata } from 'next'

// Podcast Title Brainstorm页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Podcast Title Generator - Create Catchy Episode Titles | HumanWhisper',
  description: 'Generate engaging podcast episode titles with SEO keywords. Create clickable titles for tech, business, storytelling podcasts that boost discovery.',
  keywords: [
    'podcast title generator',
    'episode title creator',
    'podcast SEO',
    'podcast marketing',
    'title brainstorm',
    'podcast naming',
    'content marketing'
  ],
  openGraph: {
    title: 'AI Podcast Title Generator - Create Catchy Episode Titles | HumanWhisper',
    description: 'Generate engaging podcast episode titles with SEO keywords. Create clickable titles for tech, business, storytelling podcasts that boost discovery.',
    url: '/podcast-title-brainstorm',
    type: 'website',
  },
  twitter: {
    title: 'AI Podcast Title Generator - Create Catchy Episode Titles | HumanWhisper',
    description: 'Generate engaging podcast episode titles with SEO keywords. Create clickable titles for tech, business, storytelling podcasts that boost discovery.',
  },
  alternates: {
    canonical: '/podcast-title-brainstorm',
  },
}

export default function PodcastTitleBrainstormPage() {
  return (
    <PageLayout>
      <PodcastTitleBrainstormClient />
    </PageLayout>
  )
}
