'use client'

import React from 'react'
import Image from 'next/image'
import { useUser } from '@clerk/nextjs'

export function WelcomeMessage() {
  const { user, isLoaded } = useUser()

  // 获取用户的显示名称
  const getUserDisplayName = () => {
    if (!isLoaded || !user) return null

    // 优先使用 firstName，然后是 username，最后是 email 的用户名部分
    if (user.firstName) {
      return user.firstName
    }

    if (user.username) {
      return user.username
    }

    if (user.primaryEmailAddress?.emailAddress) {
      return user.primaryEmailAddress.emailAddress.split('@')[0]
    }

    return null
  }

  // 根据时间生成问候语
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  const displayName = getUserDisplayName()
  const greeting = getGreeting()
  return (
    <div className="flex flex-col items-center justify-center h-full max-w-2xl mx-auto p-8 text-center">
      <div className="flex items-center justify-center mb-6">
        <Image
          src="/logo-659x128.png"
          alt="HumanWhisper"
          width={200}
          height={39}
          className="h-8 w-auto sm:h-10 sm:w-auto md:h-12 md:w-auto"
          priority
        />
      </div>
      
      <h2 className="text-2xl font-semibold text-foreground mb-4">
        {displayName ? `${greeting}, ${displayName}! 👋` : 'Hello! Welcome to HumanWhisper'}
      </h2>

      <p className="text-lg text-muted-foreground">
        {displayName
          ? "I'm here to explain complex topics in simple, human-friendly language. What would you like to learn about today?"
          : "AI that explains complex topics in simple, human-friendly language"
        }
      </p>
    </div>
  )
}
