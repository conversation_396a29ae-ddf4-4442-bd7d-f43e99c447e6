import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { upgradeUserPlan } from '@/lib/user-management'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ 
        error: 'Unauthorized' 
      }, { status: 401 })
    }

    const body = await request.json()
    const { subscriptionId, orderId, customerId } = body

    // 升级用户到Premium计划
    await upgradeUserPlan(userId, 'premium', subscriptionId)

    // 计算30天后的订阅到期时间
    const subscriptionEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()

    return NextResponse.json({
      success: true,
      message: 'User upgraded to Premium successfully',
      credits: 3600, // 正式环境积分
      plan: 'premium',
      subscriptionEnd,
      orderId,
      customerId
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
