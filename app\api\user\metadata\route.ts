import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { getUserMetadata } from '@/lib/user-management'

export async function GET() {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const metadata = await getUserMetadata(userId)
    
    return NextResponse.json(metadata)
  } catch (error) {
    console.error('Get user metadata error:', error)
    return NextResponse.json(
      { error: 'Failed to get user metadata' },
      { status: 500 }
    )
  }
}
