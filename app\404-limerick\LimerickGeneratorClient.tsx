'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { FAQ } from '@/components/ui/faq'
import { Loader2, FileX, Copy, CheckCircle, Sparkles, Zap, Shield, Globe, Smile, Code, Heart, Lightbulb } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 品牌调性选项
const BRAND_TONES = [
  { id: 'geeky', name: 'Gee<PERSON>', description: 'Tech-savvy, nerdy humor with programming references' },
  { id: 'cute', name: 'Cute', description: 'Playful, adorable, and friendly tone' },
  { id: 'dark', name: '<PERSON>', description: 'Edgy, mysterious, and sophisticated humor' },
  { id: 'minimal', name: 'Minimal', description: 'Clean, simple, and understated approach' },
  { id: 'quirky', name: 'Quirky', description: 'Unique, eccentric, and unconventional style' },
  { id: 'professional', name: 'Professional', description: 'Business-appropriate with subtle humor' },
  { id: 'playful', name: 'Playful', description: 'Fun, energetic, and lighthearted tone' },
  { id: 'witty', name: 'Witty', description: 'Clever, smart, and intellectually humorous' },
  { id: 'custom', name: 'Custom Tone', description: 'Define your own brand personality' }
]

// 支持的AI模型
const AI_MODELS = [
  {
    id: 'THUDM/glm-4-9b-chat',
    name: 'GLM-4',
    description: 'Creative writing, humor expression, brand copy'
  },
  {
    id: 'Qwen/Qwen2.5-7B-Instruct',
    name: 'Qwen2.5',
    description: 'Structured output, HTML format generation'
  }
]

interface GeneratedLimerick {
  limerick: string
  cta: string
  htmlCode: string
  explanation: string
}

export function LimerickGeneratorClient() {
  // 表单状态
  const [brandTone, setBrandTone] = useState('')
  const [customTone, setCustomTone] = useState('')
  const [brandName, setBrandName] = useState('')
  const [productDescription, setProductDescription] = useState('')
  const [selectedModel, setSelectedModel] = useState(AI_MODELS[0]?.id || 'THUDM/glm-4-9b-chat')
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLimerick, setGeneratedLimerick] = useState<GeneratedLimerick | null>(null)
  const [error, setError] = useState('')
  
  const { toast } = useToast()

  // 生成404打油诗
  const handleGenerate = async () => {
    if (!brandTone || !brandName.trim() || !productDescription.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setError('')
    
    try {
      const response = await fetch('/api/404-limerick', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandTone: brandTone === 'custom' ? customTone : brandTone,
          brandName: brandName.trim(),
          productDescription: productDescription.trim(),
          model: selectedModel
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate limerick')
      }

      const data = await response.json()
      setGeneratedLimerick(data.limerick)

      toast({
        title: "404 Limerick Generated!",
        description: "Creative error page content created successfully.",
      })

    } catch (error) {
      setError('Failed to generate limerick. Please try again.')
      toast({
        title: "Generation Failed",
        description: "Please try again or contact support.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  // 复制内容到剪贴板
  const copyToClipboard = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard.`,
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please copy manually.",
        variant: "destructive"
      })
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950 dark:to-pink-950">
        <div className="container text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
              <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                AI 404 Page Limerick
              </span>
              <br />
              <span className="text-3xl md:text-4xl text-muted-foreground">
                Creative Error Pages
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
              Generate <strong>creative 404 error page limericks</strong> for your brand. 
              Transform boring error pages into engaging brand experiences with humor.
            </p>

            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <Badge variant="secondary" className="px-3 py-1">
                <Sparkles className="h-4 w-4 mr-1" />
                100% Free
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Zap className="h-4 w-4 mr-1" />
                Instant Poetry
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Shield className="h-4 w-4 mr-1" />
                Brand Aligned
              </Badge>
              <Badge variant="secondary" className="px-3 py-1">
                <Globe className="h-4 w-4 mr-1" />
                HTML Ready
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Main Generator Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileX className="h-5 w-5 text-primary" />
                      404 Limerick Setup
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Brand Tone */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Brand Tone *</label>
                      <Select value={brandTone} onValueChange={setBrandTone}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose your brand personality" />
                        </SelectTrigger>
                        <SelectContent>
                          {BRAND_TONES.map((tone) => (
                            <SelectItem key={tone.id} value={tone.id}>
                              <div>
                                <div className="font-medium">{tone.name}</div>
                                <div className="text-xs text-muted-foreground">{tone.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Custom Tone Input */}
                    {brandTone === 'custom' && (
                      <div>
                        <label className="text-sm font-medium mb-2 block">Custom Brand Tone *</label>
                        <input
                          type="text"
                          placeholder="Describe your brand personality"
                          value={customTone}
                          onChange={(e) => setCustomTone(e.target.value)}
                          className="w-full px-3 py-2 border border-input rounded-md"
                        />
                      </div>
                    )}

                    {/* Brand Name */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Brand Name *</label>
                      <input
                        type="text"
                        placeholder="Enter your brand or company name"
                        value={brandName}
                        onChange={(e) => setBrandName(e.target.value)}
                        className="w-full px-3 py-2 border border-input rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        This will be incorporated into the limerick
                      </p>
                    </div>

                    {/* Product Description */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Product Description *</label>
                      <Textarea
                        placeholder="Describe your product or service in one sentence..."
                        value={productDescription}
                        onChange={(e) => setProductDescription(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Brief description to provide context for the limerick
                      </p>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">AI Model</label>
                      <Select value={selectedModel} onValueChange={setSelectedModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {AI_MODELS.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-xs text-muted-foreground">{model.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Generate Button */}
                    <Button 
                      onClick={handleGenerate} 
                      disabled={isGenerating}
                      className="w-full"
                      size="lg"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Limerick...
                        </>
                      ) : (
                        <>
                          <FileX className="h-4 w-4 mr-2" />
                          Generate 404 Limerick
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Output Section */}
              <div className="space-y-6">
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Generated 404 Page
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-md">
                        {error}
                      </div>
                    )}
                    
                    {generatedLimerick ? (
                      <div className="space-y-6">
                        {/* Limerick Display */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">404 Limerick</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedLimerick.limerick, 'Limerick')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950 dark:to-pink-950 p-4 rounded-lg">
                            <pre className="whitespace-pre-wrap text-sm font-serif leading-relaxed">
                              {generatedLimerick.limerick}
                            </pre>
                          </div>
                        </div>

                        {/* CTA Display */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">Call to Action</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedLimerick.cta, 'CTA')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy
                            </Button>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                            <p className="text-sm font-medium">
                              {generatedLimerick.cta}
                            </p>
                          </div>
                        </div>

                        {/* HTML Code Display */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">HTML Code</h4>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => copyToClipboard(generatedLimerick.htmlCode, 'HTML Code')}
                            >
                              <Copy className="h-4 w-4 mr-1" />
                              Copy HTML
                            </Button>
                          </div>
                          <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-lg">
                            <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-x-auto">
                              {generatedLimerick.htmlCode}
                            </pre>
                          </div>
                        </div>

                        {/* Explanation */}
                        <div className="bg-muted/50 p-3 rounded-lg">
                          <p className="text-sm text-muted-foreground">
                            <strong>Strategy:</strong> {generatedLimerick.explanation}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <FileX className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Your 404 limerick will appear here</p>
                        <p className="text-sm">Fill in the form and click generate</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Why Create Custom 404 Pages?
            </h2>
            <p className="text-xl text-muted-foreground">
              Transform error pages into brand opportunities that delight users and reduce bounce rates
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Smile className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="font-semibold mb-2">Brand Personality</h3>
                <p className="text-sm text-muted-foreground">
                  Show your brand's human side with humor that matches your personality
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">User Experience</h3>
                <p className="text-sm text-muted-foreground">
                  Turn frustrating moments into delightful experiences that users remember
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Creative Poetry</h3>
                <p className="text-sm text-muted-foreground">
                  Traditional limerick format with modern brand messaging and humor
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Code className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Ready-to-Use HTML</h3>
                <p className="text-sm text-muted-foreground">
                  Complete HTML code with styling, ready to implement on your website
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Instant Generation</h3>
                <p className="text-sm text-muted-foreground">
                  Generate custom 404 pages in seconds, no creative writing skills needed
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold mb-2">Brand Consistency</h3>
                <p className="text-sm text-muted-foreground">
                  Maintains your brand voice and tone even in error situations
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Create your custom 404 limerick in 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="h-16 w-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-red-600">1</span>
              </div>
              <h3 className="font-semibold mb-2">Choose Brand Tone</h3>
              <p className="text-sm text-muted-foreground">
                Select from geeky, cute, dark, minimal, or other personality types that match your brand
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="font-semibold mb-2">Add Brand Details</h3>
              <p className="text-sm text-muted-foreground">
                Input your brand name and a brief product description for context
              </p>
            </div>

            <div className="text-center">
              <div className="h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">3</span>
              </div>
              <h3 className="font-semibold mb-2">Get Complete Package</h3>
              <p className="text-sm text-muted-foreground">
                Receive limerick, CTA, and ready-to-use HTML code for immediate implementation
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Examples Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Limerick Examples by Brand Tone
            </h2>
            <p className="text-xl text-muted-foreground">
              See how different brand personalities create unique 404 experiences
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🤓</span>
                  Geeky Tone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4">
                  <pre className="text-sm font-serif leading-relaxed">
{`A 404 error has struck our code base,
Your request got lost in cyberspace,
Our servers are fine,
Just crossed the wrong line,
But we'll debug this digital chase!`}
                  </pre>
                </div>
                <p className="text-sm text-muted-foreground">
                  Perfect for tech companies, SaaS platforms, and developer tools
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🥰</span>
                  Cute Tone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-pink-50 dark:bg-pink-950 p-4 rounded-lg mb-4">
                  <pre className="text-sm font-serif leading-relaxed">
{`Our little page went out to play,
And seems to have wandered away,
Don't worry or fret,
We'll find it, you bet!
Come back and visit another day!`}
                  </pre>
                </div>
                <p className="text-sm text-muted-foreground">
                  Ideal for family brands, children's products, and friendly services
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">🖤</span>
                  Dark Tone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-800 text-gray-100 p-4 rounded-lg mb-4">
                  <pre className="text-sm font-serif leading-relaxed">
{`In shadows deep, your page has fled,
Through digital realms of the undead,
The path you seek,
Is rather bleak,
But fear not—choose another thread.`}
                  </pre>
                </div>
                <p className="text-sm text-muted-foreground">
                  Great for gaming companies, entertainment brands, and edgy products
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <span className="text-2xl">✨</span>
                  Minimal Tone
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4">
                  <pre className="text-sm font-serif leading-relaxed">
{`Page not found, path unclear,
Simple truth: nothing here,
Clean and neat,
Redirect feet,
Home awaits, crystal clear.`}
                  </pre>
                </div>
                <p className="text-sm text-muted-foreground">
                  Perfect for design agencies, luxury brands, and minimalist products
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to know about creating custom 404 limericks
              </p>
            </div>

            <FAQ
              items={[
                {
                  question: "What is a limerick and why use it for 404 pages?",
                  answer: "A limerick is a 5-line humorous poem with an AABBA rhyme scheme. It's perfect for 404 pages because it's memorable, entertaining, and can turn a frustrating experience into a delightful brand moment."
                },
                {
                  question: "Can I customize the generated limerick?",
                  answer: "Absolutely! The generated limerick is a starting point. You can edit the text, adjust the tone, or modify the HTML code to perfectly match your brand needs."
                },
                {
                  question: "Is the HTML code ready to use on my website?",
                  answer: "Yes! The generated HTML includes proper structure, basic styling, and responsive design. You can use it as-is or integrate it into your existing website design system."
                },
                {
                  question: "How do I implement the 404 page on my website?",
                  answer: "Implementation depends on your platform. For most websites, you'll need to create a 404.html file or configure your server to display the custom page when a 404 error occurs."
                },
                {
                  question: "Can I generate multiple versions for A/B testing?",
                  answer: "Yes! Generate different versions with various brand tones to test which resonates best with your audience and reduces bounce rates."
                },
                {
                  question: "Are the limericks appropriate for all audiences?",
                  answer: "All generated content is family-friendly and professional. The AI ensures the humor is appropriate while maintaining your brand's personality and values."
                }
              ]}
            />
          </div>
        </div>
      </section>
    </>
  )
}
