import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 政策类型映射
const POLICY_TYPE_MAP: Record<string, string> = {
  'privacy_policy': 'Privacy Policy',
  'terms_of_service': 'Terms of Service',
  'subscription_agreement': 'Subscription Agreement',
  'cookie_policy': 'Cookie Policy',
  'data_processing': 'Data Processing Agreement',
  'user_agreement': 'User Agreement',
  'license_agreement': 'License Agreement',
  'rental_contract': 'Rental Contract'
}

// 构建政策分析prompt
function buildAnalysisPrompt(
  policyType: string,
  content: string
): string {
  const typeDesc = POLICY_TYPE_MAP[policyType] || policyType

  const prompt = `You are a legal document analyst specializing in simplifying complex policies and agreements for everyday users. Your task is to create a clear, concise TL;DR summary.

**Document Type:** ${typeDesc}

**Original Document:**
"""
${content}
"""

**Instructions:**
Create a comprehensive TL;DR summary that includes:

1. **WHAT IT IS**: Brief explanation of the document's purpose
2. **KEY POINTS**: 5-7 most important provisions that users should know
3. **YOUR RIGHTS**: What rights you have as a user/customer
4. **YOUR OBLIGATIONS**: What you're required to do or not do
5. **RISKS & CONCERNS**: Potential risks, data usage, or concerning clauses
6. **IMPORTANT DETAILS**: Cancellation, refunds, liability, data retention, etc.

**Format Requirements:**
- Use clear, simple English (avoid legal jargon)
- Use bullet points and short paragraphs for readability
- Highlight critical information that could affect users
- Be objective and factual, not promotional
- Focus on practical implications for users
- Keep the summary comprehensive but concise (aim for 300-500 words)
- OUTPUT IN PLAIN TEXT FORMAT - NO MARKDOWN SYMBOLS (no *, #, **, etc.)

**Example Structure:**
TL;DR: [Document Type]

What it is: [Brief explanation]

Key Points:
• [Important provision 1]
• [Important provision 2]
• [etc.]

Your Rights:
• [Right 1]
• [Right 2]

Your Obligations:
• [Obligation 1]
• [Obligation 2]

Risks & Concerns:
• [Risk 1]
• [Risk 2]

Important Details:
• [Detail 1]
• [Detail 2]

Generate the TL;DR summary now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      policyType,
      content,
      model = 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B'
    } = body

    // 输入验证
    if (!policyType || !content?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 检查内容长度
    if (content.length < 100) {
      return NextResponse.json(
        { error: 'Content is too short for analysis' },
        { status: 400 }
      )
    }

    if (content.length > 100000) {
      return NextResponse.json(
        { error: 'Content is too long for analysis' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildAnalysisPrompt(
      policyType,
      content.trim()
    )

    // 模型参数配置
    const modelParams = {
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      },
      'Qwen/Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['deepseek-ai/DeepSeek-R1-0528-Qwen3-8B']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的摘要
    const summary = response.choices[0]?.message?.content?.trim()

    if (!summary) {
      throw new Error('No summary generated')
    }

    // 清理摘要内容 - 移除所有Markdown格式符号
    const cleanSummary = summary
      .replace(/```markdown\n?/g, '')
      .replace(/```\n?/g, '')
      // 移除Markdown标题符号
      .replace(/^#{1,6}\s+/gm, '')
      // 移除粗体和斜体符号
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      // 移除列表符号，保留内容
      .replace(/^\s*[\*\-\+]\s+/gm, '• ')
      .replace(/^\s*\d+\.\s+/gm, '')
      // 移除代码块符号
      .replace(/`([^`]+)`/g, '$1')
      // 移除链接格式，保留文本
      .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
      // 清理多余空行
      .replace(/\n{3,}/g, '\n\n')
      .trim()

    return NextResponse.json({
      summary: cleanSummary,
      metadata: {
        policyType,
        contentLength: content.length,
        summaryLength: cleanSummary.length,
        model,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Document is too long for analysis' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to analyze policy document' },
      { status: 500 }
    )
  }
}
