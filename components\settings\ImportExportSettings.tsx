'use client'

import React, { useRef } from 'react'
import { Download, Upload, FileText, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useSettingsStore } from '@/store/settings-store'
import { useChatStore } from '@/store/chat-store'

export function ImportExportSettings() {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { exportSettings, importSettings } = useSettingsStore()
  const { exportConversations, importConversations } = useChatStore()

  const handleExportSettings = () => {
    const data = exportSettings()
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `humanwhisper-settings-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportConversations = () => {
    const data = exportConversations()
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `humanwhisper-conversations-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportAll = () => {
    const settings = JSON.parse(exportSettings())
    const conversations = JSON.parse(exportConversations())
    
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings,
      conversations
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `humanwhisper-backup-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleImport = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = e.target?.result as string
        const parsed = JSON.parse(data)
        
        // Try to import settings
        if (parsed.settings || parsed.userSettings) {
          const success = importSettings(JSON.stringify(parsed.settings || parsed))
          if (success) {
            alert('Settings imported successfully!')
          }
        }

        // Try to import conversations
        if (parsed.conversations) {
          const success = importConversations(JSON.stringify(parsed))
          if (success) {
            alert('Conversation history imported successfully!')
          }
        }

        if (!parsed.settings && !parsed.conversations && !parsed.userSettings) {
          alert('Unrecognized file format')
        }
        
      } catch (error) {
        alert('File format error, import failed')
      }
    }
    reader.readAsText(file)
    
    // Clear input value to allow selecting the same file again
    event.target.value = ''
  }

  return (
    <div className="space-y-6">
      {/* Export Data */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Download className="h-5 w-5 text-blue-500" />
            <CardTitle>Export Data</CardTitle>
          </div>
          <CardDescription>
            Backup your settings and conversation history
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3 md:grid-cols-3">
            <Button
              variant="outline"
              onClick={handleExportSettings}
              className="flex items-center space-x-2"
            >
              <FileText className="h-4 w-4" />
              <span>Export Settings</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={handleExportConversations}
              className="flex items-center space-x-2"
            >
              <FileText className="h-4 w-4" />
              <span>Export Conversations</span>
            </Button>
            
            <Button
              onClick={handleExportAll}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export All</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Import Data */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Upload className="h-5 w-5 text-green-500" />
            <CardTitle>Import Data</CardTitle>
          </div>
          <CardDescription>
            Restore your data from backup files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            variant="outline"
            onClick={handleImport}
            className="flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>Select File to Import</span>
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileChange}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* Important Notes */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            <CardTitle>Important Notes</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• Importing data will overwrite current settings and conversation history</p>
            <p>• It's recommended to export current data as backup before importing</p>
            <p>• Only supports JSON format files exported by HumanWhisper</p>
            <p>• Imported data takes effect immediately, please operate with caution</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
