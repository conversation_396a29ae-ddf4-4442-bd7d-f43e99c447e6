### 🧠 **多模态 / 视觉语言模型（VLM）**

| 模型名称                       | 机构               | 参数量 | 上下文 | 主要特点                                                     |
| ------------------------------ | ------------------ | ------ | ------ | ------------------------------------------------------------ |
| **THUDM/GLM-4.1V-9B-Thinking** | 智谱 AI & 清华 KEG | 9B     | 64K    | 引入思维链推理机制，支持4K图像、任意宽高比输入，跨模态推理强，超越Qwen-2.5-VL-72B部分表现 |

---

### 🔢 **通用/推理语言模型（LLM）**

| 模型名称                                    | 机构     | 参数量 | 上下文 | 主要特点                                                     |
| ------------------------------------------- | -------- | ------ | ------ | ------------------------------------------------------------ |
| **deepseek-ai/DeepSeek-R1-0528-Qwen3-8B**   | DeepSeek | 8B     | 128K   | 蒸馏 DeepSeek-R1-0528 思维链，性能等同 Qwen3-235B-thinking，强逻辑和数学能力 |
| **Qwen/Qwen3-8B**                           | 通义千问 | 8.2B   | 128K   | 支持思考/非思考模式切换，兼顾推理和对话，支持100+语言        |
| **THUDM/GLM-Z1-9B-0414**                    | 智谱 AI  | 9B     | 128K   | 小模型但表现强，适合资源受限部署                             |
| **THUDM/GLM-4-9B-0414**                     | 智谱 AI  | 9B     | 32K    | 代码、网页、SVG 生成出色，支持函数调用，轻量部署适用         |
| **deepseek-ai/DeepSeek-R1-Distill-Qwen-7B** | DeepSeek | 7B     | 128K   | 数学与编程任务突出，MATH-500达92.8%，AIME 2024达55.5%        |
| **Qwen/Qwen2.5-7B-Instruct**                | 通义千问 | 7B     | 32K    | 优化结构化输出（如 JSON），多语言覆盖29种语言                |
| **Qwen/Qwen2.5-Coder-7B-Instruct**          | 通义千问 | 7B     | 32K    | 针对编码任务优化，训练量高达5.5万亿 tokens                   |
| **internlm/internlm2_5-7b-chat**            | 书生浦语 | 7B     | 32K    | 开源中英双语模型，专注对话任务                               |
| **Qwen/Qwen2-7B-Instruct**                  | 通义千问 | 7B     | 32K    | 强多语言能力，结构优化，超越 Qwen1.5-7B                      |
| **THUDM/glm-4-9b-chat**                     | 智谱 AI  | 9B     | 128K   | 支持网页浏览、代码执行、Function Call 等复杂功能，26种语言，长文本强大 |

---

### 🔤 **文本嵌入模型（Text Embedding）**

| 模型名称                                 | 机构       | 维度 | 上下文 | 主要特点                                             |
| ---------------------------------------- | ---------- | ---- | ------ | ---------------------------------------------------- |
| **BAAI/bge-m3**                          | 智源研究院 | 1024 | 8K     | 支持密集、多向量、稀疏检索，跨100+语言，长文档表现佳 |
| **netease-youdao/bce-embedding-base_v1** | 网易有道   | 768  | 512    | 优化中英跨语言语义检索，适配教育/医疗/法律领域       |
| **BAAI/bge-large-zh-v1.5**               | 智源研究院 | 1024 | 512    | 优秀中文嵌入性能，C-MTEB多个任务领先                 |
| **BAAI/bge-large-en-v1.5**               | 智源研究院 | 1024 | 512    | 优秀英文嵌入表现，支持多任务处理                     |

---

### 🔍 **重排序模型（Reranker）**

| 模型名称                                | 机构       | 参数量 | 上下文 | 主要特点                                        |
| --------------------------------------- | ---------- | ------ | ------ | ----------------------------------------------- |
| **BAAI/bge-reranker-v2-m3**             | 智源研究院 | 568M   | 8K     | 快速推理，多语言兼容，输出相关性分数            |
| **netease-youdao/bce-reranker-base_v1** | 网易有道   | 279M   | 512    | 中英日韩多语言支持，适用于 RAG 系统中的段落重排 |

---

### 🗣️ **语音模型（Speech）**

| 模型名称                        | 机构        | 类型                  | 语言支持 | 主要特点                                                     |
| ------------------------------- | ----------- | --------------------- | -------- | ------------------------------------------------------------ |
| **FunAudioLLM/SenseVoiceSmall** | FunAudioLLM | ASR + LID + SER + AED | 50+      | 非自回归低延迟模型，中文识别优于 Whisper，10s 音频推理仅需70ms |

---
✅ 模型总数：20 个
📊 分类统计：
多模态/视觉语言模型（VLM）：1 个

通用/推理语言模型（LLM）：10 个

文本嵌入模型（Embedding）：4 个

重排序模型（Reranker）：2 个

语音模型（Speech）：1 个

基于上面的20个模型，请你搜索全网海外的客户群体，不是中国，给我整理一些做工具的思路，以及每个思路可以用哪个模型来实现，注意模型前面的/厂商名称不能省略，是做PC的工具next.js项目，调用的硅基流动提供的免费API，我有31个KEY额度充足。