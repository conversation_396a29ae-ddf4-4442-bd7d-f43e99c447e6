import { PageLayout } from '@/components/layout/PageLayout'
import { AntiClicheLoveLetterClient } from './AntiClicheLoveLetterClient'
import type { Metadata } from 'next'

// Anti-Cliche Love Letter页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Anti-Cliche Love Letter Generator - Unique Confessions | HumanWhisper',
  description: 'Generate personalized love letters that avoid cliches. Create unique confessions with anti-routine punchlines based on shared memories.',
  keywords: [
    'love letter generator',
    'confession letter',
    'romantic writing',
    'personalized love',
    'anti-cliche romance',
    'relationship writing',
    'love confession'
  ],
  openGraph: {
    title: 'AI Anti-Cliche Love Letter Generator - Unique Confessions | HumanWhisper',
    description: 'Generate personalized love letters that avoid cliches. Create unique confessions with anti-routine punchlines based on shared memories.',
    url: '/anti-cliche-love-letter',
    type: 'website',
  },
  twitter: {
    title: 'AI Anti-Cliche Love Letter Generator - Unique Confessions | HumanWhisper',
    description: 'Generate personalized love letters that avoid cliches. Create unique confessions with anti-routine punchlines based on shared memories.',
  },
  alternates: {
    canonical: '/anti-cliche-love-letter',
  },
}

export default function AntiClicheLoveLetterPage() {
  return (
    <PageLayout>
      <AntiClicheLoveLetterClient />
    </PageLayout>
  )
}
