import { PageLayout } from '@/components/layout/PageLayout'
import { MicroGameConceptClient } from './MicroGameConceptClient'
import type { Metadata } from 'next'

// Micro Game Concept页面SEO优化元数据
export const metadata: Metadata = {
  title: 'AI Micro-Game Concept Generator - Game Jam Ideas | HumanWhisper',
  description: 'Generate complete micro-game concepts for Game Jams. Create gameplay mechanics, victory conditions, and DLC hooks in 150 words.',
  keywords: [
    'game jam ideas',
    'micro game concept',
    'game design generator',
    'indie game ideas',
    'game mechanics',
    'game development',
    'concept design'
  ],
  openGraph: {
    title: 'AI Micro-Game Concept Generator - Game Jam Ideas | HumanWhisper',
    description: 'Generate complete micro-game concepts for Game Jams. Create gameplay mechanics, victory conditions, and DLC hooks in 150 words.',
    url: '/micro-game-concept',
    type: 'website',
  },
  twitter: {
    title: 'AI Micro-Game Concept Generator - Game Jam Ideas | HumanWhisper',
    description: 'Generate complete micro-game concepts for Game Jams. Create gameplay mechanics, victory conditions, and DLC hooks in 150 words.',
  },
  alternates: {
    canonical: '/micro-game-concept',
  },
}

export default function MicroGameConceptPage() {
  return (
    <PageLayout>
      <MicroGameConceptClient />
    </PageLayout>
  )
}
