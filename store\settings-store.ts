import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { STORAGE_KEYS, DEFAULT_SETTINGS, THEMES } from '@/lib/constants'
import type { UserSettings, Theme } from '@/types'

// 用户设置状态接口
interface SettingsState {
  // 用户设置
  userSettings: UserSettings
  
  // UI偏好
  theme: Theme
  fontSize: 'small' | 'medium' | 'large'
  sidebarCollapsed: boolean
  showWelcomeMessage: boolean
  
  // 功能开关
  autoSave: boolean
  notifications: boolean
  soundEnabled: boolean
  
  // 隐私设置
  saveConversations: boolean
  shareAnalytics: boolean
  
  // 快捷键设置
  shortcuts: Record<string, string>
  
  // Actions
  updateUserSettings: (settings: Partial<UserSettings>) => void
  setTheme: (theme: Theme) => void
  setFontSize: (size: 'small' | 'medium' | 'large') => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setShowWelcomeMessage: (show: boolean) => void
  toggleAutoSave: () => void
  toggleNotifications: () => void
  toggleSound: () => void
  toggleSaveConversations: () => void
  toggleShareAnalytics: () => void
  updateShortcuts: (shortcuts: Record<string, string>) => void
  resetToDefaults: () => void
  
  // 导入导出
  exportSettings: () => string
  importSettings: (data: string) => boolean
}

// 默认用户设置
const defaultUserSettings: UserSettings = {
  ...DEFAULT_SETTINGS,
  theme: THEMES.SYSTEM,
  fontSize: 'medium',
  autoSave: true,
  notifications: true
}

// 默认快捷键
const defaultShortcuts = {
  'send': 'Enter',
  'newLine': 'Shift+Enter',
  'newChat': 'Ctrl+N',
  'clearChat': 'Ctrl+L',
  'toggleSidebar': 'Ctrl+B',
  'focusInput': 'Ctrl+I',
  'toggleSettings': 'Ctrl+,',
  'exportChat': 'Ctrl+E'
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // 初始状态
      userSettings: defaultUserSettings,
      theme: THEMES.SYSTEM,
      fontSize: 'medium',
      sidebarCollapsed: false,
      showWelcomeMessage: true,
      autoSave: true,
      notifications: true,
      soundEnabled: false,
      saveConversations: true,
      shareAnalytics: false,
      shortcuts: defaultShortcuts,

      // 用户设置更新
      updateUserSettings: (newSettings) => {
        set(state => ({
          userSettings: { ...state.userSettings, ...newSettings }
        }))
      },

      // 主题设置
      setTheme: (theme) => {
        set({ theme })
        // DOM操作由ThemeProvider统一处理，避免冲突
      },

      // 字体大小设置
      setFontSize: (size) => {
        set({ fontSize: size })
        
        // 应用字体大小到DOM
        const root = document.documentElement
        const sizeMap = {
          small: '14px',
          medium: '16px',
          large: '18px'
        }
        root.style.fontSize = sizeMap[size]
      },

      // UI状态切换
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setShowWelcomeMessage: (show) => set({ showWelcomeMessage: show }),

      // 功能开关
      toggleAutoSave: () => set(state => ({ autoSave: !state.autoSave })),
      toggleNotifications: () => set(state => ({ notifications: !state.notifications })),
      toggleSound: () => set(state => ({ soundEnabled: !state.soundEnabled })),
      toggleSaveConversations: () => set(state => ({ saveConversations: !state.saveConversations })),
      toggleShareAnalytics: () => set(state => ({ shareAnalytics: !state.shareAnalytics })),

      // 快捷键设置
      updateShortcuts: (shortcuts) => {
        set(state => ({
          shortcuts: { ...state.shortcuts, ...shortcuts }
        }))
      },

      // 重置设置
      resetToDefaults: () => {
        set({
          userSettings: defaultUserSettings,
          theme: THEMES.SYSTEM,
          fontSize: 'medium',
          sidebarCollapsed: false,
          showWelcomeMessage: true,
          autoSave: true,
          notifications: true,
          soundEnabled: false,
          saveConversations: true,
          shareAnalytics: false,
          shortcuts: defaultShortcuts
        })
      },

      // 导入导出
      exportSettings: () => {
        const state = get()
        const exportData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          userSettings: state.userSettings,
          theme: state.theme,
          fontSize: state.fontSize,
          sidebarCollapsed: state.sidebarCollapsed,
          showWelcomeMessage: state.showWelcomeMessage,
          autoSave: state.autoSave,
          notifications: state.notifications,
          soundEnabled: state.soundEnabled,
          saveConversations: state.saveConversations,
          shareAnalytics: state.shareAnalytics,
          shortcuts: state.shortcuts
        }
        return JSON.stringify(exportData, null, 2)
      },

      importSettings: (data) => {
        try {
          const parsed = JSON.parse(data)
          
          // 验证数据结构
          if (!parsed.userSettings) {
            return false
          }
          
          set({
            userSettings: { ...defaultUserSettings, ...parsed.userSettings },
            theme: parsed.theme || THEMES.SYSTEM,
            fontSize: parsed.fontSize || 'medium',
            sidebarCollapsed: parsed.sidebarCollapsed ?? false,
            showWelcomeMessage: parsed.showWelcomeMessage ?? true,
            autoSave: parsed.autoSave ?? true,
            notifications: parsed.notifications ?? true,
            soundEnabled: parsed.soundEnabled ?? false,
            saveConversations: parsed.saveConversations ?? true,
            shareAnalytics: parsed.shareAnalytics ?? false,
            shortcuts: { ...defaultShortcuts, ...parsed.shortcuts }
          })
          
          return true
        } catch {
          return false
        }
      }
    }),
    {
      name: STORAGE_KEYS.userSettings,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userSettings: state.userSettings,
        theme: state.theme,
        fontSize: state.fontSize,
        sidebarCollapsed: state.sidebarCollapsed,
        showWelcomeMessage: state.showWelcomeMessage,
        autoSave: state.autoSave,
        notifications: state.notifications,
        soundEnabled: state.soundEnabled,
        saveConversations: state.saveConversations,
        shareAnalytics: state.shareAnalytics,
        shortcuts: state.shortcuts
      })
    }
  )
)

// 主题处理由ThemeProvider统一管理，避免冲突
