import { AI_MODELS, SUPPORTED_MODELS } from './constants'
import type { AIModel, ModelInfo, ChatSettings } from '@/types'

/**
 * 获取所有支持的模型信息
 * @returns ModelInfo[]
 */
export function getAllModels(): ModelInfo[] {
  return [...SUPPORTED_MODELS]
}

/**
 * 根据ID获取模型信息
 * @param modelId - 模型ID
 * @returns ModelInfo | undefined
 */
export function getModelById(modelId: AIModel): ModelInfo | undefined {
  return SUPPORTED_MODELS.find(model => model.id === modelId)
}

/**
 * 获取默认模型
 * @returns ModelInfo
 */
export function getDefaultModel(): ModelInfo {
  return getModelById(AI_MODELS.GPT_4_1_NANO) || SUPPORTED_MODELS[0]
}

/**
 * 根据提供商筛选模型
 * @param provider - 提供商名称
 * @returns ModelInfo[]
 */
export function getModelsByProvider(provider: string): ModelInfo[] {
  return SUPPORTED_MODELS.filter(model => 
    model.provider.toLowerCase() === provider.toLowerCase()
  )
}

/**
 * 获取模型的推荐使用场景
 * @param modelId - 模型ID
 * @returns string[]
 */
export function getModelUseCases(modelId: AIModel): string[] {
  const useCases: Record<AIModel, string[]> = {
    [AI_MODELS.GPT_4_1_NANO]: [
      '日常对话和问答',
      '简单的文本生成',
      '快速响应场景',
      '教育解释',
    ],
    [AI_MODELS.CLAUDE_3_5_HAIKU]: [
      '深度分析和推理',
      '复杂问题解答',
      '创意写作',
      '代码解释',
    ],
    [AI_MODELS.GEMINI_2_0_FLASH]: [
      '多模态理解',
      '技术文档解释',
      '科学概念说明',
      '实时对话',
    ],
  }
  
  return useCases[modelId] || []
}

/**
 * 获取模型的特点描述
 * @param modelId - 模型ID
 * @returns string[]
 */
export function getModelFeatures(modelId: AIModel): string[] {
  const features: Record<AIModel, string[]> = {
    [AI_MODELS.GPT_4_1_NANO]: [
      '响应速度快',
      '成本效益高',
      '适合高频使用',
      '稳定可靠',
    ],
    [AI_MODELS.CLAUDE_3_5_HAIKU]: [
      '推理能力强',
      '上下文理解深入',
      '安全性高',
      '适合复杂任务',
    ],
    [AI_MODELS.GEMINI_2_0_FLASH]: [
      '多模态支持',
      '创新能力强',
      '技术前沿',
      '响应迅速',
    ],
  }
  
  return features[modelId] || []
}

/**
 * 根据设置推荐最佳模型
 * @param settings - 聊天设置
 * @returns AIModel
 */
export function recommendModel(settings: ChatSettings): AIModel {
  const { responseLength, targetAge, includeExamples, useAnalogies } = settings

  // 如果需要详细回答和复杂推理，推荐Claude
  if (responseLength === 'THOROUGH' || (includeExamples && useAnalogies)) {
    return AI_MODELS.CLAUDE_3_5_HAIKU
  }

  // 如果是简短回答或年龄较小，推荐GPT
  if (responseLength === 'BRIEF' || targetAge <= 12) {
    return AI_MODELS.GPT_4_1_NANO
  }

  // 其他情况推荐Gemini
  return AI_MODELS.GEMINI_2_0_FLASH
}

/**
 * 检查模型是否支持流式响应
 * @param modelId - 模型ID
 * @returns boolean
 */
export function supportsStreaming(modelId: AIModel): boolean {
  // 所有支持的模型都支持流式响应
  return Object.values(AI_MODELS).includes(modelId)
}

/**
 * 获取模型的性能评级
 * @param modelId - 模型ID
 * @returns object
 */
export function getModelPerformance(modelId: AIModel) {
  const performance: Record<AIModel, {
    speed: number      // 1-5, 5最快
    quality: number    // 1-5, 5最好
    cost: number       // 1-5, 1最便宜
    reasoning: number  // 1-5, 5最强
  }> = {
    [AI_MODELS.GPT_4_1_NANO]: {
      speed: 5,
      quality: 4,
      cost: 1,
      reasoning: 3,
    },
    [AI_MODELS.CLAUDE_3_5_HAIKU]: {
      speed: 4,
      quality: 5,
      cost: 3,
      reasoning: 5,
    },
    [AI_MODELS.GEMINI_2_0_FLASH]: {
      speed: 5,
      quality: 4,
      cost: 2,
      reasoning: 4,
    },
  }
  
  return performance[modelId] || {
    speed: 3,
    quality: 3,
    cost: 3,
    reasoning: 3,
  }
}

/**
 * 获取模型的限制信息
 * @param modelId - 模型ID
 * @returns object
 */
export function getModelLimitations(modelId: AIModel) {
  const limitations: Record<AIModel, {
    maxTokens: number
    contextWindow: number
    rateLimit: number  // 每分钟请求数
    restrictions: string[]
  }> = {
    [AI_MODELS.GPT_4_1_NANO]: {
      maxTokens: 4096,
      contextWindow: 8192,
      rateLimit: 60,
      restrictions: [
        '不支持图片输入',
        '上下文窗口相对较小',
      ],
    },
    [AI_MODELS.CLAUDE_3_5_HAIKU]: {
      maxTokens: 8192,
      contextWindow: 200000,
      rateLimit: 30,
      restrictions: [
        '响应速度相对较慢',
        '成本较高',
      ],
    },
    [AI_MODELS.GEMINI_2_0_FLASH]: {
      maxTokens: 8192,
      contextWindow: 1000000,
      rateLimit: 45,
      restrictions: [
        '新模型，稳定性待验证',
        '某些功能可能不完善',
      ],
    },
  }
  
  return limitations[modelId] || {
    maxTokens: 4096,
    contextWindow: 8192,
    rateLimit: 30,
    restrictions: ['未知限制'],
  }
}

/**
 * 比较两个模型
 * @param modelA - 模型A
 * @param modelB - 模型B
 * @returns object
 */
export function compareModels(modelA: AIModel, modelB: AIModel) {
  const perfA = getModelPerformance(modelA)
  const perfB = getModelPerformance(modelB)
  const infoA = getModelById(modelA)
  const infoB = getModelById(modelB)
  
  return {
    speed: perfA.speed - perfB.speed,
    quality: perfA.quality - perfB.quality,
    cost: perfB.cost - perfA.cost, // 成本越低越好，所以反向比较
    reasoning: perfA.reasoning - perfB.reasoning,
    maxTokens: (infoA?.maxTokens || 0) - (infoB?.maxTokens || 0),
    costPer1k: (infoB?.costPer1kTokens || 0) - (infoA?.costPer1kTokens || 0),
  }
}

/**
 * 获取模型切换建议
 * @param currentModel - 当前模型
 * @param settings - 当前设置
 * @returns object
 */
export function getModelSwitchSuggestion(
  currentModel: AIModel, 
  settings: ChatSettings
) {
  const recommended = recommendModel(settings)
  
  if (recommended === currentModel) {
    return {
      shouldSwitch: false,
      reason: '当前模型已是最佳选择',
      suggestion: null,
    }
  }
  
  const comparison = compareModels(recommended, currentModel)
  const reasons: string[] = []
  
  if (comparison.speed > 0) reasons.push('响应更快')
  if (comparison.quality > 0) reasons.push('质量更好')
  if (comparison.cost > 0) reasons.push('成本更低')
  if (comparison.reasoning > 0) reasons.push('推理能力更强')
  
  return {
    shouldSwitch: true,
    reason: `建议切换到 ${getModelById(recommended)?.name}`,
    suggestion: recommended,
    benefits: reasons,
  }
}
