import { create<PERSON>lerk<PERSON>lient } from '@clerk/nextjs/server'
import { getImageCredits } from '@/lib/credits-utils'

const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY!
})
import { UserMetadata, SUBSCRIPTION_PLANS, PlanLimits, UserUsage, MODEL_PRICING, REGISTRATION_REWARD } from '@/types/user'

/**
 * 获取用户元数据
 */
export async function getUserMetadata(userId: string): Promise<UserMetadata> {
  try {
    const user = await clerkClient.users.getUser(userId)
    const metadata = user.privateMetadata as unknown as UserMetadata
    
    // 如果是新用户，初始化默认数据
    if (!metadata.plan) {
      const defaultMetadata: UserMetadata = {
        plan: 'free',
        credits: REGISTRATION_REWARD,
        usedCredits: 0,
        totalCreditsEarned: REGISTRATION_REWARD,
        createdAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString()
      }

      await updateUserMetadata(userId, defaultMetadata)
      return defaultMetadata
    }
    
    return metadata
  } catch (error) {
    throw new Error('Failed to get user metadata')
  }
}

/**
 * 更新用户元数据
 */
export async function updateUserMetadata(userId: string, metadata: Partial<UserMetadata>): Promise<void> {
  try {
    const currentUser = await clerkClient.users.getUser(userId)
    const currentMetadata = currentUser.privateMetadata as unknown as UserMetadata
    
    const updatedMetadata = {
      ...currentMetadata,
      ...metadata,
      lastActiveAt: new Date().toISOString()
    }
    
    await clerkClient.users.updateUserMetadata(userId, {
      privateMetadata: updatedMetadata
    })
  } catch (error) {
    throw new Error('Failed to update user metadata')
  }
}

/**
 * 扣除用户积分
 */
export async function deductUserCredits(userId: string, creditsToDeduct: number): Promise<boolean> {
  try {
    // 先检查并降级过期用户，然后获取最新数据
    const metadata = await checkAndDowngradeExpiredUser(userId)

    // 检查订阅是否有效，如果无效则使用免费计划限制
    const subscriptionActive = isSubscriptionActive(metadata)
    const effectivePlan = (metadata.plan === 'premium' && subscriptionActive) ? 'premium' : 'free'
    const planLimits = SUBSCRIPTION_PLANS[effectivePlan]

    // 检查是否有足够的积分
    if (planLimits && planLimits.maxCredits !== -1) { // 不是无限制计划
      const remainingCredits = metadata.credits - metadata.usedCredits
      if (remainingCredits < creditsToDeduct) {
        return false // 积分不足
      }
    }

    // 扣除积分
    await updateUserMetadata(userId, {
      usedCredits: metadata.usedCredits + creditsToDeduct
    })

    return true
  } catch (error) {
    return false
  }
}

/**
 * 获取用户计划限制
 */
export function getUserPlanLimits(plan: string): PlanLimits {
  const planLimits = SUBSCRIPTION_PLANS[plan as keyof typeof SUBSCRIPTION_PLANS]
  return (planLimits || SUBSCRIPTION_PLANS.free) as PlanLimits
}

/**
 * 检查订阅是否有效（未过期且状态为active）
 */
export function isSubscriptionActive(metadata: UserMetadata): boolean {
  // 免费用户不需要检查订阅状态
  if (metadata.plan === 'free') {
    return true
  }

  // Premium用户需要检查订阅状态和到期时间
  if (metadata.plan === 'premium') {
    // 检查订阅状态
    if (metadata.subscriptionStatus !== 'active') {
      return false
    }

    // 检查到期时间
    if (metadata.subscriptionPeriodEnd) {
      const expiryDate = new Date(metadata.subscriptionPeriodEnd)
      const now = new Date()
      return expiryDate > now
    }

    // 如果没有设置到期时间，认为订阅无效
    return false
  }

  return false
}

/**
 * 检查并自动降级过期用户（在权限检查时调用）
 */
export async function checkAndDowngradeExpiredUser(userId: string): Promise<UserMetadata> {
  try {
    const metadata = await getUserMetadata(userId)

    // 如果是Premium用户但订阅无效，自动降级
    if (metadata.plan === 'premium' && !isSubscriptionActive(metadata)) {
      await downgradeExpiredUser(userId)
      // 返回更新后的用户数据
      return await getUserMetadata(userId)
    }

    return metadata
  } catch (error) {
    // 如果出错，返回原始数据
    return await getUserMetadata(userId)
  }
}

/**
 * 检查用户是否可以使用某个功能
 */
export async function checkUserFeatureAccess(userId: string, feature: string): Promise<boolean> {
  try {
    const metadata = await getUserMetadata(userId)

    // 首先检查订阅是否有效
    if (!isSubscriptionActive(metadata)) {
      // 如果订阅无效，降级为免费用户权限
      const freePlanLimits = getUserPlanLimits('free')
      return freePlanLimits.features.includes(feature)
    }

    const planLimits = getUserPlanLimits(metadata.plan)
    return planLimits.features.includes(feature)
  } catch (error) {
    return false
  }
}

/**
 * 获取用户使用统计
 */
export async function getUserUsage(userId: string): Promise<UserUsage> {
  try {
    const metadata = await getUserMetadata(userId)

    // 检查订阅是否有效，如果无效则使用免费计划限制
    const subscriptionActive = isSubscriptionActive(metadata)
    const effectivePlan = (metadata.plan === 'premium' && subscriptionActive) ? 'premium' : 'free'
    const planLimits = getUserPlanLimits(effectivePlan)

    const creditsRemaining = planLimits.maxCredits === -1
      ? -1 // 无限制
      : Math.max(0, metadata.credits - metadata.usedCredits)

    return {
      totalMessages: metadata.usedCredits, // 积分消耗基于模型定价：GPT(1+1图片)、Claude(4+2图片)、Gemini(2+1图片)
      messagesThisMonth: metadata.usedCredits,
      creditsUsed: metadata.usedCredits,
      creditsRemaining,
      lastResetDate: metadata.createdAt || new Date().toISOString(),
      effectivePlan, // 添加有效计划信息
      subscriptionActive // 添加订阅状态信息
    }
  } catch (error) {
    throw new Error('Failed to get user usage')
  }
}

/**
 * 重置用户月度使用量（已废弃 - 安全风险）
 * @deprecated 此函数已完全废弃，存在安全风险。积分重置只能通过订阅续费webhook处理
 * @throws {Error} 始终抛出错误，防止被滥用
 */
export async function resetMonthlyUsage(_userId: string): Promise<void> {
  // 安全措施：此函数已完全废弃，防止积分重置被滥用
  throw new Error('SECURITY: resetMonthlyUsage is deprecated and disabled. Credits can only be reset through subscription renewal webhooks.')
}

/**
 * 升级用户计划
 */
export async function upgradeUserPlan(
  userId: string,
  newPlan: 'free' | 'premium',
  subscriptionId?: string
): Promise<void> {
  try {
    // 获取当前用户数据
    const currentMetadata = await getUserMetadata(userId)
    const planLimits = getUserPlanLimits(newPlan)

    // 计算新的积分：保留剩余积分 + 新计划积分（订阅用户福利）
    const remainingCredits = Math.max(0, currentMetadata.credits - currentMetadata.usedCredits)
    const newPlanCredits = planLimits.maxCredits // 使用配置的积分数（测试环境5积分，生产环境3600积分）
    const totalCredits = remainingCredits + newPlanCredits

    // 更新总获得积分
    const totalCreditsEarned = (currentMetadata.totalCreditsEarned || 0) + newPlanCredits

    const updateData: Partial<UserMetadata> = {
      plan: newPlan,
      credits: totalCredits,
      usedCredits: currentMetadata.usedCredits, // 保持已使用积分不变
      totalCreditsEarned: totalCreditsEarned,
      subscriptionStatus: 'active'
    }

    if (subscriptionId) {
      updateData.subscriptionId = subscriptionId
    }

    // 如果是升级到付费计划，设置30天订阅期限
    if (newPlan === 'premium') {
      const now = new Date()
      const subscriptionEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后

      updateData.subscriptionPeriodStart = now.toISOString()
      updateData.subscriptionPeriodEnd = subscriptionEnd.toISOString()
    }

    await updateUserMetadata(userId, updateData)
  } catch (error) {
    throw new Error('Failed to upgrade user plan')
  }
}

/**
 * 降级过期的Premium用户到免费计划
 */
export async function downgradeExpiredUser(userId: string): Promise<void> {
  try {
    const metadata = await getUserMetadata(userId)

    // 只处理Premium用户
    if (metadata.plan !== 'premium') {
      return
    }

    // 检查是否真的过期
    if (isSubscriptionActive(metadata)) {
      return
    }

    // 降级到免费计划，清空所有积分
    await updateUserMetadata(userId, {
      plan: 'free',
      credits: 0,
      usedCredits: 0,
      subscriptionStatus: 'canceled'
    })
  } catch (error) {
    throw new Error('Failed to downgrade expired user')
  }
}

/**
 * 根据模型名称和图像数量扣除积分
 */
export async function deductCreditsForModel(userId: string, modelName: string, imageCount: number = 0): Promise<boolean> {
  const baseCredits = MODEL_PRICING[modelName] || 1
  const imageCredits = getImageCredits(modelName, imageCount)
  const totalCredits = baseCredits + imageCredits
  return await deductUserCredits(userId, totalCredits)
}



/**
 * 检查用户是否可以使用指定模型（包含图像）
 */
export async function canUseModel(userId: string, modelName: string, imageCount: number = 0): Promise<boolean> {
  try {
    const metadata = await getUserMetadata(userId)
    const baseCredits = MODEL_PRICING[modelName] || 1
    const imageCredits = getImageCredits(modelName, imageCount)
    const totalCreditsRequired = baseCredits + imageCredits
    const remainingCredits = metadata.credits - metadata.usedCredits

    // 检查订阅是否有效
    const subscriptionActive = isSubscriptionActive(metadata)

    // 如果是Premium用户且订阅有效，可以使用所有模型
    if (metadata.plan === 'premium' && subscriptionActive) {
      return remainingCredits >= totalCreditsRequired
    }

    // 如果是Premium用户但订阅无效，或者是免费用户，只能使用基础模型
    const freeModels = ['gpt-4.1-nano', 'claude-3-5-haiku-latest', 'gemini-2.0-flash']
    if (!freeModels.includes(modelName)) {
      return false
    }

    return remainingCredits >= totalCreditsRequired
  } catch (error) {
    return false
  }
}


