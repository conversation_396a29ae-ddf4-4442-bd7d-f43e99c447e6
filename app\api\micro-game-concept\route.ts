import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { getRandomSiliconFlowApiKey, getSiliconFlowBaseUrl } from '@/lib/siliconflow-utils'

// 创建AI客户端的函数（每次请求时动态创建以使用随机 API key）
function createSiliconFlowClient() {
  return new OpenAI({
    apiKey: getRandomSiliconFlowApiKey(),
    baseURL: getSiliconFlowBaseUrl(),
  })
}

// 游戏主题映射
const GAME_THEME_MAP: Record<string, string> = {
  'environmental': 'environmental themes like climate change, pollution, conservation, and sustainability',
  'time_loop': 'time loop mechanics with repeating cycles, temporal puzzles, and cause-effect relationships',
  'social_anxiety': 'social anxiety themes including introversion, communication challenges, and social situations',
  'minimalism': 'minimalist design with simple mechanics, clean aesthetics, and essential elements only',
  'cooperation': 'cooperation and teamwork mechanics requiring collaboration and mutual assistance',
  'memory': 'memory-based gameplay involving remembering, forgetting, nostalgia, and cognitive challenges',
  'growth': 'growth and evolution themes with progression, learning, and development mechanics',
  'isolation': 'isolation themes exploring loneliness, solitude, connection, and emotional distance',
  'creativity': 'creativity and artistic expression through art, imagination, and innovative mechanics',
  'survival': 'survival mechanics with resource management, adaptation, and perseverance challenges',
  'mystery': 'mystery and investigation gameplay with secrets, discovery, and puzzle-solving'
}

// 构建游戏概念生成prompt
function buildGameConceptPrompt(
  gameTheme: string,
  coreVerbs: string,
  artKeyword: string
): string {
  const themeDesc = GAME_THEME_MAP[gameTheme] || gameTheme

  const prompt = `You are a game design expert specializing in creating innovative micro-game concepts for Game Jams. Your task is to design a complete, playable game concept that can be developed within a Game Jam timeframe.

**Design Parameters:**
- Theme: ${themeDesc}
- Core Actions: ${coreVerbs}
- Art Style: ${artKeyword}

**Instructions:**
Create a comprehensive micro-game concept that includes:

1. **Gameplay Overview** (80-90 words): Core mechanics, player actions, and how the game works
2. **Victory Conditions** (30-40 words): Clear win/lose states and progression goals
3. **DLC Expansion Hooks** (3 ideas, 10-15 words each): Future content possibilities that extend the core concept

**Design Requirements:**
- **Scope**: Achievable in 48-72 hours for a small team
- **Innovation**: Unique twist on familiar mechanics
- **Theme Integration**: Meaningful connection to the chosen theme
- **Accessibility**: Easy to learn, engaging to master
- **Technical Feasibility**: Realistic for indie developers
- **Replayability**: Mechanics that encourage multiple playthroughs

**Core Verbs Integration:**
The two core verbs (${coreVerbs}) should be the primary player actions that drive the entire gameplay loop. Every mechanic should relate back to these fundamental actions.

**Art Style Consideration:**
The ${artKeyword} art style should influence not just visuals but also gameplay feel, UI design, and player experience. Consider how this aesthetic choice affects game mechanics.

**Output Format:**
Return a valid JSON object:

{
  "gameplayOverview": "Detailed description of core mechanics and player experience",
  "victoryConditions": "Clear explanation of how players win and progress",
  "dlcHooks": ["Expansion idea 1", "Expansion idea 2", "Expansion idea 3"],
  "wordCount": 150,
  "coreLoop": "Brief description of the main gameplay loop",
  "targetAudience": "Primary demographic and player type"
}

**Game Design Principles:**
- Start with the core loop and build outward
- Ensure every element serves the theme
- Balance simplicity with depth
- Consider both single-player and multiplayer potential
- Think about monetization and post-launch content
- Design for emotional engagement, not just mechanics

**Innovation Guidelines:**
- Avoid direct copies of existing games
- Combine familiar elements in unexpected ways
- Use constraints as creative opportunities
- Consider unconventional input methods or perspectives
- Think about social and cultural relevance

Generate the complete micro-game concept now:`

  return prompt
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const {
      gameTheme,
      coreVerbs,
      artKeyword,
      model = 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B'
    } = body

    // 输入验证
    if (!gameTheme || !coreVerbs?.trim() || !artKeyword?.trim()) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // 构建prompt
    const prompt = buildGameConceptPrompt(
      gameTheme,
      coreVerbs.trim(),
      artKeyword.trim()
    )

    // 模型参数配置
    const modelParams = {
      'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B': {
        temperature: 0.6,
        max_tokens: 8192,
        top_p: 0.95,
        frequency_penalty: 0.0
      },
      'THUDM/glm-4-9b-chat': {
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.7,
        frequency_penalty: 0.0
      }
    }

    const params = modelParams[model as keyof typeof modelParams] || modelParams['deepseek-ai/DeepSeek-R1-0528-Qwen3-8B']

    // 构建消息
    const messages = [
      {
        role: 'user' as const,
        content: prompt
      }
    ]

    // 创建AI客户端
    const aiClient = createSiliconFlowClient()

    // 调用AI API
    const response = await aiClient.chat.completions.create({
      model: model,
      messages: messages,
      ...params
    })

    // 提取生成的内容
    const generatedContent = response.choices[0]?.message?.content?.trim()

    if (!generatedContent) {
      throw new Error('No content generated')
    }

    // 尝试解析JSON
    let gameConcept: any
    try {
      // 清理可能的markdown代码块标记
      const cleanContent = generatedContent
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim()
      
      gameConcept = JSON.parse(cleanContent)
      
      // 验证数据结构
      if (!gameConcept.gameplayOverview || !gameConcept.victoryConditions || !Array.isArray(gameConcept.dlcHooks)) {
        throw new Error('Invalid response structure')
      }
      
      // 确保DLC hooks是数组且有3个元素
      if (gameConcept.dlcHooks.length < 3) {
        gameConcept.dlcHooks = [
          ...gameConcept.dlcHooks,
          ...Array(3 - gameConcept.dlcHooks.length).fill('Additional content expansion')
        ]
      }
      
      // 计算实际字数
      const totalWords = (
        gameConcept.gameplayOverview + ' ' + 
        gameConcept.victoryConditions + ' ' + 
        gameConcept.dlcHooks.join(' ')
      ).split(/\s+/).length
      
      gameConcept.wordCount = totalWords
      
    } catch (parseError) {
      // 如果JSON解析失败，创建基本结构
      gameConcept = {
        gameplayOverview: `A ${gameTheme} themed game where players ${coreVerbs.split(',')[0]?.trim() || 'interact'} and ${coreVerbs.split(',')[1]?.trim() || 'explore'} in a ${artKeyword} visual style. The core mechanics revolve around strategic decision-making and resource management, creating an engaging experience that challenges players to think creatively while staying true to the central theme.`,
        victoryConditions: `Players win by successfully completing the main objective while managing limited resources. Progress is measured through achievement milestones and skill mastery.`,
        dlcHooks: [
          'New levels with increased difficulty',
          'Additional character abilities and customization',
          'Multiplayer cooperative mode expansion'
        ],
        wordCount: 150,
        coreLoop: `${coreVerbs.split(',')[0]?.trim() || 'Action'} → Decision → ${coreVerbs.split(',')[1]?.trim() || 'Reaction'} → Progress`,
        targetAudience: 'Indie game enthusiasts and puzzle game fans'
      }
    }

    return NextResponse.json({
      gameConcept,
      metadata: {
        gameTheme,
        coreVerbs,
        artKeyword,
        wordCount: gameConcept.wordCount,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error: any) {
    // 处理不同类型的错误
    if (error?.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: 'Service temporarily unavailable' },
        { status: 429 }
      )
    }

    if (error?.code === 'model_not_found') {
      return NextResponse.json(
        { error: 'Selected model is not available' },
        { status: 400 }
      )
    }

    if (error?.code === 'context_length_exceeded') {
      return NextResponse.json(
        { error: 'Input content is too long' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate game concept' },
      { status: 500 }
    )
  }
}
