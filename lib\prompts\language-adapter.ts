import type { Language } from '@/types'

/**
 * 多语言文化适配系统
 * 为不同语言和文化背景提供定制化的提示词增强
 */

// 语言特定的文化适配指令
export const LANGUAGE_ADAPTATIONS: Record<Language, {
  name: string
  nativeName: string
  culturalInstructions: string
  exampleTypes: string[]
  communicationStyle: string
  commonAnalogies: string[]
}> = {
  'zh': {
    name: 'Chinese (Simplified)',
    nativeName: '中文',
    culturalInstructions: `
### 中文用户特殊指令:
- 使用中国文化中熟悉的比喻和典故（如"井底之蛙"、"盲人摸象"等）
- 引用日常生活场景（买菜、坐地铁、网购、移动支付等）
- 避免过于西化的表达方式和例子
- 使用"咱们"、"您"等亲近但礼貌的称呼
- 适当使用成语和俗语来增强理解
- 考虑中国的教育体系和社会背景`,
    exampleTypes: [
      '中国传统文化元素',
      '现代中国生活场景',
      '中式思维方式',
      '熟悉的历史典故'
    ],
    communicationStyle: '温和、耐心、循循善诱，体现中国文化中的师者风范',
    commonAnalogies: [
      '做菜调味 - 解释平衡和比例',
      '种花养草 - 解释成长和培养',
      '修房子 - 解释构建和基础',
      '开车导航 - 解释路径和选择'
    ]
  },
  
  'en': {
    name: 'English',
    nativeName: 'English',
    culturalInstructions: `
### English Language Instructions:
- Use familiar Western cultural references and idioms
- Include examples from daily American/British life
- Reference common experiences like driving, shopping, sports
- Use conversational American English tone
- Include pop culture references when appropriate
- Consider diverse backgrounds within English-speaking cultures`,
    exampleTypes: [
      'Sports analogies',
      'Cooking and recipes',
      'Technology and gadgets',
      'Movies and entertainment'
    ],
    communicationStyle: 'Friendly, casual, encouraging - like talking to a good friend',
    commonAnalogies: [
      'Building blocks - for foundational concepts',
      'Recipe ingredients - for components and mixing',
      'Team sports - for collaboration and roles',
      'Road trips - for journeys and processes'
    ]
  },
  
  'ja': {
    name: 'Japanese',
    nativeName: '日本語',
    culturalInstructions: `
### 日本語ユーザー向け特別指示:
- 適切な敬語表現を使用し、礼儀正しく謙遜な態度を示す
- 日本文化に根ざした概念や例を引用する
- 過度に直接的な表現を避け、婉曲的な表現を心がける
- "〜ですね"、"〜でしょうか"などの温和な語気を使用
- 季節感や自然への言及を適切に含める
- 集団主義的な価値観を考慮した説明を行う`,
    exampleTypes: [
      '四季の変化',
      '日本の伝統文化',
      '現代日本の生活',
      '和の心や美意識'
    ],
    communicationStyle: '丁寧で謙虚、相手を立てる日本的なコミュニケーション',
    commonAnalogies: [
      '庭園の手入れ - 継続的な改善',
      '茶道の作法 - 手順と精神性',
      '桜の開花 - タイミングと美しさ',
      '職人の技 - 熟練と献身'
    ]
  },
  
  'ko': {
    name: 'Korean',
    nativeName: '한국어',
    culturalInstructions: `
### 한국어 사용자를 위한 특별 지침:
- 적절한 존댓말을 사용하여 예의를 지킨다
- 한국 문화와 사회에 친숙한 예시를 활용한다
- 교육열과 성취 지향적 문화를 고려한다
- "~습니다", "~지요" 등의 정중한 어미를 사용한다
- 집단주의적 가치관과 관계 중심 사고를 반영한다
- 한국의 현대적 기술 문화를 적절히 언급한다`,
    exampleTypes: [
      '한국의 전통 문화',
      'K-pop과 한류',
      '한국의 IT 문화',
      '교육과 학습'
    ],
    communicationStyle: '정중하고 친근하며, 상대방을 배려하는 한국적 소통 방식',
    commonAnalogies: [
      '김치 담그기 - 시간과 정성의 과정',
      '태권도 수련 - 단계적 발전',
      '한옥 건축 - 조화와 균형',
      '등산 - 목표 달성의 여정'
    ]
  },
  
  'es': {
    name: 'Spanish',
    nativeName: 'Español',
    culturalInstructions: `
### Instrucciones especiales para usuarios de español:
- Usar referencias culturales familiares de países hispanohablantes
- Incluir ejemplos de la vida cotidiana latina
- Mantener un tono cálido y expresivo característico de la cultura hispana
- Usar expresiones coloquiales apropiadas
- Considerar la diversidad cultural dentro del mundo hispanohablante
- Incluir referencias a tradiciones y valores familiares`,
    exampleTypes: [
      'Tradiciones familiares',
      'Comida y cocina latina',
      'Fútbol y deportes',
      'Música y celebraciones'
    ],
    communicationStyle: 'Cálido, expresivo y familiar, con el carisma latino',
    commonAnalogies: [
      'Cocinar paella - ingredientes y timing',
      'Bailar salsa - ritmo y coordinación',
      'Familia extendida - conexiones y roles',
      'Fiesta de pueblo - comunidad y celebración'
    ]
  },
  
  'fr': {
    name: 'French',
    nativeName: 'Français',
    culturalInstructions: `
### Instructions spéciales pour les utilisateurs français:
- Utiliser des références culturelles françaises familières
- Inclure des exemples de la vie quotidienne française
- Maintenir un ton élégant mais accessible
- Utiliser des expressions idiomatiques appropriées
- Considérer l'appréciation française pour la logique et la clarté
- Inclure des références à l'art, la cuisine et la culture`,
    exampleTypes: [
      'Cuisine et gastronomie',
      'Art et littérature',
      'Vie quotidienne française',
      'Histoire et patrimoine'
    ],
    communicationStyle: 'Élégant, logique et raffiné, avec la clarté française',
    commonAnalogies: [
      'Préparer un bon vin - patience et savoir-faire',
      'Architecture gothique - structure et beauté',
      'Jardin à la française - ordre et harmonie',
      'Débat philosophique - logique et argumentation'
    ]
  },
  
  'de': {
    name: 'German',
    nativeName: 'Deutsch',
    culturalInstructions: `
### Besondere Anweisungen für deutsche Nutzer:
- Vertraute deutsche Kulturverweise und Redewendungen verwenden
- Beispiele aus dem deutschen Alltag einbeziehen
- Einen gründlichen und systematischen Ansatz beibehalten
- Deutsche Werte wie Präzision und Effizienz berücksichtigen
- Angemessene Höflichkeitsformen verwenden
- Technische Kompetenz und Ingenieursdenken würdigen`,
    exampleTypes: [
      'Deutsche Ingenieurskunst',
      'Ordnung und System',
      'Handwerk und Qualität',
      'Natur und Umwelt'
    ],
    communicationStyle: 'Gründlich, präzise und respektvoll, mit deutscher Direktheit',
    commonAnalogies: [
      'Uhrwerk - Präzision und Mechanismus',
      'Handwerkskunst - Qualität und Tradition',
      'Autobahn - Effizienz und Regeln',
      'Schwarzwald - Natur und Beständigkeit'
    ]
  },
  
  'it': {
    name: 'Italian',
    nativeName: 'Italiano',
    culturalInstructions: `
### Istruzioni speciali per utenti italiani:
- Utilizzare riferimenti culturali italiani familiari
- Includere esempi dalla vita quotidiana italiana
- Mantenere un tono caloroso ed espressivo
- Usare espressioni idiomatiche appropriate
- Considerare l'apprezzamento italiano per l'arte e la bellezza
- Includere riferimenti alla famiglia e alle tradizioni`,
    exampleTypes: [
      'Cucina e tradizioni culinarie',
      'Arte e architettura',
      'Famiglia e comunità',
      'Storia e patrimonio'
    ],
    communicationStyle: 'Caloroso, espressivo e appassionato, con lo spirito italiano',
    commonAnalogies: [
      'Preparare pasta - timing e tradizione',
      'Opera lirica - emozione e tecnica',
      'Piazza del paese - comunità e incontro',
      'Restauro artistico - pazienza e maestria'
    ]
  },
  
  'pt': {
    name: 'Portuguese',
    nativeName: 'Português',
    culturalInstructions: `
### Instruções especiais para usuários de português:
- Usar referências culturais familiares do mundo lusófono
- Incluir exemplos da vida cotidiana brasileira/portuguesa
- Manter um tom caloroso e acolhedor
- Usar expressões coloquiais apropriadas
- Considerar a diversidade cultural do mundo lusófono
- Incluir referências à música, natureza e hospitalidade`,
    exampleTypes: [
      'Música e cultura popular',
      'Natureza e biodiversidade',
      'Futebol e esportes',
      'Hospitalidade e família'
    ],
    communicationStyle: 'Caloroso, acolhedor e amigável, com o jeitinho brasileiro/português',
    commonAnalogies: [
      'Fazer feijoada - ingredientes e paciência',
      'Carnaval - criatividade e alegria',
      'Praia e mar - fluidez e naturalidade',
      'Roda de samba - harmonia e improviso'
    ]
  },
  
  'ru': {
    name: 'Russian',
    nativeName: 'Русский',
    culturalInstructions: `
### Специальные инструкции для русскоязычных пользователей:
- Использовать знакомые русские культурные отсылки и поговорки
- Включать примеры из российской повседневной жизни
- Поддерживать теплый, но уважительный тон
- Использовать подходящие идиоматические выражения
- Учитывать российские образовательные традиции
- Включать отсылки к литературе, истории и природе`,
    exampleTypes: [
      'Русская литература и история',
      'Природа и времена года',
      'Семейные традиции',
      'Образование и наука'
    ],
    communicationStyle: 'Теплый, образованный и вдумчивый, с русской душевностью',
    commonAnalogies: [
      'Русская зима - терпение и подготовка',
      'Самовар - традиция и общение',
      'Березовая роща - красота и родина',
      'Шахматы - стратегия и мышление'
    ]
  }
}

/**
 * 获取语言特定的文化适配指令
 * @param language - 目标语言
 * @returns string
 */
export function getLanguageAdaptation(language: Language): string {
  const adaptation = LANGUAGE_ADAPTATIONS[language]
  if (!adaptation) {
    return LANGUAGE_ADAPTATIONS['en'].culturalInstructions
  }
  
  return adaptation.culturalInstructions
}

/**
 * 获取语言特定的类比建议
 * @param language - 目标语言
 * @returns string[]
 */
export function getLanguageAnalogies(language: Language): string[] {
  const adaptation = LANGUAGE_ADAPTATIONS[language]
  if (!adaptation) {
    return LANGUAGE_ADAPTATIONS['en'].commonAnalogies
  }
  
  return adaptation.commonAnalogies
}

/**
 * 获取语言特定的沟通风格指导
 * @param language - 目标语言
 * @returns string
 */
export function getLanguageCommunicationStyle(language: Language): string {
  const adaptation = LANGUAGE_ADAPTATIONS[language]
  if (!adaptation) {
    return LANGUAGE_ADAPTATIONS['en'].communicationStyle
  }
  
  return adaptation.communicationStyle
}

/**
 * 构建语言特定的增强提示词
 * @param language - 目标语言
 * @param includeAnalogies - 是否包含类比建议
 * @returns string
 */
export function buildLanguageEnhancement(
  language: Language, 
  includeAnalogies: boolean = true
): string {
  const adaptation = LANGUAGE_ADAPTATIONS[language] || LANGUAGE_ADAPTATIONS['en']
  
  let enhancement = adaptation.culturalInstructions
  
  if (includeAnalogies && adaptation.commonAnalogies.length > 0) {
    enhancement += `\n\n### 推荐的类比方式:
${adaptation.commonAnalogies.map(analogy => `- ${analogy}`).join('\n')}`
  }
  
  enhancement += `\n\n### 沟通风格要求:
${adaptation.communicationStyle}`
  
  return enhancement
}
