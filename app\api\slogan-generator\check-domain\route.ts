import { NextRequest, NextResponse } from 'next/server'
import { promises as dns } from 'dns'

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const { domain } = body

    // 输入验证
    if (!domain?.trim()) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      )
    }

    const cleanDomain = domain.trim().toLowerCase()
    
    // 验证域名格式
    const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/
    if (!domainRegex.test(cleanDomain)) {
      return NextResponse.json(
        { error: 'Invalid domain format' },
        { status: 400 }
      )
    }

    // 检查域名可用性
    const isAvailable = await checkDomainAvailability(cleanDomain)

    return NextResponse.json({
      domain: cleanDomain,
      available: isAvailable,
      checkedAt: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to check domain availability' },
      { status: 500 }
    )
  }
}

// 检查域名可用性的函数
async function checkDomainAvailability(domain: string): Promise<boolean> {
  try {
    // 尝试解析 .com 域名
    const fullDomain = `${domain}.com`
    
    // 使用DNS查询检查域名是否已注册
    await dns.resolve(fullDomain, 'A')
    
    // 如果能解析到A记录，说明域名已被注册
    return false
    
  } catch (error: any) {
    // 如果DNS查询失败，可能是域名未注册
    if (error.code === 'ENOTFOUND' || error.code === 'ENODATA') {
      // 进一步检查是否有其他记录类型
      try {
        await dns.resolve(`${domain}.com`, 'CNAME')
        return false // 有CNAME记录，域名已注册
      } catch (cnameError: any) {
        if (cnameError.code === 'ENOTFOUND' || cnameError.code === 'ENODATA') {
          try {
            await dns.resolve(`${domain}.com`, 'MX')
            return false // 有MX记录，域名已注册
          } catch (mxError: any) {
            if (mxError.code === 'ENOTFOUND' || mxError.code === 'ENODATA') {
              // 没有找到任何DNS记录，可能可用
              return true
            }
            return false
          }
        }
        return false
      }
    }
    
    // 其他错误情况，保守地认为不可用
    return false
  }
}
