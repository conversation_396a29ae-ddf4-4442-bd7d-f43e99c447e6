import { NextResponse } from 'next/server'
import { auth, createClerkClient } from '@clerk/nextjs/server'
import { creem } from '@/lib/creem-client'
import { getUserMetadata } from '@/lib/user-management'

const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY!
})

export async function POST() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 检查必要的环境变量

    if (!process.env.CREEM_API_KEY) {
      console.error('Missing CREEM_API_KEY environment variable')
      return NextResponse.json(
        { error: 'Payment service not configured' },
        { status: 500 }
      )
    }

    if (!process.env.CREEM_PRODUCT_ID) {
      console.error('Missing CREEM_PRODUCT_ID environment variable')
      return NextResponse.json(
        { error: 'Product not configured' },
        { status: 500 }
      )
    }

    if (!process.env.NEXT_PUBLIC_APP_URL) {
      console.error('Missing NEXT_PUBLIC_APP_URL environment variable')
      return NextResponse.json(
        { error: 'App URL not configured' },
        { status: 500 }
      )
    }

    // 获取用户信息
    const userMetadata = await getUserMetadata(userId)

    // 获取用户的 Clerk 信息
    const user = await clerkClient.users.getUser(userId)

    if (!user.emailAddresses[0]?.emailAddress) {
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      )
    }

    const userEmail = user.emailAddresses[0].emailAddress

    // 创建checkout session
    const checkoutSession = await creem.createCheckoutSession({
      product_id: process.env.CREEM_PRODUCT_ID,
      customer: {
        email: userEmail  // 只使用 email，不使用 id
      },
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
      metadata: {
        userId,
        plan: 'premium',
        upgradeFrom: userMetadata.plan
      }
    })

    return NextResponse.json({
      success: true,
      checkout_url: checkoutSession.checkout_url,
      session_id: checkoutSession.id
    })

  } catch (error) {
    console.error('Create checkout error:', error)

    // 提供更详细的错误信息
    let errorMessage = 'Failed to create checkout session'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
