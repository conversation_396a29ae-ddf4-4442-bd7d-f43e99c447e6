import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { checkAndDowngradeExpiredUser } from '@/lib/user-management'

export async function GET() {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // 检查并降级过期用户，然后获取最新数据
    const metadata = await checkAndDowngradeExpiredUser(userId)
    
    return NextResponse.json({
      plan: metadata.plan,
      credits: metadata.credits,
      usedCredits: metadata.usedCredits,
      totalCreditsEarned: metadata.totalCreditsEarned || 0,
      subscriptionPeriodEnd: metadata.subscriptionPeriodEnd
    })

  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get user stats' },
      { status: 500 }
    )
  }
}
